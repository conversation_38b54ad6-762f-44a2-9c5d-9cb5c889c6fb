{"name": "@trunk/mapsets", "private": false, "version": "1.1.8-beta.5", "type": "module", "author": "TrunkFE", "license": "MIT", "main": "./dist/mapsets.cjs", "module": "./dist/mapsets.js", "typings": "./dist/index.d.ts", "exports": {".": {"require": "./dist/mapsets.cjs", "import": "./dist/mapsets.js"}, "./style": "./dist/style.css"}, "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"dev": "vite", "build": "vite build", "build:mapsets": "vite build --mode mapsets", "preview": "vite preview", "scp": "vite build && scp -r dist infra@*************:~/mapsets", "protobuf": "mkdir -p public/protobuf && cd public/protobuf/ && git clone -b dev http://git-rd.trunk.tech/infra/trunk_core/trunk_msgs.git && cd trunk_msgs/src && cp -R . ../../ && cd ../../  && rm -rf trunk_msgs  && rm -rf backward", "downloadMap": "cd public/geojson &&  python3 download_hdmap.py && cd ../..", "fitpb": "mkdir -p public/protobuf && cd public/protobuf/ && rm -rf trunk_protocol && rm -rf trunk && <NAME_EMAIL>:trunkport/trunk_protocol.git && mv -f trunk_protocol/trunk . &&  rm -rf trunk_protocol"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "dependencies": {"@ant-design/icons": "^5.0.0", "@deck.gl-community/editable-layers": "^9.0.3", "@deck.gl-community/layers": "^9.0.3", "@mapbox/mapbox-gl-language": "^1.0.1", "@react-three/drei": "^9.57.3", "@react-three/fiber": "^8.12.0", "@reduxjs/toolkit": "^1.8.5", "@turf/turf": "^6.5.0", "@types/i18next": "^13.0.0", "@types/lodash": "^4.14.184", "@types/react-i18next": "^8.1.0", "@types/three": "^0.149.0", "@vitejs/plugin-react": "^4.2.1", "antd": "^5.2.1", "axios": "^1.3.0", "deck.gl": "^9.0.38", "dexie": "^3.2.3", "echarts": "^5.4.0", "i18next": "^25.3.2", "less": "^4.1.3", "lil-gui": "^0.18.1", "lodash": "^4.17.21", "mapbox-gl": "^2.10.0", "maplibre-gl": "^4.7.1", "postcss-px-to-viewport": "^1.1.1", "protobufjs": "^7.1.6", "react-draggable": "^4.4.6", "react-grid-layout": "^1.3.4", "react-i18next": "^15.6.1", "react-json-view": "^1.21.3", "react-map-gl": "^7.1.7", "react-markdown": "^8.0.5", "react-redux": "^8.0.2", "react-router-dom": "^7.1.1", "reconnecting-websocket": "^4.4.0", "require": "^2.4.20", "roslib": "^1.3.0", "three": "^0.150.1", "vconsole": "^3.15.1"}, "devDependencies": {"@rollup/plugin-typescript": "^11.1.6", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@types/roslib": "^1.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^4.6.4", "vite": "^5.1.4", "vite-plugin-libcss": "^1.1.1"}}