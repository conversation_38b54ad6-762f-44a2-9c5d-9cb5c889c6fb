[[[-95.364041, 29.756421], [-95.361471, 29.754867], [-95.360633, 29.754379], [-95.361213, 29.753626], [-95.362074, 29.75412], [-95.363759, 29.755176], [-95.364355, 29.754418], [-95.366974, 29.75601], [-95.364622, 29.758971], [-95.363733, 29.758421], [-95.362881, 29.757926]], [[-95.362849, 29.754704], [-95.361736, 29.756131], [-95.360893, 29.755613], [-95.361472, 29.754875], [-95.362082, 29.754121], [-95.362646, 29.753394], [-95.363498, 29.753894], [-95.362908, 29.754629]], [[-95.362674, 29.75493], [-95.361736, 29.756131], [-95.360893, 29.755613], [-95.361472, 29.754875], [-95.362082, 29.754121], [-95.362646, 29.753394], [-95.363498, 29.753894]], [[-95.363177, 29.755911], [-95.362588, 29.756656], [-95.364287, 29.75766], [-95.364862, 29.756939], [-95.365483, 29.756174], [-95.36464, 29.755656], [-95.363757, 29.755162]], [[-95.362588, 29.756656], [-95.364287, 29.75766], [-95.364862, 29.756939], [-95.365483, 29.756174], [-95.36464, 29.755656], [-95.363757, 29.755162], [-95.363177, 29.755911]], [[-95.36666, 29.754684], [-95.366058, 29.755443], [-95.366172, 29.755509], [-95.366763, 29.754746]], [[-95.365637, 29.756273], [-95.367251, 29.757255], [-95.367834, 29.756522], [-95.367405, 29.756257], [-95.367834, 29.756522], [-95.367251, 29.757255]]]