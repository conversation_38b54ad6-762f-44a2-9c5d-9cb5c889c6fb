syntax = "proto3";
package trunk.msgs;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "geometry.proto";

message PointField {
    enum Type {
        INT8 = 0;
        UINT8 = 1;
        INT16 = 2;
        UINT16 = 3;
        INT32 = 4;
        UINT32 = 5;
        FLOAT32 = 6;
        FLOAT64 = 7;
    }

    string name = 1;    // Name of field
    uint32 offset = 2;  // Offset from start of point struct
    Type   type = 3;    // Datatype enumeration, see above
    uint32 count = 4;   // How many elements in the field
}

message PointCloud2 {
    google.protobuf.Timestamp timestamp = 1;
    uint32                    height = 2;
    uint32                    width = 3;
    repeated PointField point_field = 4;
    bool                is_big_endian = 5;
    uint32              point_step = 6;
    uint32              row_step = 7;
    bytes               data = 8;
    bool                is_dense = 9;
    Transform           tf = 10;
    string              frame_id = 11;
}

message LidarDiagnose {
    google.protobuf.Timestamp timestamp = 1;
    repeated string lidar_name = 2;
    repeated bool need_calibrate_flag = 3;
}

message LidarTimestamp {
  string lidar_name = 1;
  string install_position = 2;
  google.protobuf.Timestamp timestamp = 3;
}

message LidarTimestamps {
  repeated LidarTimestamp lidar_timestamps = 1;
}

service LidarService {
  rpc getLidar(google.protobuf.Empty) returns (PointCloud2) {}
  rpc getLidarTimestamps(google.protobuf.Empty) returns (LidarTimestamps) {}
  rpc getLidarDiagnose(google.protobuf.Empty) returns (LidarDiagnose) {}
}
