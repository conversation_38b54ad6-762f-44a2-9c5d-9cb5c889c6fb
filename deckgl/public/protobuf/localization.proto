syntax = "proto3";
package trunk.msgs;

import "geometry.proto";
import "eigen.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

message Localization {
  google.protobuf.Timestamp timestamp = 1;
  Pose pose = 2;
  Twist velocity = 3;
}

message IntensityThreshold {
  float intensity_threshold = 1;
}

message SwiftDatumState {
  repeated double data = 1;
}

message Odometry {
  Header header = 1;
  string child_frame_id = 2;
  PoseWithCovariance pose = 3;
  TwistWithCovariance twist = 4;
}

message SimpleCloud {
  message Point {
    float x = 1;
    float y = 2;
    float z = 3;
    float intensity = 4;
  }
  google.protobuf.Timestamp timestamp = 1;
  repeated Point points = 2;
}

message SimpleCloudArray {
  repeated SimpleCloud simple_cloud = 1;
}

message VoxelState {
  message State {
    MatrixXd vector3d_mean = 1;
    MatrixXd matrix3d_icov = 2;
  }
  State state = 1;
  MatrixXi vector3i_key = 2;
  uint32 points_num = 3;
}

message ChunkState {
  repeated VoxelState voxel_state = 1;
  MatrixXi vector2i_key = 2;
  bool replace_all_voxels_flag = 3;
}

message TargetMap {
  repeated ChunkState chunk_state = 1;
  repeated MatrixXi vector2i_key = 2;
}

service LocalizationService {
  rpc getLocalization(google.protobuf.Empty) returns (Localization) {}
  rpc getIntensityThreshold(google.protobuf.Empty) returns (IntensityThreshold) {}
  rpc getSwiftDatumState(google.protobuf.Empty) returns (SwiftDatumState) {}
  rpc getOdometry(google.protobuf.Empty) returns (Odometry) {}
  rpc setPointCloud(SimpleCloudArray) returns (google.protobuf.Empty) {}
  rpc getTargetMap(Pose) returns (TargetMap) {}
}
