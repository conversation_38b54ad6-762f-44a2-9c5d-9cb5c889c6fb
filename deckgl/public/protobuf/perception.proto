syntax = "proto3";
package trunk.msgs;

import "geometry.proto";
import "google/protobuf/empty.proto";

// 动态障碍物包络数据
message DynamicObstacle {
  // Truck_Head = 1
  // Truck_Full_Trailer = 2
  // Truck_No_Trailer = 3
  // TRUCK = 7
  // CAR = 8
  // PEDESTRIAN = 9
  // LSHAPE = 10
  // Empty_Trailer = 11
  // OTHERS = 12
  int32 type = 1;                       // 语义标签
  repeated Point3D vertex = 2;          // 2D包络点
  Pose center = 3;                      // 计算出来的中心点
  double min_z = 4;                     // 下底
  double max_z = 5;                     // 上底
  Vector3 speed = 6;                    // Box速度
  int32 id = 7;
}

// 动态障碍物
message DynamicObstacles {
  repeated DynamicObstacle objs = 1;    // 存储2D+上下地动态对象
}

// 静态障碍物
message StaticObstacle {
  int32 type = 1;                       // 点云类型
  repeated Point3D points = 2;          // 点云数据
}

// 静态障碍物点
message StaticObstacles {
  repeated StaticObstacle objs = 1;     // 存储静态点云
}

service PerceptionService {
  rpc getDynamicObstacles(google.protobuf.Empty) returns (DynamicObstacles) {}
  rpc getStaticObstacles(google.protobuf.Empty) returns (StaticObstacles) {}
}
