syntax = "proto3";
package trunk.msgs;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

message ControlCmd {
  // 纵向控制状态, 位置环, 速度环, 力矩环等
  uint32 longitu_control_mode = 1;
  // 横向控制状态, 半八, 全八, 斜形等
  uint32 lateral_control_mode = 2;
  // 自动挡挡位
  uint32 gear_a = 3;
  // 手动挡挡位
  uint32 gear_m = 4;
  // 电子手刹
  uint32 epb = 5;
  // 雨刷
  uint32 wiper = 6;
  // 转向灯
  uint32 turn_light = 7;
  // 远近光灯
  uint32 light = 8;
  // 喇叭
  uint32 horn = 9;
  // 油门开度, 0~1
  double throttle = 10;
  // 刹车开度, 0~1
  double brake = 11;
  // 方向盘转角, 0~1
  double steering_angle = 12;
  // 移动距离控制
  double distance = 13;
  // 速度控制
  double speed_target = 14;
  // 预测的转向角序列
  repeated double steering_angles = 15;
  // time stamp
  google.protobuf.Timestamp time_stamp = 16;
}

service ControlCmdService {
  rpc getControlCmd(google.protobuf.Empty) returns (ControlCmd) {}
}
