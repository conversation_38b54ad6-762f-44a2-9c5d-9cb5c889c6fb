syntax = "proto3";
package trunk.msgs;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

// 1: planning success 2: planning fail 3: execute success,arrived  4: execute fail 
message ADSStayResponse{
  google.protobuf.Timestamp timestamp = 1;
  uint32 id = 2;
  uint32 response = 3;
}

service ADSStayResponseService {
  rpc GetADSStayResponse(google.protobuf.Empty) returns (ADSStayResponse) {}
}
