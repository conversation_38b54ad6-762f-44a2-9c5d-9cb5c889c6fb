syntax = "proto3";
package trunk.proto.fit;

import "google/protobuf/timestamp.proto";
import "trunk/proto/fit/vehicle.proto";
import "trunk/proto/fit/task.proto";
import "trunk/proto/fit/navi.proto";
import "trunk/proto/fit/statistics.proto";
import "trunk/proto/fit/enums.proto";
import "trunk/proto/data/frame.proto";

message MonitorOne {
  VehicleBase veh_base = 1;
  data.Frame frame = 2;
  VehicleStatus veh_status = 3;
  TaskInfo task_info = 4;
  Navi navi = 5;
  Statistics statistics = 6;
  google.protobuf.Timestamp timestamp = 7;  // 时间戳
}

message TaskInfo {
  Task current_task = 1;
  Task next_task = 2;
  Task prev_task = 3;
}


message StopInfo{
  google.protobuf.Timestamp created = 1;  //  创建时间
  string sender = 2;  // 发送方
  StopType type = 3;  // 停车类型
  string reason = 4;  // 发送原因
}

message LongStopAlarm {
  google.protobuf.Timestamp created = 1;  //  创建时间
  int32 create_timestamp = 2;
  int32 update_timestamp = 3;
  ColorType color = 4;
}

message VehicleBase {
  string che_id = 1;
  string ip = 2;
  string port = 3;
  SourceType source_type = 4;
  PoolType pool_type = 5;
}

message VehicleStatus {
  google.protobuf.Timestamp timestamp = 1;  // 时间戳
  StateFlow state_flow = 2;   // tpa状态机
  OperationalStatus operational_status = 3;  // 机械操作状态，driving_mode
  repeated StopInfo stop_infos = 4; // 停车信息数组
  LongStopAlarm long_stop_alarm = 5; // 警告信息
}
