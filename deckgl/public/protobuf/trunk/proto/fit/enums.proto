syntax = "proto3";
package trunk.proto.fit;

//  车辆进入方向
enum WorkCycleDirection {
  WORK_CYCLE_DIRECTION_DEFAULT = 0;
  CLOCKWISE = 1;       // 顺时针
  ANTI_CLOCKWISE = 2;  // 逆时针
}

// 终点类型
enum DestType {
  DEST_TYPE_DEFAULT = 0;
  YCTP = 1;       // yard transfer point
  PSTP = 2;       // pin station
  PB = 3;         // parallel buffer, 锁站下面的主干道
  PSTP_HPB = 4;   // HPB of pin station
  PSTP_QPB = 5;   // QPB of pin station
  QCTP = 6;       // quay crane transfer point
  QCTP_1 = 7;     // qctp - 1
  QCTP_2 = 8;     // qctp - 2
  QCTP_3 = 9;     // qctp - 3
  QCTP_4 = 10;    // qctp - 4
  QCTP_5 = 11;    // qctp - 5
  PCTP = 12;      // park space
  CHARGETP = 13;  // charging space
  XRAYTP = 14;    // 海关检测区域
  VPB = 15;       // 上下岸桥PB
  ST = 16;        // 疏停区
  MT = 17;        // 密停区
  LT = 18;        // 立体车库
}  // 终点类型,地图用

// waypoints模式
enum WayPointsMode {
  WAYPOINT_MODE_DEFAULT = 0;
  WPM_NONE = 1;             // 单车自主规划
  TRAJECTORY = 2;           // 全轨迹
  SECTION = 3;              // 全区间,邻接矩阵,车道级导航
  TURN_HAS_TRAJECTORY = 4;  // 弯路有轨迹,直线无轨迹
  TURN_NO_TRAJECTORY = 5;   // 弯路无轨迹
  OPEN_SPACE = 6;           // 倒车
}

// 控制点类型
enum PointType {
  POINT_TYPE_DEFAULT = 0;
  STRAIGHT = 1;                        // 直行
  TURN = 2;                            // 转弯
  LANE_CHANGE_NORMAL = 3;              // 楔形
  LANE_CHANGE_ENTER_WORKING_LANE = 4;  // 斜行进入作业车道
  LANE_CHANGE_ENTER_STATION = 5;       // 斜行进入锁站
  LANE_CHANGE_ENTER_CHARGE_LANE = 6;   // 斜行进入充电桩
  LANE_CHANGE_OUT_WORKING_LANE = 7;    // 斜行出作业车道
  LANE_CHANGE_OUT_CHARGE_LANE = 8;     // 斜行出充电桩
  START_CHANGE = 9;  // 开始换道点(智港通)
  END_CHANGE = 10;  // 结束换道点(智港通)
  UP_DETOUR = 11;  // 上岸桥绕行
  DOWN_DETOUR = 12;  // 下岸桥绕行
  TRAFFIC_STOP = 13; // 交通流停止点
  KEEP = 14; // 该类型点在车身后也需要保留
  UP_DETOUR_1 = 15;  // 上岸桥绕行, 宁波经过后大梁8车道
  DOWN_DETOUR_1 = 16;
}

// 行车方向，前进或后退
enum DriveDirection {
  DRIVE_DIRECTION_DEFAULT = 0;
  FORWARD = 1;   // 前进,正向
  BACKWARD = 2;  // 后退，反向
}

// 安全距离等级
enum SafeDistanceLevel {
  SAFE_DIS_LEVEL_DEFAULT = 0;
  FAR = 1;         // 相对远
  NEAR = 2;        // 比较近
  CLOSE = 3;       // 特别近，密停
  IN_STATION = 4;  // 锁站密停
}

//任务状态
enum OrderStatus {
  ORDER_STATUS_DEFAULT = 0;
  ENTERED = 1;    // 创建状态,收到指令的状态
  EXECUTING = 2;  // 工作状态
  COMPLETED = 3;  // 完成状态
  REJECTED = 4;   // 拒绝状态
  CANCELED = 5;   // 取消状态
  ABORTED = 6;    // 中止状态
  UPDATED = 7;    // 更新状态
}

// 车辆指令类型
enum OrderType {
  ORDER_TYPE_DEFAULT = 0;
  MOVE = 1;         // 指定目的地移动
  DELIVER = 2;      // 送箱
  RECEIVE = 3;      // 收箱
  CHARGE = 4;       // 充电
  MAINTENANCE = 5;  // 维保
  PARK = 6;         // 就近停车区停
}

// 业务类型
enum BusinessType {
  BUSINESS_TYPE_DEFAULT = 0;
  LOAD = 1;         // 装船
  DSCH = 2;         // 卸船
  YARD_MOVE = 3;    // 移箱
  MANUAL_MOVE = 4;  // 人工指令移动
  BT_PARK = 5;      // 就近停车区停车
  BT_CHARGE = 6;    // 充电
  XRAY = 7;         //海关检测
}

// 箱在拖板上的位置
enum ContainerPos{
  CONTAINER_POS_DEFAULT = 0;
  CON_POS_FRONT = 1; // 前
  CON_POS_MID = 2; // 中
  CON_POS_REAR = 3; // 后
}

// fms下发给tpa的任务类型
enum TaskType {
  TASK_TYPE_DEFAULT = 0;
  EMPTY_TO_QC = 1;           // 空载前往岸桥 QC: quay crane
  FULL_TO_QC = 2;            // 满载前往岸桥
  EMPTY_TO_YC = 3;           // 空载前往堆场 YC: yard crane
  FULL_TO_YC = 4;            // 满载前往堆场
  EMPTY_TO_ECY = 5;          // 空载前往空箱厂 ECY: empty container yard
  FULL_TO_ECY = 6;           // 满载前往空箱厂
  TO_BUFFER = 7;             // 前往缓冲区
  TO_LOCK_AREA = 8;          // 前往自动锁区
  EMPTY_TO_INTERACTION = 9;  // 空载前往人工交互区，如 人工锁站
  FULL_TO_INTERACTION = 10;  // 满载前往人工交互区， 如 人工锁站
  TRUCK_POWER = 11;          // 前往自动充电区
  PICKUP_CAR = 12;
  RELEASE_CAR = 13;
}

// TPA任务模式
enum TaskMode {
  TASK_MODE_DEFAULT = 0;
  AUTO = 1;             // task from fms auto mode
  GUI = 2;              // task from gui
  ACTIVE_GUIDE = 3;     // 主动引导
  PASSIVE_GUIDE = 4;    // 被动引导
  GUI_MAINTENANCE = 5;  // 远程维保
  AUTOSTANDBY = 6;      // 休眠模式
}

// 引导类型
enum GuideType {
  GUIDE_TYPE_DEFAULT = 0;
  COLUMN = 1;  // 反光柱
  GIRDER = 2;  // 大梁
  TAG = 3;     // tag板
}

enum BypassSequence {
  BYPASS_SEQUENCE_DEFAULT = 0;  //
  FREESTYLE =
      1;  // 自由装船配置，在该模式下，任何任务车辆满足条件即可引导装船。
  SELECTIVE =
      2;  // 灵活装船配置，在该模式下，同组任务可以实现灵活装船顺序，不同组任务应严格按照既定顺序执行。
  STRICT =
      3;  // 严格装船的车序管理功能，即在该模式下车辆到达岸桥顺序应当严格按照既定顺序到达。
}

enum WaitPoint {
  WAIT_POINT_DEFAULT = 0;  // 默认值
  WAIT_POINT_FRONT = 1;    // 前等待位
  WAIT_POINT_WORK = 2;     // 作业位
  WAIT_POINT_REAR = 3;     // 后等待位
}

enum TwinFlag {
  TWIN_FLAG_DEFAULT = 0;  // 默认值
  TWIN_FLAG_BIND = 1;     // 车辆箱绑定双箱
  TWIN_FLAG_UNBIND = 2;   // 车辆箱未绑双箱
  ALONE_FLAG = 3;         // 无双箱
}

enum StateFlow{
  IDLE = 0;
  EXECUTE_RUNNING = 1;
  EXECUTE_PNDARRIVED = 2;
  EXECUTE_AUTOALIGNING = 3;
  EXECUTE_ARRIVED = 4;
  STOPPING = 5;
  STOP_STOPPED = 6;
  ALIGN_ALIGNING = 7;
  ALIGN_ALIGNED = 8;
  WORKING = 9;
  SLEEP = 10;
  CHARGER_CHARGING = 11;
  CHARGER_CLOSEHV = 12;
  READY_TO_PICKUP = 13;  // 准备取车/到达取车准备点
  PICKUP_OR_RELEASE_COMPLETED = 14;  // 取或放车完成
  STATE_FLOW_OFFLINE = 99;
}

// 对位类型
enum InpositionType{
  IPT_DEFAULT = 0;
  LEAVE_SAFELY = 1 ;// 0 吊具已经上升到安全高度
  // 通知车可以驶离，
  // 这个时候FMS要通知车需要解锁，
  // 然后完成任务。
  MAKE_LOCK = 2;  // 让车锁车
  MAKE_ALIGNMENT = 3;  // 让车对位
  CPS_INVALID = 4;  // 无法定位到集卡拖板。就是CPS失效，扫不到所以就说不能就位
  CRANE_UNSEATED = 5;  // 起重机未到位。
}

enum WorkState{
  WORK_STATE_DEFAULT = 0; // 默认值
  WORK_IDLE = 1;  // 空闲,没有task
  WORK_WAIT_NAVI = 2;  // 无导航,等待导航
  WORK_RUNNING = 3;  // 行驶中
  WORK_ARRIVED = 4;  // 到达
  WORK_ALIGNING = 5;  // 对位
  WORK_LOCKED = 6;  // 锁车
}

enum StopType{
  DEFAULT = 0;  // 默认
  SLOW_STOP = 1; // 缓停
  EMER_STOP = 2; // 急停
  FMS_SLOW_STOP = 3; // 管控停车, 缓停
}

// 锁站相关
enum PinsSwitchType{
  PINS_SWITCH_TYPE_DEFAULT = 0;
  PINS_SWITCH_TYPE_ESTOP = 1;  // 急停开关
  PINS_SWITCH_TYPE_LEAVE = 2;  // 装卸锁完成或恢复行驶开关
}

enum ButtonStatus{
  DEFAULT_BUTTON_STATUS = 0;
  OFF = 1;
  ON = 2;
}

enum SourceType{
  SOURCE_TYPE_DEFAULT = 0;
  SOURCE_TRUNK = 1;
  SOURCE_EXTERNAL = 2;
}

enum ColorType{
  COLOR_WHITE = 0;
  COLOR_RED = 1;
  COLOR_GREEN = 2;
}

//机械操作状态
enum OperationalStatus{
  OPERATION_STATUS_DEFAULT = 0;
  AUTOMATIC                = 1; //  自动驾驶模式
  MANUAL                   = 2; //  人工干预状态，手动模式
}

enum PoolType {
  DEFAULT_POOL = 0;
  TRUNK_POOL = 1;
  TOS_POOL = 2;
}
