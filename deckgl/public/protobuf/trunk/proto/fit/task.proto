syntax = "proto3";
package trunk.proto.fit;

import "trunk/proto/common/geometry.proto";
import "trunk/proto/fit/cargo.proto";
import "trunk/proto/fit/container.proto";
import "trunk/proto/fit/enums.proto";
import "trunk/proto/fit/align.proto";

import "google/protobuf/timestamp.proto";

message TaskEvent {
  string desc = 1;
  string time = 2;
}

message TaskFlow {
  TaskEvent start = 1;
  TaskEvent arrived = 2;
  TaskEvent aligned = 3;
  TaskEvent locked = 4;
  TaskEvent end = 5;
}

message Task {
  google.protobuf.Timestamp updated = 1;  // 更新时间，本条数据产生或更新时间
  string che_id = 2;                      // 车辆id
  string trace_id = 3;  // tosc在收到外部tos的task时生成,用于做链路追踪
  uint32 order_version = 4;  // 每当ACT_ORDERS表有更新时,该字段都会加1
                             // 缺省为1 task中的task_id对应外部tos的task ID
  string task_id = 5;  // 指令主键,不可重复,由数据库自动生成
  uint32 task_version = 6;  // 每当ACT_TASKS表有更新时,该字段都会加1,缺省为1
  OrderStatus status = 7;  // Task指令状态,当是Entered时,TPA需要做对应处理
                           // 否则只是转发给数据采集即可，用于公司监控
  repeated Container containers = 8; // Task箱子信息
  OrderType act_order_type = 9;  // ORDER类型，指明车辆行为，如收送箱
  BusinessType act_business_type = 10;  // 业务类型
  DestType dest_type = 11;              //  TASK目标位置类型
  string plan_destination =
      12;  // FMS派遣AGV到达的目的地, 一般与
           // ACT_ORDERS表中的PLANNED_DESTINATION值相符
           // 除非岸桥作业时TOS没有指定岸桥车道,需要FMS设定
  string actual_destination = 13;  // fms分配的实际目的地
  common.Point4D loc_point = 14;   // 任务开始时车辆的坐标
  common.Point4D dest_point = 15;  // 目的地坐标
  TaskType task_type = 16;         // task任务类型,TPA用
  TaskMode task_mode = 17;         // 任务模式，区分是fms还是gui指令
  bool guide_stop = 18;            // 是否引导停车
  GuideType guide_type = 19;       // 引导停车类型，反光柱、大梁等
  string target = 20;  // 目的地,桥号、场地号、充电桩号、锁站号等
  string sub_target = 21;  // 目的地贝位或道路号
  bool active_cps = 22;    // 是否要自主对位
  GuideType active_cps_type = 23;  // 自主对位类型，大梁或反光柱?为什么要搞两个
  BypassSequence bypass_sequence = 24;  // 排队作业顺序规则
  string problem_code = 25;  // 问题代码，当task状态异常时，有该值
  string problem_description = 26;          // task问题描述
  WorkCycleDirection route_direction = 27;  // 顺逆方向
  string up_vpb = 28;                       // 上岸桥经过的引桥号
  string down_vpb = 29;                     // 下岸桥经过的引桥号
  bool guide_cps = 30;  // 是否有外部的cps进行引导对位，比如ABB，有的话为True
  string crane_id = 31;   // 桥号
  bool has_navi = 32;     // tosc 是否会下发 navi
  bool need_manual = 33;  // 是否需要人工做指令，tosc指定该字段
  string tos_id = 34;     // 关联一整趟循环作业任务
  WaitPoint wait_point = 35;  // 指令作业点位目前对应 堆高机等待位
  TwinFlag twin_flag = 36;    // 双箱标记
  Cargo cargo_info = 37;      // 商品车信息
  common.Point4D narrow_point = 38;  // 狭窄空间坐标
  Align align = 39;
  TaskFlow task_flow = 40;     // 任务事件流, 低
  int32 task_status = 42;      // 任务接收状态
  int32 align_status = 43;     // 对位接收状态
  string task_status_desc = 44;     // 任务状态描述
  repeated Container con_show = 45; // UI模型展示的箱体信息
  string pow = 46;  //  作业点位，point of work，航运作业时对应岸桥号
  uint32 vbay = 47; //  航运作业时，箱子在船上的bay位号
  uint32 seq = 48;  //  装船作业时的顺序号，号码越小优先级越高
  string vessel_id = 49; // 船期ID
  bool need_ps = 50; // 是否需要去锁站
}
