syntax = "proto3";
package trunk.proto.fit;

import "trunk/proto/fit/enums.proto";

import "google/protobuf/timestamp.proto";

message Align{
  google.protobuf.Timestamp created = 1;  //  收到该对位指令的最初始时间
  string task_id  = 2;   // 关联的Task_id，fms下发给TPA时赋值
  string crane_id = 3; // 发出引导信息的设备号，如桥号、锁站号等
  float offset    = 4;     // 单车需要执行的偏移量，m
  InpositionType inposition_type = 5;  // 对位类型
  float cps_offset          = 6; // cps引导值, m
  DestType dest_type = 7; // 表示发出引导信息的设备类型
}