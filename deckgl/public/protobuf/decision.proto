syntax = "proto3";
package trunk.msgs;

import "google/protobuf/timestamp.proto";
import "geometry.proto";

message DecisionCmd {
  google.protobuf.Timestamp time_stamp = 1;
  string scenario_name = 2;
  string stage_name = 3;
  uint32 decision_lat_state = 4;
  uint32 decision_lon_state = 5;
  repeated Point2D left_boundary = 6;
  repeated Point2D right_boundary = 7;
  repeated Point3D reference_line = 8;
}

// highway scenario_name
// string INLANE_SCENARIO         = "inlane"
// string INTORAMP_SCENARIO       = "ramp"
// string MERGE_SCENARIO          = "merge"

// port scenario_name
// string BRIDGE_SCENARIO         = "bridge"
// string JUNCTION_SCENARIO       = "junction"
// string YARD_SCENARIO           = "yard"

// highway stage_name
// string LANEFOLLOW_STAGE        = "lanefollow"
// string PRE_LANECHANGE_STAGE    = "prelanechange"
// string POST_LANECHANGE_STAGE   = "postlanechange"

// string PRE_INTORAMP_STAGE      = "preintoramp"
// string RAMP_STAGE              = "ramp"

// string MERGE_STAGE             = "merge"
// string POST_MERGE_STAGE        = "postmerge"

// port stage_name
// string PRE_BRIDGE_WORK_STAGE   = "prebridgework"
// string BRIDGE_WORK_STAGE       = "bridgework"
// string POST_BRIDGE_WORK_STAGE  = "postbridgework"

// string FOLLOW_STAGE            = "follow"

// string PRE_PASS_STAGE          = "prepass"
// string POST_PASS_STAGE         = "postpass"
// 
// string PRE_YARD_WORK_STAGE     = "preyardwork"
// string POST_YARD_WORK_STAGE    = "postyardwork"

// decision_lat_state
// uint8 FOLLOW                = 0
// uint8 LEFT_LANE_CHANGE      = 1
// uint8 RIGHT_LANE_CHANGE     = 2
// uint8 CANCEL_LANE_CHANGE    = 3
// uint8 LEFT_NUDGE            = 4
// uint8 RIGHT_NUDGE           = 5
// uint8 UNKNOWN               = 6
// 
// ## decision_lon_state
// uint8 KEEP                  = 0
// uint8 OVERTAKE              = 1
// uint8 YIELD                 = 2
// uint8 STOP                  = 3
// uint8 E_STOP                = 4
