syntax = "proto3";
package trunk.msgs;

// Livox costom pointcloud format.

message CustomPoint {
    uint32 offset_time = 1;     // offset time relative to the base time
    float x = 2;                // X axis, unit:m
    float y = 3;                // Y axis, unit:m
    float z = 4;                // Z axis, unit:m
    uint32 reflectivity = 5;    // reflectivity, 0~255
    uint32 tag = 6;             // livox tag
    uint32 line = 7;            // laser number in lidar
}
