syntax = "proto3";
option cc_generic_services = true;
package trunk.msgs;

import "geometry.proto";

message Map {
    message Section {
        enum TurnType {
            NO_TURN = 0;
            LEFT_TURN = 1;
            RIGHT_TURN = 2;
            U_TURN = 3;
        }
        message Lane {
            enum Type {
                DRIVING = 0;
                SEASIDE = 1;
                YARD = 2;
                CROSS = 3;
                CONNECTION = 4;
                LOCKZONE = 5;
                HATCHCOVER = 6;
                LANTITUDE = 7;
                LONGITUDE = 8;
                SHOULDER = 9;
                EMERGENCY = 10;
                JUNCTION = 11;
                BIKING = 12;
                SIDEWALK = 13;
                PARKING = 14;
            }

            enum TurnType {
                NO_TURN = 0;
                LEFT_TURN = 1;
                RIGHT_TURN = 2;
                U_TURN = 3;
            }

            repeated Point2D center_point = 1;
            repeated Point2D left_point = 2;
            repeated Point2D right_point = 3;
            int32 id = 4;
            Type type = 5;
            TurnType turn_type = 6;
            double speed_limit = 7;
        }
        repeated Lane lane = 1;
    }
    repeated Section section = 1;
}

