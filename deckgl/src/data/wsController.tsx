/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-16 17:55:13
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2024-10-16 15:11:33
 * @FilePath: /deckgl/src/data/wsController.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { memo, useEffect } from "react";
import ReconnectingWebSocket from "reconnecting-websocket";
import { DataSource } from "@/types";
import { useDispatch } from "react-redux";
import { setStatus } from "@/features/dataSlice";
import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
let rws: ReconnectingWebSocket | null = null;

let pendingTiem = 0;
function App({
    ip,
    port,
    origin,
    worker,
}: {
    ip: string | null;
    port: string | null;
    origin: DataSource | null;
    worker: any;
}) {
    const dispatch = useDispatch();
    const state = useSelector((state: RootState) => state.dataReducer.status);
    useEffect(() => {
        if (!ip) return;
        let suffix = {
            EXTEND: "extend",
            HMI: "",
        };
        let url;
        url = `ws://${ip}:${port}/ws/tm/v2/${suffix[origin]}`;
        if (origin == "FITALL") {
            url = `ws://${ip}:${port}/ws/monitor`;
        } else if (origin == "FITONE") {
            //  获取 url 参数
            const urlParams = new URLSearchParams(window.location.search);
            console.log(urlParams.get("id"));
            url = `ws://${ip}:${port}/ws/monitor/${urlParams.get("id")}`;
        }

        const options = {
            connectionTimeout: 2000,
            maxReconnectionDelay: 2000,
        };
        rws = new ReconnectingWebSocket(url, [], options);
        rws.binaryType = "arraybuffer";

        const timer = setInterval(() => {
            pendingTiem++;
            if (pendingTiem > 1 && state.wsConnect == "true") {
                dispatch(setStatus({ wsConnect: "pending" }));
            }
        }, 1000);

        if (rws) {
            rws.onopen = () => {
                dispatch(setStatus({ wsConnect: "true" }));
            };
            rws.onmessage = (event: MessageEvent) => {
                pendingTiem = 0;
                worker.postMessage({
                    type: "data",
                    origin: origin,
                    message: event.data,
                });
            };
            rws.onclose = () => {
                dispatch(setStatus({ wsConnect: "false" }));
            };
        }
        return () => {
            rws?.close();
            clearInterval(timer);
        };
    }, [ip, port, origin]);
    return <></>;
}

export default memo(App);
