/*
 * @Author: luo<PERSON>i <EMAIL>
 * @Date: 2022-08-24 10:11:34
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2023-08-25 17:28:00
 * @FilePath: /deckgl/src/data/snake.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { DataSource } from "@/types";
import { useEffect } from "react";
let ws: WebSocket | null = null;
function App({
    ip,
    port,
    origin,
    setData,
}: {
    ip: string | null;
    port: string | null;
    origin: DataSource | null;
    setData: React.Dispatch<React.SetStateAction<object | string | null>>;
}) {
    useEffect(() => {
        if (origin !== "SNAKE") {
            ws?.close();
            return;
        }
        if (!ip) return;
        // ws初始化
        const baseUrl = "**********";
        const wsUrl = "ws://" + ip + ":" + port + "/ws";
        ws = new WebSocket(wsUrl);
        ws.onopen = () => {};
        ws.onmessage = function (e) {
            setData(e.data);
        };

        ws.onerror = (e) => {};
        ws.onclose = () => {};

        return () => {
            ws?.close();
        };
    }, [ip, port, origin]);

    return <></>;
}

export default App;
