/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-16 17:55:13
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2024-08-22 15:58:33
 * @FilePath: /deckgl/src/data/verse.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { useEffect } from "react";

import ReconnectingWebSocket from "reconnecting-websocket";
import { DataSource } from "@/types";
let rws: ReconnectingWebSocket | null = null;
function App({
    ip,
    port,
    origin,
    worker,
}: {
    ip: string | null;
    origin: DataSource | null;
    port: string;
    worker: any;
}) {
    useEffect(() => {
        if (origin !== "VERSE") {
            rws?.close();
            return;
        }
        if (!ip) return;
        const url = "ws://" + ip + ":" + port + "/ws/tm/v2/extend";

        const options = {
            connectionTimeout: 500,
            maxReconnectionDelay: 500,
        };
        rws = new ReconnectingWebSocket(url, [], options);
        rws.binaryType = "arraybuffer";

        rws.onmessage = (event: MessageEvent) => {
            worker.postMessage({
                type: "data",
                origin: origin,
                message: event.data,
            });
        };
        return () => {
            rws?.close();
        };
    }, [ip, port, origin]);
    return <></>;
}

export default App;
