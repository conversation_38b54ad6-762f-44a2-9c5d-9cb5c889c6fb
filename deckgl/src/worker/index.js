/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-25 14:07:38
 * @LastEditors: luofei <EMAIL>
 * @LastEditTime: 2025-05-06 14:34:37
 * @FilePath: /deckgl/src/worker/index.js
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { toLatLon as toLatLonForMap } from "@/utils/mapTools";
import protobuf from "protobufjs";
import { getBoundingBoxByCenter, calculateBoxVertices } from "@/utils";
import { VehicleParam, TruckDebugInfo } from "@/utils/enum";
import { creat_glbs, MODEL_LIST } from "@/consconts";

const workData = {
    operation: {
        focus: 0,
    },
    debugInfo: TruckDebugInfo,
};

// 只存储一帧展示数据
let originData = null;
const decodeType = {
    HMI: null,
    EXTEND: null,
    FITONE: null,
    FITALL: null,
};

let AwesomeMessage = null;
console.log("welcome to worker~");
const root = new protobuf.Root();
root.resolvePath = (origin, target) => {
    if (origin.length === 0) {
        return origin + target;
    } else {
        return "/protobuf/" + target;
    }
};

// HMI
root.load("/protobuf/frame.proto", (err, root) => {
    if (err) {
        console.log(err);
    } else {
        decodeType.HMI = root?.lookupType("trunk.msgs.Frame");
    }
});

// EXTEND
root.load("/protobuf/offline_extend.proto", (err, root) => {
    if (err) {
        console.log(err);
    } else {
        // 仿真
        decodeType.EXTEND = root.lookupType("trunk.msgs.OfflineExtend");
    }
});
// fit 单车
root.load("/protobuf/trunk/proto/fit/monitor_one.proto", (err, root) => {
    if (err) {
        console.log(err);
    } else {
        // 仿真
        decodeType.FITONE = root.lookupType("trunk.fit.MonitorOne");
    }
});

// fit全局
root.load("/protobuf/trunk/proto/fit/monitor_all.proto", (err, root) => {
    if (err) {
        console.log(err);
    } else {
        // 仿真
        decodeType.FITALL = root.lookupType("trunk.fit.MonitorAll");
    }
});
function calculateMeridianAndConvert(longitude, latitude) {
    // Step 2: 计算子午线的度数
    if (!workData.UTMZone) return 0;
    const utmZone = workData.UTMZone;
    const meridianDegrees = (utmZone - 1) * 6 - 180 + 3;

    // Step 3: 将经度、纬度和子午线度数转换为弧度
    const longitudeRadians = (longitude * Math.PI) / 180;
    const latitudeRadians = (latitude * Math.PI) / 180;
    const meridianRadians = (meridianDegrees * Math.PI) / 180;

    // Step 4: 计算子午线弧度
    const meridian = Math.atan(
        Math.tan(longitudeRadians - meridianRadians) * Math.sin(latitudeRadians)
    );

    // 弧度转角度
    const angle = (meridian * 180) / Math.PI;
    return angle;
}
function toLatLon(position, z) {
    return toLatLonForMap(
        position,
        workData.UTMZone,
        workData.config?.mapConfig?.mapName || "",
        z || 0
    );
}

// utm 转车体坐标系
let coordinateOriginUtm;
function utmToBody(position, z) {
    if (!coordinateOriginUtm) {
        return [0, 0];
    } else {
        const { x: utm_x, y: utm_y, yaw: utm_theta } = coordinateOriginUtm;
        const newPosition = {};
        const x_offset = position.x - utm_x;
        const y_offset = position.y - utm_y;
        newPosition.x = (
            x_offset * Math.cos(utm_theta) +
            y_offset * Math.sin(utm_theta)
        ).toFixed(3);
        newPosition.y = (
            y_offset * Math.cos(utm_theta) -
            x_offset * Math.sin(utm_theta)
        ).toFixed(3);

        return newPosition;
    }
}
function NormalizeAngle(angle) {
    let a = (angle + Math.PI) % (2 * Math.PI);
    if (a <= 0.0) {
        a += 2.0 * Math.PI;
    }
    return a - Math.PI;
}
// 坐标转换
let isBodyC = false;
function CoordinateTransform(position, z, model) {
    // console.log(coordinateOriginUtm);
    if (isBodyC) {
        const { x, y } = position;
        if (x != 0 || y != 0) {
            return [x / 100000, y / 100000, z];
        } else {
            return [0, 0, z];
        }
    } else {
        if (model == "body") {
            return utmToBody(position, z);
            rz;
        } else {
            return toLatLon(position, z);
        }
    }
}
let wsTime = 0;
let timeFlage = 0;
let oldPathJSON = null;
onmessage = (e) => {
    const data = e?.data;
    if (!data) return;
    const type = data.type;
    if (type == "data") {
        // 实时数据  ws防爆
        const now = new Date().getTime();
        if (now - wsTime > 30) {
            const origin = data.origin;
            createData(origin, data);
            originData = data;
            wsTime = now;
        }
    } else if (type == "config") {
        workData.config = data.message;
    } else if (type == "operation") {
        const message = data.message;
        workData.operation = {
            ...workData.operation,
            ...message,
        };
    } else if (type == "customRender") {
        const message = data.message;
        workData.customRender = {
            ...workData.customRender,
            ...message,
        };
    } else if (type == "customData") {
        const message = data.message;
        workData.customData = {
            ...workData.customData,
            ...message,
        };
    } else if (type == "debug") {
        workData.debugInfo = {
            ...workData.debugInfo,
            ...data.message,
        };
        if (originData) {
            createData(originData.origin, originData, true);
        }

        if (
            JSON.stringify(workData.debugInfo.pathJSON) ==
            JSON.stringify(oldPathJSON)
        )
            return;
        const { waypoints, UTMZone } = workData.debugInfo.pathJSON || {};
        if (waypoints?.length > 0 && UTMZone) {
            const planningTraj = workData.debugInfo.pathJSON.waypoints;
            const debugInfo = workData.debugInfo;
            workData.UTMZone = workData.debugInfo.pathJSON.UTMZone;
            let modelName = workData.config?.mapConfig?.egoModel || "truck";
            const data = {
                lines: [],
                points: [],
                boxs: [],
                focus: {},
            };
            data.lines.push({
                path: planningTraj?.map((item) => {
                    data.points.push({
                        position: CoordinateTransform(item, 0.001),
                        color: [87, 255, 206, 255],
                    });
                    // 实时轨迹车辆包络
                    const { core, truck, trailer } = createBoudingBoxByKeyWords(
                        {
                            hingeJoint: [item.x, item.y],
                            truckTheta: item.theta,
                            truckAspect: {
                                length: debugTruckLength,
                                width: debugTruckWidth,
                            },
                            trailerTheta: item.trailerAngle
                                ? item.trailerAngle - Math.PI
                                : undefined,
                            trailerAspect: {
                                length: debugTrailerLength,
                                width: debugTrailerWidth,
                            },
                            boundingZ: 0.02,
                            opacity: 255,
                            color: [135, 206, 250, 0],
                            lineWidth: 0.1,
                            lineColor: [0, 100, 250, 255],
                            egoModel: modelName,
                            trailerLineColor: [255, 50, 0, 255],
                        }
                    );
                    data.boxs.push(core, truck);
                    if (trailer) {
                        data.boxs.push(trailer);
                    }

                    return CoordinateTransform(item, 0.001);
                }),
                width: 2,
                color: [87, 255, 206, 50],
            });
            if (data?.lines[0]?.path[0]) {
                data.focus.position = data.lines[0].path[0];
            }

            postMessage({
                data,
            });
        } else {
            oldPathJSON = workData.debugInfo.pathJSON;
            postMessage({
                data: {
                    lines: [],
                    points: [],
                    boxs: [],
                },
            });
        }

        oldPathJSON = workData.debugInfo.pathJSON;
    }
};
/**
 * @description: 数据解析
 * @param {*} origin 数据源
 * @param {*} data 带解析数据
 * @return {*}
 */
const decodeData = (origin, data) => {
    AwesomeMessage = decodeType[origin] || decodeType.EXTEND;
    if (AwesomeMessage && data instanceof ArrayBuffer) {
        let object = AwesomeMessage.toObject(
            AwesomeMessage.decode(new Uint8Array(data)),
            {
                enums: String, // enums as string names
                longs: String, // longs as strings (requires long.js)
                bytes: String, // bytes as base64 encoded strings
                defaults: true, // includes default values
                arrays: true, // populates empty arrays (repeated fields) even if defaults=false
                objects: true, // populates empty objects (map fields) even if defaults=false
                oneofs: true, // includes virtual oneof fields set to the present field's name
            }
        );
        workData.realData = object;
        return object;
    } else {
        return data;
    }
};

// 历史数据缓存
// const cache = new Map();

/**
 * @description: 数据组装
 * @param {*} origin 数据源
 * @param {*} data
 * @return {*}
 */
const createData = (origin, data, debug) => {
    // const time = performance.now();
    // console.time("createData");
    // console.time("decode");
    const decodeDataD = decodeData(origin, data.message);
    // console.timeEnd("decode");
    if (decodeDataD?.monitorOnes || decodeDataD?.vehBase?.cheId) {
        workData.data = fitAllData(decodeDataD);
    } else {
        workData.data = mapSetsData(decodeDataD);
        // todo 剩余距离放入 worker 计算
        //  主应用所需数据
        workData.toParent = {
            time: workData.data.time,
            naviId: workData.realData?.map?.naviId,
            stopDistances: workData.realData?.map?.stopDistances,
        };
    }

    //  时间戳过滤
    // if (workData.data.time != timeFlage || debug) {
    //     postMessage(workData);
    //     timeFlage = workData.data.time;
    // }
    postMessage(workData);
    // console.timeEnd("createData");
    // const newtime = performance.now() - time;
    // console.log(newtime);
    // if (newtime < 20) {
    //     console.log(newtime);
    //     postMessage(workData);
    // }
};

const mapSetsData = (datas) => {
    const ref = {
        glbs: creat_glbs(),
        points: [],
        circles: [],
        lines: [],
        boxs: [],
        marks: [],
        time: null,
        icons: [],
        images: [],
    };

    // 如果没有定位信息 全局修改为 车体坐标系
    const { frame, vehicle } = datas;
    if (vehicle) {
        const { position } = vehicle;
        if (!position) {
            isBodyC = true;
            vehicle.position = {
                position: {
                    x: 0,
                    y: 0,
                },
            };
        }
    }
    if (frame) {
        isBodyC = true;
        const { position } = frame.vehicle;
        if (!position) {
            frame.vehicle = {
                ...frame.vehicle,
                position: {
                    position: {
                        x: 0,
                        y: 0,
                        z: 0,
                    },
                },
            };
        }
    }
    isBodyC = false;
    create(datas, ref);

    return ref;
};
const fitAllData = (datas) => {
    const cheId = datas.vehBase?.cheId;
    const ref = {
        glbs: creat_glbs(),
        points: [],
        circles: [],
        lines: [],
        boxs: [],
        marks: [],
        time: null,
        icons: [],
        images: [],
    };

    if (cheId) {
        const data = {
            frame: datas.frame,
            cheId,
            taskInfo: datas.taskInfo,
            sourceType: datas.vehBase.sourceType,
            vehStatus: datas.vehStatus,
        };
        create(data, ref);
    } else {
        const { monitorOnes, cranes } = datas;
        //   全局
        if (monitorOnes.length > 0) {
            monitorOnes.forEach((item) => {
                const data = {
                    frame: item.frame,
                    cheId: item.vehBase.cheId,
                    taskInfo: item.taskInfo,
                    sourceType: item.vehBase.sourceType,
                    vehStatus: item.vehStatus,
                };
                create(data, ref, false);

                // navi 规划路线
                const { waypoints } = item.navi || {};
                if (waypoints?.length > 0) {
                    const lineType1 = [];
                    const lineType2 = [];
                    let isSline = true; // 标记
                    waypoints.forEach((point) => {
                        // planing.points.push(point.pos);
                        lineType1.push(CoordinateTransform(point.pos, 0.001));
                        if (point.type === "TURN") {
                            const turnPoint = CoordinateTransform(
                                point.pos,
                                0.002
                            );
                            if (isSline) {
                                // 如果当前正在处理曲线，则开始新曲线段
                                lineType2.push([turnPoint]); // 将新曲线段加入lineType2
                                isSline = false; // 当前不再是直线
                            } else {
                                // 如果当前已经在处理曲线，则继续当前曲线段
                                lineType2[lineType2.length - 1].push(turnPoint); // 将点加入当前曲线段
                            }
                        } else {
                            // 如果当前点不是曲线，则重置曲线标记
                            isSline = true;
                        }
                    });
                    ref.lines.push({
                        path: lineType1,
                        color: [255, 165, 0, 150],
                        width: 1,
                    });
                    lineType2.map((item) => {
                        ref.lines.push({
                            path: item,
                            color: [255, 0, 0, 150],
                            width: 1,
                        });
                    });
                }
            });
        }
        // 岸桥场桥
        cranes.forEach((item, index) => {
            const {
                craneId,
                gantryPos,
                craneType,
                trolleyOffset,
                spreaderHeight,
                lockStatus,
            } = item;
            if (craneType === "QC") {
                ref.glbs.anqiao.push({
                    id: craneId,
                    name: craneId,
                    position: CoordinateTransform(
                        [gantryPos.x, gantryPos.y],
                        0
                    ),
                    heading: 28,
                    properties: {
                        info: {
                            trolleyOffset,
                            spreaderHeight,
                            lockStatus,
                        },
                    },
                });
            } else if (craneType === "YC") {
                ref.glbs.changqiao.push({
                    id: craneId,
                    name: craneId,
                    position: CoordinateTransform(
                        [gantryPos.x, gantryPos.y],
                        0
                    ),
                    heading: 28,
                    properties: {
                        info: {
                            trolleyOffset,
                            spreaderHeight,
                            lockStatus,
                        },
                    },
                });
            } else if (craneType === "DBYC") {
                ref.glbs.dbchangqiao.push({
                    id: craneId,
                    name: craneId,
                    position: CoordinateTransform(
                        [gantryPos.x, gantryPos.y],
                        0
                    ),
                    heading: 28,
                    properties: {
                        info: {
                            trolleyOffset,
                            spreaderHeight,
                            lockStatus,
                        },
                    },
                });
            }
        });
    }

    return ref;
};
function create(data, ref, focus = true) {
    const { debugInfo } = workData;
    const {
        frame,
        entityInfo,
        aeb,
        debugFigure,
        fusionDetection,
        visionLanes,
        roadBoundary,
        constrains,
        debugObjects,
    } = data;
    const { glbs, points, marks, circles, lines, boxs, icons } = ref;

    const { vehicle, errorCodes, map, ads, environment, timestamp } =
        frame || data;
    if (timestamp) {
        if (timestamp.seconds || timestamp.seconds === 0) {
            ref.time = Number(
                timestamp.seconds +
                    "." +
                    String(timestamp.nanos).padStart(9, "0")
            );
        } else {
            ref.time = timestamp;
        }
    }

    const {
        position,
        yaw,
        UTMZone,
        vehicleSpeed, // m/s
        brake,
        throttle,
        gear,
        longAccel,
        lightStatus,
        trailerAngle,
        autoDriverStatus,
        steeringAngle,
        localizationStatus,
    } = vehicle || {};
    const { lanes, currentSpeedLimit } = map || {};
    const {
        decisionLeftBoundary,
        decisionRightBoundary,
        decisionReferenceLine,
        planningTraj,
        predictionObjs,
        decisionLatState,
        decisionLonState,
        decisionScenarioName,
        decisionStageName,
        rtSpeed, // 期望车速 m/s
        rtBrake, // 期望制动踏板开度
        rtThrottle, // 期望油门开度
        rtGear, // 期望档位
        rtSteeringAngleFront,
        rtSteeringAngleRear,
        stopReason,
        presetCruiseSpeed,
        laneChangeStatus,
        severityLevel,
        fcwLevel,
        planningNudge,
        planningTrajPts,
        nearestObstacleId,
    } = ads || {};
    const {
        objects,
        ogm,
        trafficLights = [],
        craneResult = [],
    } = environment || {};
    // 换算单位 m/s=> km/h
    const convertVehicleSpeed = vehicleSpeed * 3.6;
    const convertRtSpeed = rtSpeed * 3.6;
    let id = data.cheId || 0;
    let name = data.cheId || "";
    let modelName = workData.config?.mapConfig?.egoModel || "truck";
    ref.errorCodes = errorCodes;
    if (!workData.UTMZone) {
        workData.UTMZone = UTMZone;
    }
    if (!position?.position) return; // 转换点记录
    coordinateOriginUtm = position.position;
    coordinateOriginUtm.yaw = yaw;
    ref.coordinateOriginLatLon = CoordinateTransform(position.position);
    // 自车
    const lat = CoordinateTransform(position.position);
    const truckAngle =
        (180 / Math.PI) * yaw -
        180 -
        calculateMeridianAndConvert(lat[0], lat[1]);
    let info = {
        position: CoordinateTransform(position.position),
        heading: truckAngle,
        name: name,
        id: id,
        properties: {
            info: {
                stopReason: stopReason, // 停车原因
                longAccel, //纵向加速度 m/s^2
                aebCollision: stopReason == 30 ? true : false, //aeb触发状态 true: 触发 false: 未触发
                autoDriverStatus, //自动驾驶状态 0: 自动模式 1: 人工模式 2: 遥控模式 3: 远控模式 4: 维保模式
                steeringAngle, // 方向盘角度°
                // 底盘
                vehicleSpeed: convertVehicleSpeed, // 车速 km/h
                brake, // 制动踏板开度
                throttle, // 油门开度
                gear, // 档位
                lightStatus, // 灯光状态
                trailerAngle, // 挂车角度
                presetCruiseSpeed, // 预设巡航速度
                laneChangeStatus, // 换道提示
                severityLevel, // 故障等级
                currentSpeedLimit, // 当前限速
                // 决策
                decision: {
                    decisionLatState, // 决策-横向 0: 跟随 1: 左侧变道 2: 右侧变道 3: 取消变道 4: left nudge 5: right nudge 6: 未知
                    decisionLonState, // 决策-纵向 0: 保持 1: 超车 2: yield 3: stop 4: e-stop
                    decisionScenarioName, // 决策-场景
                    decisionStageName, // 决策-阶段
                    ...craneResult[0],
                },
                diff: {
                    trailerAngle, // 挂车角度 °
                    steeringAngle, //方向盘角度°，平板车默认填充0
                    rtSteeringAngleFront, // 前轮转角控制量 -1.0 ~ 1.0
                    rtSteeringAngleRear, // 后轮转角控制量 -1.0 ~ 1.0
                    localizationStatus, // 定位状态
                    fcwLevel, // FCW预警等级
                    planningNudge, // planning的nudge状态
                    speed: [convertVehicleSpeed, convertRtSpeed], // 车速 km/h
                    brake: [brake, rtBrake], // 制动踏板开度
                    throttle: [throttle, rtThrottle], //油门开度
                    gear: [gear, rtGear], // 档位
                },
                trafficLights,
                constrains,
            },
        },
    };

    const debugTruckWidth = debugInfo.truck.width.value || 0;
    const debugTruckLength = debugInfo.truck.length.value || 0;
    const debugTrailerWidth = debugInfo.trailer.width.value || 0;
    const debugTrailerLength = debugInfo.trailer.length.value || 0;
    // 仿真拓展
    if (entityInfo?.length > 0) {
        // 修改为for
        for (let i = 0; i < entityInfo.length; i++) {
            const item = entityInfo[i];
            const {
                bounding,
                isFocus,
                modelName,
                name,
                pose,
                safetyBoundaryBounding,
                yaw,
                distance,
                speed,
                id,
            } = item;

            let text = "";
            const speedValue = {
                x: speed.x.toFixed(2),
                y: speed.y.toFixed(2),
            };
            if (debugInfo.simulation.details.value) {
                text +=
                    "\n" +
                    "speed:" +
                    JSON.stringify(speedValue) +
                    "\n" +
                    "position:" +
                    JSON.stringify(pose) +
                    "\n" +
                    "distance:" +
                    distance.toFixed(2);
            }
            if (text) {
                marks.push({
                    position: CoordinateTransform(pose, 5),
                    text: text,
                });
            }

            // 自车包络
            if (bounding.length > 0) {
                const header = bounding[0];
                const trailer = bounding[1];
                // 模型
                if (glbs[modelName]) {
                    glbs[modelName].push({
                        position: CoordinateTransform(pose),
                        heading: (180 / Math.PI) * yaw - 180,
                        name: name,
                        id: id,
                        color:
                            name.indexOf("shadow") == -1
                                ? []
                                : [255, 255, 255, 80],
                    });
                }

                if (trailer) {
                    glbs.trailer.push({
                        ...info,
                        name: name + "_trailer",
                        position: CoordinateTransform(pose),
                        heading: (180 / Math.PI) * trailer.yaw - 180,
                    });
                }
                // 包络
                if (debugInfo.simulation.details.value) {
                    bounding.map((item) => {
                        boxs.push({
                            box: item.points.map((box) => {
                                return CoordinateTransform(box);
                            }),
                            height: item.points[0].z,
                            color: [0, 255, 255, 100],
                        });
                    });
                }
            }

            // metric安全边界
            if (safetyBoundaryBounding[0]) {
                for (let i = 0; i < safetyBoundaryBounding?.length; i++) {
                    let bounding = safetyBoundaryBounding[i].points;
                    boxs.push({
                        box: bounding.map((box) => CoordinateTransform(box)),
                        height: 0.1,
                        color: [255, 255, 0, 200],
                    });
                }
            }

            // foucs
            if (workData.operation.focus?.id == id) {
                info.position = CoordinateTransform(pose);
                info.heading = (180 / Math.PI) * yaw - 180;
            }
        }
    } else {
        if (glbs[modelName]) {
            // 动态测距
            const radius = workData.debugInfo?.distance?.value?.value || 0;
            // todo  多模型区分
            // 生成动态测距数据
            const distance = Array.from({ length: 4 }, (_, i) => {
                return {
                    position: [0, 0],
                    radius: radius * i,
                };
            });
            glbs[modelName].push({ ...info, distance: distance });
        }
        if (modelName === "truck" && trailerAngle < 180) {
            glbs.trailer.push({
                ...info,
                position: CoordinateTransform(position.position),
                heading: truckAngle - trailerAngle,
                id: id,
                name: id + "_trailer",
            });
        }
    }
    // 实车包络
    if (debugInfo.display) {
        const { core, truck, trailer } = createBoudingBoxByKeyWords({
            hingeJoint: [position.position.x, position.position.y],
            truckTheta: yaw,
            truckAspect: {
                length: debugTruckLength,
                width: debugTruckWidth,
            },
            trailerTheta:
                trailerAngle < 180
                    ? yaw - (trailerAngle / 180) * Math.PI - Math.PI
                    : undefined,
            trailerAspect: {
                length: debugTrailerLength,
                width: debugTrailerWidth,
            },
            boundingZ: 0.005,
            opacity: 100,
            lineWidth: 0.03,
            color: [100, 100, 255, 100],
            lineColor: [100, 100, 255, 255],
            trailerLineColor: [100, 100, 255, 255],
            egoModel: modelName,
        });
        boxs.push(core, truck);
        trailer && boxs.push(trailer);
    }
    // 可行驶区域 局部地图
    if (debugInfo.planning.lanes.value) {
        if (lanes?.[0]) {
            const laneIds = [];
            for (let i = 0; i < lanes.length; i++) {
                let lane = lanes[i];
                const {
                    leftLine,
                    rightLine,
                    centerLine,
                    boundaryType,
                    boundaryColor,
                    id,
                    speedLimit,
                    goalIndex,
                } = lane;

                let lid = (id / 100).toFixed(0);
                if (laneIds.indexOf(lid) == -1) {
                    laneIds.push(lid);
                }
                // 分组颜色
                const colorID = {
                    1: [255, 70, 31],
                    2: [188, 230, 114],
                    3: [112, 243, 255],
                    4: [255, 41, 81],
                    5: [255, 241, 67],
                    6: [68, 206, 246],
                };

                const colorState = {
                    1: [100, 100, 100],
                    2: [255, 165, 0],
                    3: [255, 0, 0],
                };

                let leftolor = colorState[boundaryColor[0]];
                let rightColor = colorState[boundaryColor[1]];

                // 分段地图
                if (debugInfo.planning.sectionMap.value) {
                    leftolor = colorID[laneIds.indexOf(lid) + 1];

                    rightColor = colorID[laneIds.indexOf(lid) + 1];
                }

                lines.push({
                    path: rightLine.map((item) =>
                        CoordinateTransform(item, 0.001)
                    ),
                    width: 0.2,
                    color: rightColor,
                    isDash: boundaryType[1] == 2 ? true : false,
                });
                lines.push({
                    path: leftLine.map((item) =>
                        CoordinateTransform(item, 0.001)
                    ),
                    width: 0.2,
                    color: leftolor,
                    isDash: boundaryType[0] == 2 ? true : false,
                });

                if (debugInfo.visualization.speedLimit.value) {
                    // 限速标志
                    if (speedLimit && goalIndex != -1) {
                        marks.push({
                            position: CoordinateTransform(
                                centerLine[(centerLine.length / 3.2).toFixed()],
                                3
                            ),
                            text: JSON.stringify(speedLimit),
                            size: 17,
                        });
                        icons.push({
                            position: CoordinateTransform(
                                centerLine[(centerLine.length / 3.2).toFixed()],
                                3
                            ),
                            size: 40, //4
                            icon: {
                                url: "geojsonImg/Circle.png",
                                width: 128,
                                height: 128,
                                x: 64,
                                anchorY: 65,
                            },
                        });
                    }
                }
            }
        }
    }
    // 实时轨迹
    if (debugInfo.planning.planningTraj.value) {
        if (planningTrajPts?.[0]) {
            lines.push({
                path: planningTrajPts?.map((item) => {
                    const utm4d = item.pathPoint.utm4d;
                    const trailerAngle = item.pathPoint.utmTrailerAngle;
                    const point = CoordinateTransform(utm4d, 0.001);
                    points.push({
                        position: point,
                        color: [87, 255, 206, 255],
                    });
                    // 实时轨迹车辆包络
                    if (debugInfo.planning.planningBox.value) {
                        const { core, truck, trailer } =
                            createBoudingBoxByKeyWords({
                                hingeJoint: [utm4d.x, utm4d.y],
                                truckTheta: utm4d.theta,
                                truckAspect: {
                                    length: debugTruckLength,
                                    width: debugTruckWidth,
                                },
                                trailerTheta: trailerAngle
                                    ? trailerAngle - Math.PI
                                    : undefined,
                                trailerAspect: {
                                    length: debugTrailerLength,
                                    width: debugTrailerWidth,
                                },
                                boundingZ: 0.02,
                                opacity: 255,
                                color: [135, 206, 250, 0],
                                lineWidth: 0.03,
                                lineColor: [135, 206, 250, 255],
                                egoModel: modelName,
                            });
                        boxs.push(core, truck);
                        if (trailer) {
                            boxs.push(trailer);
                        }
                    }
                    return point;
                }),
                width: 0.5,
                color: [87, 255, 206, 200],
            });
        }
    }
    // 决策边界
    if (debugInfo.planning.decisionBoundary.value) {
        if (decisionLeftBoundary?.[0]) {
            lines.push({
                path: decisionLeftBoundary?.map((item) =>
                    CoordinateTransform(item, 0.001)
                ),
                width: 0.2,
                color: [41, 122, 255, 255],
            });
        }
        if (decisionRightBoundary?.[0]) {
            lines.push({
                path: decisionRightBoundary?.map((item) => {
                    return CoordinateTransform(item, 0.001);
                }),
                width: 0.2,
                color: [41, 122, 255, 255],
            });
        }
    }
    // 决策参考线
    if (debugInfo.planning.decisionReferenceLine.value) {
        if (decisionReferenceLine?.[0]) {
            lines.push({
                path: decisionReferenceLine?.map((item) =>
                    CoordinateTransform(item, 0.002)
                ),
                width: 0.2,
                color: [41, 122, 255, 200],
            });
        }
    }
    // 障碍物包络
    if (objects?.[0]) {
        // tad 字典
        // const glblist = {
        //     1: "vehicle",
        //     2: "motorcycle",
        //     3: "truck_normal",
        //     4: "person",
        //     5: "bus",
        //     9: "bicycle",
        // };
        // https://trunk.feishu.cn/wiki/wikcnY0QsMEsJdQdYVmy2lnHQIO?from=from_copylink
        const glblist = {
            1: "vehicle",
            2: "bicycle",
            3: "person",
            4: "bus",
            5: "three_wheel",
            6: "traffic_cone",
            7: "barrel",
            10: "art",
            11: "art_default",
            12: "art_ms",
            13: "art_mb",
            20: "truck_normal",
            21: "obstacle_truck",
            22: "obstacle_trailter",
            23: "obstacle_trailter",
            24: "obstacle_trailter",
            25: "obstacle_trailter",
            30: "alien_vehicle",
            31: "forklift",
            32: "eccentric",
        };
        for (let i = 0; i < objects.length; i++) {
            let obj = objects[i];
            const {
                vertex,
                center,
                classification,
                heading,
                speed,
                id,
                size,
                referencePoint,
            } = obj;

            const modelType = glblist[classification];
            if (modelType) {
                const { size: modelSize } = MODEL_LIST[modelType];
                let objectsModelScale = { x: 1, y: 1, z: 1 };
                if (size) {
                    const { x, y, z } = size;
                    // 模型缩放比例 blender与decklgl坐标轴不一致 模型的y和z互换
                    objectsModelScale = {
                        x: Number((x / modelSize.x).toFixed(2)),
                        y: Number((z / modelSize.z).toFixed(2)),
                        z: Number((y / modelSize.y).toFixed(2)),
                    };
                }
                glbs[modelType]?.push({
                    position: CoordinateTransform(center, 0.001),
                    heading: heading * (180 / Math.PI) - 180,
                    customScale: objectsModelScale,
                    info: {
                        id: id,
                    },
                });
            }
            // 根据车辆center 和 自车position 计算距离
            const distance = Math.sqrt(
                Math.pow(center.x - position.position.x, 2) +
                    Math.pow(center.y - position.position.y, 2)
            );
            const speedValue = {
                x: speed.x.toFixed(2),
                y: speed.y.toFixed(2),
            };
            // utmToBody
            if (debugInfo.perception.objects.value) {
                if (vertex?.[0]) {
                    // 感知障碍物的id是匹配 高亮显示
                    let color, lineColor, lineWidth;
                    if (nearestObstacleId == id) {
                        color = [240, 150, 20, 150];
                    }

                    // 选择障碍物
                    if (workData.operation.selectObj.id == id) {
                        const text =
                            "ID:" +
                            id +
                            "\n" +
                            "speed:" +
                            JSON.stringify(speedValue) +
                            "\n" +
                            "position:" +
                            JSON.stringify(utmToBody(center)) +
                            "\n" +
                            "distance:" +
                            distance.toFixed(2) +
                            "\n" +
                            "type:" +
                            glblist[classification] +
                            "_" +
                            classification;
                        marks.push({
                            position: CoordinateTransform(center, 5),
                            text: text,
                            size: 12,
                        });
                        lineColor = [0, 0, 255, 255];
                        lineWidth = 20;
                    }
                    boxs.push({
                        box: vertex.map((position) => {
                            return CoordinateTransform(position, 0.001);
                        }),
                        height: size?.z || 0,
                        color,
                        lineColor,
                        lineWidth,
                        // 详情
                        info: {
                            id: id,
                        },
                    });
                    circles.push({
                        position: CoordinateTransform(center, 0.02),
                        radius: 0.1,
                        color: [255, 150, 0, 255],
                    });

                    // 模型追踪框
                    if (debugInfo.perception.TrackObj.value) {
                        if (size) {
                            const boxVertices = calculateBoxVertices(
                                center,
                                size,
                                heading
                            );
                            boxs.push({
                                box: boxVertices.map((position) => {
                                    return CoordinateTransform(position, 0.001);
                                }),
                                height: size?.z || 0,
                                color: [0, 0, 0, 0],
                                lineColor: [0, 255, 0, 255],
                            });
                        }
                        referencePoint &&
                            circles.push({
                                position: CoordinateTransform(
                                    referencePoint,
                                    0.02
                                ),
                                radius: 0.2,
                                color: [255, 0, 0, 255],
                            });
                    }
                }
            }

            // 障碍物详情
            if (debugInfo.perception.perceptionDetails.value) {
                const text =
                    "ID:" +
                    id +
                    "\n" +
                    "speed:" +
                    JSON.stringify(speedValue) +
                    "\n" +
                    "position:" +
                    JSON.stringify(utmToBody(center)) +
                    "\n" +
                    "distance:" +
                    distance.toFixed(2) +
                    "\n" +
                    "type:" +
                    glblist[classification] +
                    "_" +
                    classification;
                marks.push({
                    position: CoordinateTransform(center, 5),
                    text: text,
                    size: 12,
                });
            }
        }
    }
    //模型检测框
    if (debugInfo.perception.DebugObj.value && debugObjects?.length) {
        for (let i = 0; i < debugObjects.length; i++) {
            const { center, heading, size } = debugObjects[i];
            const boxVertices = calculateBoxVertices(center, size, heading);
            boxs.push({
                box: boxVertices.map((position) => {
                    return CoordinateTransform(position, 0.001);
                }),
                height: size?.z || 0,
                color: [0, 0, 0, 0],
                lineColor: [255, 0, 0, 255],
            });
        }
    }
    // 障碍物点云
    if (debugInfo.perception.ogm.value) {
        if (ogm?.[0]) {
            for (let i = 0; i < ogm.length; i++) {
                let obj = ogm[i];
                const { point } = obj;
                points.push({
                    position: CoordinateTransform(point, 0.001),
                });
            }
        }
    }
    // 障碍物预测轨迹
    if (debugInfo.perception.predictionObjs.value) {
        if (predictionObjs?.[0]) {
            for (let i = 0; i < predictionObjs.length; i++) {
                let obj = predictionObjs[i];
                const { trajectories } = obj;
                for (let j = 0; j < trajectories.length; j++) {
                    let item = trajectories[j];
                    const { points } = item;
                    lines.push({
                        path: points.map((position) =>
                            CoordinateTransform(position, 0.001)
                        ),
                        width: 0.1,
                    });
                }
            }
        }
    }

    // 回放拓展
    const { collisionPoint, trajectories, controlOffset } = aeb || {};
    if (controlOffset) {
        info.properties.info.decision = {
            ...info.properties.info.decision,
            ...controlOffset,
        };
    }

    // aeb 碰撞点
    if (collisionPoint) {
        const position = CoordinateTransform(collisionPoint, 0.001);
        circles.push({
            position: position,
            radius: 0.5,
            color: [255, 0, 0, 255],
        });
        icons.push({
            position: position,
            size: 10,
            icon: {
                url: "geojsonImg/warning.png",
                width: 128,
                height: 128,
                x: 32,
                anchorY: 150,
            },
        });
    }
    // aeb 预测轨迹
    if (debugInfo.planning.aebBox.value) {
        if (trajectories?.[0]) {
            for (let i = 0; i < trajectories.length; i++) {
                const item = trajectories[i];
                const { core, truck, trailer } = createBoudingBoxByKeyWords({
                    hingeJoint: [item.position.x, item.position.y],
                    truckTheta: item.utmTheta,
                    truckAspect: {
                        length: debugTruckLength,
                        width: debugTruckWidth,
                    },
                    trailerTheta: item.utmTrailerTheta - Math.PI,
                    trailerAspect: {
                        length: debugTrailerLength,
                        width: debugTrailerWidth,
                    },
                    boundingZ: 0.002,
                    opacity: 1,
                    lineWidth: 0.03,
                    color: [135, 206, 250, 0],
                    lineWidth: 0.03,
                    lineColor: [135, 206, 250, 255],
                    trailerLineColor: [255, 100, 100, 255],
                    egoModel: modelName,
                });
                boxs.push(core, truck);
                trailer && boxs.push(trailer);
            }
        }
    }
    // 融合点云
    if (debugInfo.perception.fusionDetection.value && fusionDetection) {
        const { ogm } = fusionDetection;
        ogm?.map((obj) => {
            const { point } = obj;
            points.push({
                position: CoordinateTransform(point, 0.001),
                color: [255, 0, 0, 100],
            });
        });
    }

    //  视觉 车道线
    if (debugInfo.perception.visionLanesDetails.value && visionLanes) {
        visionLanes.map((obj) => {
            const { points, type } = obj;
            lines.push({
                path: points.map((position) =>
                    CoordinateTransform(position, 0.001)
                ),
                width: 0.2,
                color: [255, 0, 0, 255],
                isDash: type === 2 ? true : false,
            });
        });
    }

    // 海岸线
    if (debugInfo.perception.roadBoundary.value && roadBoundary) {
        roadBoundary.forEach((item) => {
            const { ogm } = item;
            ogm.map((obj) => {
                const { point } = obj;
                points.push({
                    position: CoordinateTransform(point, 0.001),
                    color: [255, 0, 0, 255],
                });
            });
        });
    }

    // 第三方自定义显示
    if (debugFigure && debugFigure != "") {
        let debugFigureData = JSON.parse(debugFigure);
        if (!debugFigureData) return;
        for (let i = 0; i < debugFigureData.length; i++) {
            const item = debugFigureData[i];
            const type = item.type;
            const coordinates = item.coordinates;
            const properties = item.properties;
            switch (type) {
                case "Point":
                    marks.push({
                        position: CoordinateTransform(
                            coordinates,
                            coordinates[2]
                        ),
                        text: properties.text,
                        color: properties.color,
                        size: properties.size,
                    });
                    break;

                case "LineString":
                    lines.push({
                        path: coordinates.map((item) =>
                            CoordinateTransform(item, 0.001)
                        ),
                        width: properties.lineWidth,
                        color: properties.lineColor,
                        isDash: properties.isDash,
                    });
                    break;
                case "Polygon":
                    boxs.push({
                        box: coordinates.map((item) =>
                            item.map((item) => CoordinateTransform(item))
                        ),
                        height: properties.elevation,
                        color: properties.fillColor,
                        lineColor: properties.lineColor,
                        lineWidth: properties.lineWidth,
                    });
                    break;
                // {name: 'Colma (COLM)', code:'CM', address: '365 D Street, Colma CA 94014', exits: 4214, coordinates: [-122.466233, 37.684638]}
                case "Circle":
                    circles.push({
                        position: CoordinateTransform(
                            coordinates,
                            coordinates[2]
                        ),
                        radius: properties.radius,
                        color: properties.color,
                        lineColor: properties.lineColor,
                    });
                default:
                    break;
            }
        }
    }
    // 上层应用 customGlb
    if (workData?.customRender?.customGlb?.length > 0) {
        workData.customRender.customGlb.map((item) => {
            const { modelName, position, yaw } = item;

            glbs[modelName].push({
                position: CoordinateTransform(position),
                heading: (180 / Math.PI) * yaw - 180,
            });
        });
    }

    // focus
    // ref.focus = info;
    if (focus) ref.focus = info;
    ref.customGeojson = workData?.customRender?.customGeojson;
}
const createBoudingBoxByKeyWords = ({
    hingeJoint,
    truckTheta,
    truckAspect,
    trailerTheta,
    trailerAspect,
    boundingZ = 0.001,
    opacity = 100,
    color = [255, 0, 0, opacity],
    lineColor = [255, 100, 100, opacity],
    lineWidth,
    egoModel,
    trailerLineColor = [255, 100, 100, opacity],
}) => {
    let core, truck, trailer;
    const coreBouding = getBoundingBoxByCenter({
        center: hingeJoint,
        width: 0.1,
        length: 0.1,
        yaw: truckTheta,
    });
    core = {
        box: coreBouding.map((item) => CoordinateTransform(item, boundingZ)),
        height: 0.01,
        color: [255, 0, 0, opacity],
    };
    if (egoModel !== "art" && egoModel !== "agv" && egoModel !== "bus") {
        const lengthValue =
            truckAspect.length / 2 - VehicleParam.head_base2tail;
        const fixCenter = [
            hingeJoint[0] + lengthValue * Math.cos(truckTheta),
            hingeJoint[1] + lengthValue * Math.sin(truckTheta),
        ];
        const truckBounding = getBoundingBoxByCenter({
            center: fixCenter,
            width: truckAspect.width,
            length: truckAspect.length,
            yaw: truckTheta,
        });
        truck = {
            box: truckBounding.map((item) =>
                CoordinateTransform(item, 0.001 + boundingZ)
            ),
            height: 0.01,
            color,
            lineColor: lineColor,
            lineWidth: lineWidth,
        };
        if (trailerTheta !== undefined) {
            const lengthValueTrailer =
                trailerAspect.length / 2 - VehicleParam.trailer_base2front;
            const fixCenterTrailer = [
                hingeJoint[0] + lengthValueTrailer * Math.cos(trailerTheta),
                hingeJoint[1] + lengthValueTrailer * Math.sin(trailerTheta),
            ];

            const boundingTrailer = getBoundingBoxByCenter({
                center: fixCenterTrailer,
                width: trailerAspect.width,
                length: trailerAspect.length,
                yaw: trailerTheta,
            });
            trailer = {
                box: boundingTrailer.map((item) =>
                    CoordinateTransform(item, 0.001 + boundingZ)
                ),
                height: 0.01,
                color,
                lineColor: trailerLineColor || lineColor,
                lineWidth: lineWidth,
            };
        }
    } else {
        const truckBounding = getBoundingBoxByCenter({
            center: hingeJoint,
            width: truckAspect.width,
            length: truckAspect.length,
            yaw: truckTheta,
        });
        truck = {
            box: truckBounding.map((item) =>
                CoordinateTransform(item, 0.001 + boundingZ)
            ),
            height: 0.01,
            color,
            lineColor: lineColor,
            lineWidth: lineWidth,
        };
    }
    return {
        core,
        truck,
        trailer,
    };
};
