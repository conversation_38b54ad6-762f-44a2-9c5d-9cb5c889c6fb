/**
 * @description: TrunkSnake 数据匹配
 * @param {*} data
 * @return {*}
 */
const createData_Snake = (data) => {
    // 数据组装
    const { ego, obu, platoonlist, simulation_extend } = data;

    let boxs = [];
    let lines = [];
    let glbs = {
        person: [],
        bicycle: [],
        motorcycle: [],
        vehicle: [],
        truck: [],
        trailer: [],
        truck_all: [],
        art: [],
    };
    let marks = [];
    let time = 0;

    let ego_id = 0;
    let ego_name = "ego_0";
    let ego_modelName = "truck";

    // 仿真拓展
    if (simulation_extend) {
        const { entity_info, trunk_bounding, metric_info, timestamp } =
            simulation_extend;

        const { id, name, model_name } = entity_info;
        ego_id = id;
        ego_name = name;
        ego_modelName = model_name;
        if (timestamp) {
            time = timestamp / 1000;
        }
        // 自车包络
        if (model_name == "truck" && trunk_bounding.length > 0) {
            const header = trunk_bounding[0];
            const trailer = trunk_bounding[1];

            if (trailer) {
                glbs.trailer.push({
                    position: [ego.position.x, ego.position.y],
                    heading: (180 / Math.PI) * trailer.yaw - 180,
                });
            }
            trunk_bounding.forEach((item) => {
                const box = item.bounding.map((bx) => {
                    return [bx[0], bx[1]];
                });
                boxs.push({
                    box: box,
                    height: item.bounding[0].z || 1,
                    color: [0, 255, 255, 100],
                });
            });
        }

        // metric安全边界
        if (metric_info?.metric_bounding) {
            metric_info.metric_bounding.forEach((metric_bounding) => {
                boxs.push({
                    box: metric_bounding.bounding.map((item) => {
                        return item;
                    }),
                    height: 0.1,
                    color: [255, 255, 0, 200],
                });
            });
        }
    }
    const info = {
        position: [ego.position.x, ego.position.y],
        heading: (180 / Math.PI) * ego.heading - 180,
        name: ego.plateNum || ego_name,
        id: ego_id,
        type: "truck",
        properties: {
            info: {
                distance: ego.distance || 0, //距离
                vehicleSpeed: ego.velocity || 0, //车速
                gear: Number(ego.gear) || 0, //档位
                lightStatus: ego.light_status || 0, //转向灯
                TNPMode: ego.TNPMode || 0, //TNP状态
                targetSpeed: ego.target_speed || 0, //目标车速
            },
        },
    };
    glbs[ego_modelName].push(info);
    lines.push({
        path: ego.reference_line,
        width: 2,
        color: [80, 150, 255, 150],
    });
    if (platoonlist.list) {
        platoonlist.list.forEach((item) => {
            glbs.truck.push({
                position: [item.position.x, item.position.y],
                heading: (180 / Math.PI) * item.heading - 180,
                name: item.plateNum,
                id: item.uid,
            });
        });
    }
    ego.perception_res.forEach((item) => {
        boxs.push({
            box: item.map((item) => {
                return [item.x, item.y];
            }),
        });

        // 模型处理
        // const position = [item.position.x, item.position.y];
        // const heading = (180 / Math.PI) * item.yaw - 180;
        // switch (item.type) {
        //     case 1:
        //         glbs.vehicle.push({
        //             position,
        //             heading,
        //         });
        //     case 2:
        //         glbs.motorcycle.push({
        //             position,
        //             heading,
        //         });
        //     case 3:
        //         glbs.truck.push({
        //             position,
        //             heading,
        //         });
        //     case 4:
        //         glbs.person.push({
        //             position,
        //             heading,
        //         });
        //     case 5:
        //         glbs.bicycle.push({
        //             position,
        //             heading,
        //         });
        // }
    });

    if (obu.vehicles) {
        obu.vehicles.forEach((item) => {
            glbs[item.type].push({
                position: [ego.position.x, ego.position.y],
                heading: (180 / Math.PI) * ego.heading - 180,
                name: ego.plateNum,
            });
        });
    }

    if (ego.drivableArea) {
        ego.drivableArea.forEach((item) => {
            const { leftLine, rightLine, centerLine, type, color } = item;
            //  repeated int32 type = 4;  // [left_type, right_color] 0:未知 1:实线 2:虚线 3:路边 4:波茨点 5:虚拟线
            //  repeated int32 color = 5; // [left_color, right_color] 0:未知 1:白线 2:黄线
            const colorState = {
                1: [100, 100, 100],
                2: [255, 165, 0],
            };
            const leftData = {
                path: leftLine,
                width: 0.2,
                color: colorState[color[0]],
            };
            const rightData = {
                path: rightLine,
                width: 0.2,
                color: colorState[color[1]],
            };

            // const centerData = {
            //     path: centerLine,
            //     width: 0.1,
            //     color: colorState[2],
            //     isDash: true,
            // };
            if (type[0] == 2) leftData.isDash = true;
            if (type[1] == 2) rightData.isDash = true;

            lines.push(leftData, rightData);
        });
    }
    return {
        glbs,
        boxs,
        lines,
        marks,
        time,
        focus: info,
    };
};
export default createData_Snake;
