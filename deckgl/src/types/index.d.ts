/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> l<PERSON>@trunk.tech
 * @Date: 2022-11-11 13:28:53
 * @LastEditors: jack 501177081.com
 * @LastEditTime: 2025-07-21 18:00:58
 * @FilePath: /deckgl/src/types/index.d.ts
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { PanelType, GeojsonMap } from "@/components/ConfigPage/dataPanelList";
import { b } from "vite/dist/node/types.d-aGj9QkWt";

export type Vec2 = [number, number];
export type Vec3 = [number, number, number];
export type Vec4 = [number, number, number, number];
export type Path = [number, number][];
export type Theme = "light" | "dark";
export type DataSource = "HMI" | "EXTEND" | "SNAKE";

export interface IMark {
    position: Vec3;
    name?: string;
}
export interface ILine {
    path: Path;
    name?: string;
    color?: Vec4;
}
/**
 * @description: glb model
 * @return {*}
 */
export interface IGlbs {
    position: Vec3 | Vec2;
    heading: number;
    name?: string;
    id?: string;
}
/**
 * @description: 感知结果
 * @return {*}
 */
export interface IPerceptionBox {
    box: Vec2;
    height: number;
}
/**
 * @description: 改变地图视图
 * @return {*}
 */
export interface IMapConfig {
    parentApp: string;
    mapConfig: {
        mapName: GeojsonMap;
        zoom: number;
        bearing: number;
        pitch: number;
        egoModel: string;
        osm: boolean;
        visualAngle?: string;
        latitude?: number;
        longitude?: number;
    };
    layoutConfig?: any[];
    theme?: Theme;
    osm?: boolean;
    connectConfig: {
        ip: string;
        port: number;
        apiPort: number;
        versionPort: number;
        rosPort: number;
        origin: string;
    };
    customGeojson?: object | null;
    mapList: object[];
    tools: boolean;
    language: string;
}

/**
 * @description: Deck视图配置
 * @return {*}
 */
export interface IDeckViewConfig {
    width: number;
    height: number;
    latitude: number;
    longitude: number;
    maxPitch: number;
    maxZoom: number;
    minPitch: number;
    minZoom: number;
    normalize: boolean;
    pitch: number;
    bearing: number;
    zoom: number;
}

/**
 * @description: 播放展示元素
 * @return {*}
 */
export interface IPlayerData {
    points: [];
    marks: [];
    lines: [];
    boxs: [];
    glbs: [];
    circles: [];
    icons: [];
    coordinateOriginLatLon: [number, number];
    customGeojson: any;
}
/**
 * @description: entity集合
 * @return {*}
 */
export interface IEntityCollect {
    person: IGlbs[];
    bicycle: IGlbs[];
    vehicle: IGlbs[];
    truck: IGlbs[];
    trailer: IGlbs[];
    // 声明之后可以用方括号的方式去对象里边的值 用于for in 遍历
    [key: string]: IGlbs[];
}

export interface IReplayTopic {
    name: string;
    type: string;
}
export interface IReplayBagInfo {
    topics: IReplayTopic[];
    topics_lost: string[];
    topics_error: string[];
    bag_end_time: number;
    bag_start_time: number;
    bag_length: number;
    bag_size: string;
    bag_name: string;
}
/**
 * @description: store数据
 * @return {*}
 */

export interface IStoreData {
    config: IMapConfig;
    operation: {
        focus: object;
        fullScreen: boolean;
        showInfo: object;
        voice: boolean;
        clearCache: boolean;
        display: object;
        selectObj: object;
    };
    customRender: {};
    customData: {};
    settings: {
        viewMode: string;
        lock: boolean;
        measure: boolean;
        satelliteMap: boolean;
        reset: boolean;
        debug: boolean;
        legend: boolean;
        console: boolean;
        dotting: boolean;
        transformModel: boolean;
        modelVisible: boolean;
    };
    status: {
        wsConnect: "true" | "false" | "pending";
        loading: boolean;
    };
    data: any;
    showPointCloud: boolean;
    truckDebugInfo: {
        display: boolean;
        egoModel: string;
        truck: object;
        trailer: object;
        visualization: {};
        perception: {};
        planning: {};
        simulation: {};
        distance: {};
    };
    selectBagList: string[];
    currentReplayBagInfo: IReplayBagInfo | null;
    playingStateHook: boolean;
    filterTopicList:
        | {
              filter: boolean;
              name: string;
              topic: string;
              type: string;
          }[]
        | null;
}
