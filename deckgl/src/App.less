@import "./assets/font/index.less";
body,
html,
#root {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}
p {
    margin: 0 !important;
    padding: 0 !important;
}
.App {
    width: 100%;
    height: 100%;
}

.mapboxgl-ctrl-logo {
    display: none !important;
}
.layout-box {
    width: 100%;
    height: 100%;
    background: #fff;
    position: absolute;
    left: 0;
    top: 0;
    /* z-index: 1003; */
}
.drag {
    /* position: absolute;
    right: 10px;
    top: 10px;
    z-index: 1002; */
    max-width: 600px; /* 设置最大宽度限制 */
    max-height: 400px; /* 设置最大高度限制 */
    padding: 10px;
    border: 1px solid #f90;
    resize: both; /* 设置元素的宽度和高度均可调整 */
    overflow: auto;
}
button:focus {
    outline: none !important;
}

.ant-empty-normal {
    display: flex;
    justify-content: center;
    width: 100vw;
    flex-direction: column;
}
@font-face {
    font-family: "SourceHanSansCNMedium";
    src: url(./assets/font/SourceHanSansCNMedium.otf);
}
.red {
    color: #ff4d50;
}
.green {
    color: #7be05e;
}
