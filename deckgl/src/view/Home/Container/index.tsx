/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-23 15:25:45
 * @LastEditors: luofei <EMAIL>
 * @FilePath: /deckgl/src/view/Home/Container/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import InfoPanel from "@/components/InfoPanel";
import ReplayControls from "@/components/ReplayControls";
import RosPlayer from "@/components/RosPlayer";
import { PlayerContainerView } from "@/utils/enum";
import Draggable from "react-draggable";
import Core3DViewer from "@/components/Core3DViewer";
import "./index.less";
import { useTranslation } from "react-i18next";
function Header({
    type,
    setViewType,
}: {
    type: string;
    setViewType: (type: string) => void;
}) {
    const { t } = useTranslation("enum");
    return (
        <div className="box-header">
            {Object.keys(PlayerContainerView).map((state, index) => {
                if (state == type) {
                    return null;
                }
                return (
                    <i
                        key={index}
                        title={t(PlayerContainerView[state].title)}
                        className={`iconfont ${PlayerContainerView[state].iconfont}`}
                        onClick={() => setViewType(state)}
                    ></i>
                );
            })}
        </div>
    );
}

function App({
    isShowReplayContent,
    pageType,
    changeContinerContant,
}: {
    isShowReplayContent: boolean;
    pageType: string;
    changeContinerContant: any;
}) {
    const setViewType = (type: string) => {
        changeContinerContant(type);
    };

    return (
        <div className="container">
            <div className="base-layer">
                <div className="deck-view">
                    <Core3DViewer></Core3DViewer>
                </div>
                {/* 点云 */}
                {pageType !== "MINIMIZE" && (
                    <Draggable
                        axis={pageType == "FLOAT" ? "both" : "none"}
                        handle=".box-header"
                    >
                        <div
                            className={`point-cloud-view ${
                                pageType === "FLOAT" ? "float-dom" : ""
                            }`}
                            style={{
                                width:
                                    PlayerContainerView[pageType].deckWidth ||
                                    "50%",
                            }}
                        >
                            <Header type={pageType} setViewType={setViewType} />
                            <RosPlayer
                                display={isShowReplayContent}
                                pageType={pageType}
                            ></RosPlayer>
                            {/* <PointCloud data={data} /> */}
                        </div>
                    </Draggable>
                )}
            </div>
            {/* 第二层 z-index 10 */}
            <InfoPanel />
            {/* 第三层 z-index 20 */}
            {isShowReplayContent && <ReplayControls></ReplayControls>}
        </div>
    );
}
export default App;
