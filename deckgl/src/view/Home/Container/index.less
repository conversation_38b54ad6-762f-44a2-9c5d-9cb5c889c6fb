@import "@/theme/mixin.less";

.point-cloud-view {
    width: 100%;
    height: 100%;
    background: @point-cloud-bg;
    position: relative;
    overflow: hidden;
    .box-header {
        position: absolute;
        width: 100%;
        height: 20px;
        text-align: right;
        border-radius: 10px 10px 0 0;
        line-height: 20px;
        z-index: 2;
        .iconfont {
            margin-right: 10px;
            color: #605e5f;
            border-radius: 50%;
            font-size: 12px;
            padding: 2px;
            background-color: #fdbc2f;
        }
        .iconfont:first-child {
            background-color: #ff6057;
        }
        .iconfont:last-child {
            background-color: #28c940;
        }
        .box-handle {
            width: 20%;
            height: 3px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background-color: #0a07e8;
        }
    }

    #ros {
        width: 100%;
        height: 100%;
    }
}
.float-dom {
    position: absolute !important;
    width: 30% !important;
    height: 40% !important;
    right: 2%;
    bottom: 10%;
    box-shadow: @panel-bs-color;
    border-radius: 12px;
    backdrop-filter: blur(18px);
    .box-header {
        background-color: #615e5e;
    }
}
