@import "@/theme/mixin.less";

.home {
    width: 100%;
    height: 100%;
    display: flex;
    background-color: @player-container-bg-color;
    .menu {
        width: 50px;
        height: 100%;
        border-left: 1px solid @player-container-bd-color;
        background-color: @player-container-bg-color;
        z-index: 10;
        .tabs {
            width: 100%;
            color: #000;
            height: 100%;
            .ant-tabs-nav {
                width: 100%;
                padding-top: 20px;
                min-width: 0;

                .ant-tabs-nav-list {
                    .ant-tabs-tab {
                        padding: 0;
                        .ant-tabs-tab-btn {
                            width: 100%;
                            font-size: 14px;
                            display: flex;
                            justify-content: center;
                            .iconfont {
                                color: @config-title-font-color;
                            }
                        }
                        &:nth-last-child(5) {
                            margin-top: auto;
                        }
                        // 过滤掉竖线元素
                        &:nth-last-child(2) {
                            margin-bottom: 20px;
                        }
                    }
                    .ant-tabs-tab-active {
                        .iconfont {
                            color: #0056ff !important;
                        }
                    }
                    > .ant-tabs-ink-bar {
                        left: 95%;
                    }
                }
            }
            .ant-tabs-content-holder {
                display: none;
            }
        }
    }
    .mosaic-page {
        width: 320px;
        overflow-y: hidden;
        z-index: 10;
        background-color: @player-container-bg-color;
    }

    .container {
        height: 100%;
        flex: 1;
        display: flex;
        position: relative;
        overflow: hidden;
        .base-layer {
            width: 100%;
            height: 100%;
            display: flex;
            .deck-view,
            .point-cloud-view {
                position: relative;
                width: 50%;
                height: 100%;
            }
            .deck-view {
                flex: 1;
            }
        }
    }
}
