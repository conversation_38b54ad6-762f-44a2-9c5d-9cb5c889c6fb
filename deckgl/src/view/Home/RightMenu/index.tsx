/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> lish<PERSON>@trunk.tech
 * @Date: 2023-03-23 14:53:45
 * @LastEditors: fanmx <EMAIL>
 * @FilePath: \deckgl\src\view\Home\RightMenu\index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { Tabs } from "antd";
import { useState } from "react";
import { useI18n } from "../../../i18n/provider";
import { useTranslation } from "react-i18next";
interface Menu {
    icon: string;
    title: string;
    key: string;
    isChange?: boolean;
}
function App({
    menuList,
    changeToolsPage,
}: {
    menuList: Menu[];
    changeToolsPage: any;
}) {
    const [tilePageType, changeTilePageType] = useState("");
    const { currentLang } = useI18n();
    const { t } = useTranslation("menu");
    return (
        <Tabs
            className="tabs"
            activeKey={tilePageType}
            tabPosition={"right"}
            items={menuList.map((item) => {
                return {
                    label: (
                        <span
                            className={`iconfont ${
                                !item?.isChange && item.icon
                            } ${
                                item?.isChange && currentLang == "en-US"
                                    ? "icon-yingwen1"
                                    : "icon-zhongwen1"
                            }`}
                            title={t(item.title)}
                        ></span>
                    ),
                    key: item.key,
                };
            })}
            onTabClick={changeToolsPage}
        />
    );
}
export default App;
