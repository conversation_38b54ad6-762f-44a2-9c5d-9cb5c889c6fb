/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> l<PERSON>@trunk.tech
 * @Date: 2023-03-17 10:42:00
 * @LastEditors: jack <EMAIL>
 * @FilePath: /deckgl/src/view/Home/MosaicTile/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import ConfigPage from "@/components/ConfigPage";
import ReplayDataPage from "@/components/ReplayDataPage";
function MosaicTile({ type }: { type: string }) {
    const getPageByType = (type: string) => {
        switch (type) {
            case "SETTING":
                return <ConfigPage />;
            case "REPLAYDATA":
                return <ReplayDataPage />;
            default:
                return <></>;
        }
    };
    return <>{getPageByType(type)}</>;
}
export default MosaicTile;
