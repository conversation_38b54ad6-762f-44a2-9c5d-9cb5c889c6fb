/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-08 16:31:32
 * @LastEditors: fanmx <EMAIL>
 * @FilePath: \deckgl\src\view\Home\index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { useEffect, useState } from "react";
import "./index.less";
import { isMobile } from "@/utils/index";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/app/store";
import Version from "@/components/Version";
import MosaicTile from "@/view/Home/MosaicTile";
import RightMenu from "@/view/Home/RightMenu";
import Container from "@/view/Home/Container";
import Console from "@/components/Console";
import { Modal } from "antd";
import Loading from "@/components/Loading";
import { defaultMenuList } from "@/utils/enum";
import {
    setSettings,
    setOperation,
    setCurrentReplayBagInfo,
    setConfig,
    setMapList,
} from "@/features/dataSlice";
import { copyText } from "@/utils";
import { clearSelectBag } from "@/request/bag";
import { useI18n } from "../../i18n/provider";
import { useTranslation } from "react-i18next";
let checkMenuDisplayForParentApp = false;

function PlayerContainer() {
    const { t } = useTranslation(["message", "common"]);
    const dispatch = useDispatch();
    // 点云界面
    const [showReplay, changeShowReplay] = useState(false);
    const [showViewType, setShowViewType] = useState("MINIMIZE");
    const [menuList, setMenuList] = useState(defaultMenuList);
    const [displayVersion, changeDisplayVersion] = useState(false);
    const config = useSelector((state: RootState) => state.dataReducer.config);
    const operation = useSelector((state: any) => state.dataReducer.operation);
    const settings = useSelector((state: any) => state.dataReducer.settings);
    const state = useSelector((state: any) => state.dataReducer.status);
    const currentReplayBagInfo = useSelector(
        (state: any) => state.dataReducer.currentReplayBagInfo
    );
    const { connectConfig, parentApp, mapConfig, language } = config;
    const [titlePageType, changeTitlePageType] = useState("");
    const [displayMenu, changeDisplayMenu] = useState(true);
    const [modal, contextHolder] = Modal.useModal();
    const { currentLang, changeLang } = useI18n();
    /**
     * @description: 菜单交互
     * @param {any} val
     * @return {*}
     */
    const changeToolsPage = async (val: any) => {
        //清空缓存
        if (val == "CLEARCACHE") {
            return modal.confirm({
                width: 300,
                title: t("clearCache"), //"是否确定清空缓存？",
                content: "",
                okText: t("common:ok"), //"确认",
                cancelText: t("common:cancel"), // "取消",
                maskClosable: false,
                closable: false,
                icon: null,
                wrapClassName: `speech-modal ${config.theme}`,
                onOk() {
                    console.log("OK");
                    window.localStorage.clear();
                    dispatch(setOperation({ clearCache: true }));
                },
                onCancel() {
                    console.log("Cancel");
                },
            });
        }
        //标定
        if (val == "CALIBRATION") {
            // navigate("/Calibration");
            window.open("/#/Calibration");
            return;
        }
        //版本信息
        if (val == "VERSION") {
            changeDisplayVersion(true);
            return;
        }
        //点云
        if (val == "POINTCLOUD") {
            changeContinerContant("CUSTOM");
            return;
        }
        // 分享
        if (val == "SHARE") {
            const url =
                "http://" +
                window.location.host +
                "?config=" +
                JSON.stringify(config);
            copyText(url);

            window.open(url);

            return;
        }
        //设置
        if (val == "SETTING") {
            dispatch(
                setOperation({
                    display: {
                        type: val,
                        flag: titlePageType == val ? false : true,
                    },
                })
            );
        }
        //中英文切换
        if (val == "LANGUAGE") {
            const newLang = currentLang === "zh-CN" ? "en-US" : "zh-CN";
            changeLang(newLang);
            dispatch(
                setConfig({
                    ...config,
                    language: newLang,
                })
            );
            return;
        }
        if (val == titlePageType) {
            changeTitlePageType("");
        } else {
            changeTitlePageType(val);
        }
    };
    /**
     * @description: 获取bag回放列表
     * @return {*}
     */
    // const initReplayBag = async () => {
    //     try {
    //         await clearSelectBag(connectConfig.ip);
    //     } catch (error) {
    //         changeShowReplay(false);
    //         return;
    //     }
    //     const listRes: any = await getBags(connectConfig.ip);
    //     const list = listRes?.results?.items;
    //     if (list?.length) {
    //         // 去让react一次更新
    //         dispatch(setReplayBagList(list));
    //         // 回放组件
    //         changeShowReplay(true);
    //         const viewType =
    //             localStorage.getItem("pointCloudViewType") || "MINIMIZE";
    //         changeContinerContant(viewType);
    //     }
    // };
    useEffect(() => {
        const ip = connectConfig.ip;
        if (parentApp && !checkMenuDisplayForParentApp) {
            checkMenuDisplayForParentApp = true;
            changeDisplayMenu(false);
            setMenuList(defaultMenuList.slice(2));
        }
        if (!ip || parentApp || isMobile()) {
            return;
        }
        clearSelectBag(ip, connectConfig.apiPort);
        dispatch(setCurrentReplayBagInfo(""));
        //  initReplayBag();
    }, [connectConfig, parentApp]);

    /**
     * @description: 监听外层应用发送改变菜单指令
     * @return {*}
     */
    useEffect(() => {
        let { type, flag, isParent } = operation?.display;
        if (!type || !isParent) return;
        switch (type) {
            case "SETTING":
                changeTitlePageType(flag ? type : "");
                changeDisplayMenu(flag ? true : false);
                break;
            case "VERSION":
                changeDisplayVersion(true);
                break;
            default:
                break;
        }
    }, [operation?.display]);

    const changeContinerContant = (type: string) => {
        // 菜单默认所有按钮都展示
        // let result: menu[] = replayMenuList;
        // if (type === "MINIMIZE") {
        //     result = result.concat(pointCloudMenuList);
        // }
        localStorage.setItem("pointCloudViewType", type);
        setShowViewType(type);
        // setMenuList([...result, ...defaultMenuList]);
    };
    const handleClearSelectBag = () => {
        clearSelectBag(connectConfig.ip, connectConfig.apiPort);
    };
    const setSettingsFn = (key: string, value: any = null) => {
        //  补充联动逻辑
        let obj = {} as any;
        if (key === "measure") {
            if (!settings.measure) {
                obj.viewMode = "TOP_DOWN";
                value = "line";
            } else {
                obj.viewMode = "PERSPECTIVE";
            }
        }
        dispatch(
            setSettings({
                ...settings,
                [key]: value || !settings[key],
                ...obj,
            })
        );
    };
    useEffect(() => {
        const handleKeyDown = (e: any) => {
            if (e.ctrlKey) {
                //  组合键
                // ctrl + m 键
                if (e.keyCode == 77) {
                    changeDisplayMenu((value) => {
                        if (value) {
                            changeTitlePageType("");
                            dispatch(
                                setOperation({
                                    display: {
                                        type: "SETTING",
                                        flag: false,
                                    },
                                })
                            );
                        }
                        return !value;
                    });
                }
            } else {
                // 单键
                switch (e.key) {
                    // ctrl + l 键
                    case "c":
                        setSettingsFn("console");
                        break;
                    case "l":
                        setSettingsFn("lock");
                        break;
                    case "s":
                        setSettingsFn("satelliteMap");
                        break;
                    case "r":
                        setSettingsFn("reset");
                        break;
                    case "d":
                        setSettingsFn("debug");
                        break;
                    case "h":
                        setSettingsFn("legend");
                        break;
                    case "m":
                        setSettingsFn("measure");
                        break;
                    case "2":
                        setSettingsFn("viewMode", "TOP_DOWN");
                        break;
                    case "3":
                        setSettingsFn("viewMode", "PERSPECTIVE");
                        break;
                    case "o":
                        setSettingsFn("modelVisible");

                    default:
                        break;
                }
            }
        };
        window.addEventListener("keydown", handleKeyDown);
        return () => {
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, [settings, displayMenu]);

    useEffect(() => {
        //界面刷新时调用clear接口
        window.addEventListener("beforeunload", handleClearSelectBag);
        return () => {
            window.removeEventListener("beforeunload", handleClearSelectBag);
        };
    }, []);

    // 动态加载地图配置
    useEffect(() => {
        fetch("/geojson/map_config.json?v=" + Date.now())
            .then((res) => res.json())
            .then((data) => {
                dispatch(setMapList(data));
            })
            .catch((error) => {
                console.error("加载地图配置失败:", error);
            });
    }, [dispatch]);

    useEffect(() => {
        if (language) {
            changeLang(language || "zh-CN");
            dispatch(
                setConfig({
                    ...config,
                    language,
                })
            );
        }
    }, [language]);

    return (
        <div className={`home ${config.theme}`}>
            {state?.loading ? <Loading /> : <></>}
            {/* showReplay && */}
            <Container
                isShowReplayContent={Boolean(
                    currentReplayBagInfo?.bag_size &&
                        !parentApp &&
                        (!isMobile() as boolean)
                )}
                pageType={showViewType}
                changeContinerContant={changeContinerContant}
            />
            <div
                className="mosaic-page"
                style={{ display: titlePageType ? "block" : "none" }}
            >
                <MosaicTile type={titlePageType}></MosaicTile>
            </div>
            <div
                className="menu"
                style={{
                    display: displayMenu ? "block" : "none",
                }}
            >
                <RightMenu
                    menuList={menuList}
                    changeToolsPage={changeToolsPage}
                />
            </div>
            <Version
                displayModal={displayVersion}
                changeDisplayVersion={changeDisplayVersion}
            />
            <Console isOpen={settings.console}></Console>
            {contextHolder}
        </div>
    );
}
export default PlayerContainer;
