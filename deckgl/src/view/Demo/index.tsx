/*
 * @Author: jack <EMAIL>
 * @Date: 2023-04-03 17:00:08
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2023-06-21 14:53:49
 * @FilePath: /deckgl/src/view/Demo/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { useEffect, useMemo, useState } from "react";
import DeckGL, {
    GeoJsonLayer,
    PointCloudLayer,
    PolygonLayer,
    ScenegraphLayer,
    AmbientLight,
    DirectionalLight,
    PointLight,
    LightingEffect,
    LayersList,
    COORDINATE_SYSTEM,
} from "deck.gl";
import { Map } from "react-map-gl";

import demojson from "./demo.json";
const MAPBOX_ACCESS_TOKEN =
    "pk.eyJ1IjoibHVvZmVpIiwiYSI6ImNrY3gwanpldzBkcTIycXF6d2FmYmF0bzYifQ.VecZ41O_HKtxU4JSQUXlyw";

// 随机点
const generatePoint = (
    center: [number, number],
    radius: number,
    count: number
) => {
    const points = [];
    for (let i = 0; i < count; i++) {
        let theta = Math.random() * 2 * Math.PI;
        let r = Math.random() * radius;
        let x = center[0] + r * Math.cos(theta);
        let y = center[1] + r * Math.sin(theta);
        points.push({
            position: [x, y, Math.floor(Math.random() * 20)],
            color: [
                Math.random() * 255,
                Math.random() * 255,
                Math.random() * 255,
            ],
        });
    }
    return points;
};

const generatePointF32 = (
    center: [number, number],
    radius: number,
    count: number
) => {
    const points = new Float32Array(count * 3); // 5 是因为每个点有5个数据

    for (let i = 0; i < count; i++) {
        let theta = Math.random() * 2 * Math.PI;
        let r = Math.random() * radius;
        let x = center[0] + r * Math.cos(theta);
        let y = center[1] + r * Math.sin(theta);

        points[i * 3] = x; // 存储x坐标
        points[i * 3 + 1] = y; // 存储y坐标
        points[i * 3 + 2] = 0; // 存储z坐标
    }

    return { src: points, length: points.length / 3 };
};

// 随机矩形
const generateRect = (count: number = 10000) => {
    const rects = [];

    for (let i = 0; i < count; i++) {
        // 随机矩形半宽和半高,范围5-10度
        const hw = (Math.random() * 1 + 1) / 2;
        const hh = (Math.random() * 1 + 1) / 2;

        // 随机中心点经纬度,范围-180到180度
        const lon = Math.random() * 360 - 180;
        const lat = Math.random() * 360 - 180;

        // 根据中心点和半宽高计算4个点的坐标
        const p1 = [lon - hw, lat - hh];
        const p2 = [lon - hw, lat + hh];
        const p3 = [lon + hw, lat + hh];
        const p4 = [lon + hw, lat - hh];

        // 添加矩形的4个点
        rects.push([p1, p2, p3, p4, p1]);
    }

    return rects;
};

export default function App() {
    const [viewState, setViewState] = useState({
        longitude: -123.0249569,
        latitude: 49.240719,
        zoom: 20,
        pitch: 0,
        bearing: 0,
        maxPitch: 85,
    });
    const [points, setPoints] = useState(generatePoint([0, 0], 100, 10000));
    const [rects, setRects] = useState(generateRect(10000));
    const [position, setPosition] = useState([-123.0249569, 49.240719, 0]);
    // console.log(generateRect([0, 0], 10000, 100000));
    const data = [
        {
            position: [-123.0249569, 49.240719, 0],
            heading: 90,
        },
    ];
    // light
    const ambientLight = new AmbientLight({
        color: [255, 255, 255],
        intensity: 2.0,
    });
    const dirLight = new DirectionalLight({
        color: [0, 0, 255],
        intensity: 2,
        direction: [-1, -1, -1],
    });
    const pointLight = new PointLight({
        color: [0, 255, 0],
        intensity: 0.001,
        position: [-123.0249569, 50.240719, 10000],
    });
    const lightingeffect = new LightingEffect({
        ambientLight,
        dirLight,
        pointLight,
    });

    const layers = useMemo(() => {
        return [
            new ScenegraphLayer({
                id: "glb-layer",
                data: data,
                pickable: true,
                scenegraph: "glb/truck_all.glb",
                getPosition: [0, 0, 0],
                getOrientation: (d) => [0, d.heading + 0.7, 90],
                getScale: [1.4, 1.4, 1.4],
                getTranslation: [0, 0, 0],
                sizeScale: 10,
                _lighting: "pbr",
                coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                coordinateOrigin: position,
            }),
            new PointCloudLayer({
                id: "point-cloud-layer",
                data: points,
                coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                coordinateOrigin: position,
                getColor: (d) => [255, 0, 255, 255],
                sizeUnits: "meters",
                pointSize: 0.1,
                metetial: true,
                // getPosition: (object, { index, data }) => {
                //     console.log(data);
                //     return data.src.subarray(index * 3, index * 3 + 3);
                // },
            }),
            new PolygonLayer({
                id: "polygon-layer",
                data: rects,
                coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                coordinateOrigin: position,
                stroked: false,
                filled: true,
                extruded: true,
                lineWidthMinPixels: 2,
                getLineColor: [255, 100, 0],
                getFillColor: [200, 0, 0, 100],
                getLineWidth: 1,
                getElevation: (d) => {
                    return Math.random() * 10;
                },
                getPolygon: (d) => d,
            }),

            // new GeoJsonLayer({
            //     id: "geojson-layer",
            //     data: demojson,
            //     stroked: false,
            //     filled: true,
            //     extruded: true,
            //     lineWidthMinPixels: 2,
            //     getLineColor: [255, 100, 100],
            //     getFillColor: [200, 160, 0, 180],
            //     getRadius: 1,
            //     getLineWidth: 1,
            //     getElevation: 30,
            // })
        ];
    }, [points, rects, position]);

    useEffect(() => {
        const radomNum = Math.floor(Math.random() * 10);
        // const pointsTimer = setInterval(() => {
        //     setPoints(generatePoint([0, 0], 100, 1000));
        // });
        const rectsTimer = setInterval(() => {
            setRects(generateRect(10));
        }, 100);
        const positionTimer = setInterval(() => {
            setPosition((state) => {
                return [state[0], state[1] - 0.000001];
            });
        }, 100);
        // animate();

        return () => {
            // clearInterval(pointsTimer);
            clearInterval(rectsTimer);
            clearInterval(positionTimer);
        };
    }, []);

    const animate = () => {
        requestAnimationFrame(animate);
        // console.log("animate");
        // setPoints(generatePoint([0, 0], 100, 10000));
        // setRects(generateRect(10000));
        setPosition((state) => {
            return [state[0], state[1] - 0.000001];
        });
    };

    return (
        <div onContextMenu={(evt) => evt.preventDefault()}>
            <DeckGL
                initialViewState={viewState}
                controller={true}
                layers={layers}
                effects={[lightingeffect]}
                _pickable={false}
            >
                {/* <Map
                    mapboxAccessToken={MAPBOX_ACCESS_TOKEN}
                    mapStyle={"mapbox://styles/mapbox/dark-v10"}
                    maxZoom={viewState.maxZoom}
                    maxPitch={viewState.maxPitch}
                ></Map> */}
            </DeckGL>
        </div>
    );
}
