/*
 * @Author: jack <EMAIL>
 * @Date: 2023-06-21 14:54:53
 * @LastEditors: luofei <EMAIL>
 * @LastEditTime: 2024-03-14 15:40:16
 * @FilePath: /deckgl/src/view/Demo/boby.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import DeckGL, { ScenegraphLayer, TextLayer } from "deck.gl";
const creatData = () => {
    const data = [];
    for (let i = 0; i < 200; i++) {
        data.push({
            position: [
                120.12 + Math.random() * 0.01,
                30.27 + Math.random() * 0.01,
            ],
            orientation: [0, 0, 90],
            text: i,
        });
    }
    return data;
};
const data = creatData();
export default function App() {
    const viewState = {
        latitude: 30.27 + Math.random() * 0.01,
        longitude: 120.12 + Math.random() * 0.01,
        zoom: 15,
        pitch: 0,
        bearing: 0,
        maxZoom: 25,
    };

    const layers = [
        // glbs
        new ScenegraphLayer({
            id: "glbs",
            data: creatData(),
            scenegraph: "/glb/demo.glb",
            getPosition: (d) => d.position,
            getOrientation: (d) => d.orientation,
            // getScale: [10, 10, 10],
            pickable: false,
            _lighting: "pbr",
            onHover: ({ object }) => {
                console.log(object);
            },
            loadOptions: {
                draco: {
                    workerUrl: "/draco/draco_worker.js",
                },
            },
            getScene: (scenegraph) => {
                console.log(
                    scenegraph && scenegraph.scenes
                        ? scenegraph.scenes[0].children[0].children[0]
                        : scenegraph
                );
                return scenegraph && scenegraph.scenes
                    ? scenegraph.scenes[0]
                    : scenegraph;
            },
        }),
        new TextLayer({
            id: "text",
            data: data,
            getPosition: (d) => [...d.position, 10],
            getText: (d) => d.text,
            getSize: 16,
            getColor: [255, 255, 255],
            getAngle: 0,
        }),
    ];

    return (
        <>
            <DeckGL
                style={{
                    background: "#000",
                }}
                initialViewState={viewState}
                layers={layers}
                controller={true}
                onViewStateChange={(info) => {
                    // console.log(info);
                }}
            ></DeckGL>
        </>
    );
}
