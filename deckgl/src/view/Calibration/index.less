@import "@/theme/mixin.less";
.calibration {
    width: 100%;
    height: 100%;
    position: relative;
    background: #000;
    position: relative;
    div:first-child {
        z-index: 101;
    }
    .reset-box {
        width: 245px;
        height: 120px;
        position: absolute;
        top: 20px;
        right: 15px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-end;
        border-radius: 3px;
        border: 1px solid #424242;
        padding: 10px;
        .icon-setting,
        .icon-bangzhuyushuoming {
            font-size: 25px;
            color: #fff;
            cursor: pointer;
            z-index: 101;
            margin-bottom: 5px;
        }
        .icon-bangzhuyushuoming {
            font-size: 30px;
            margin-top: 5px;
        }
        .prot {
            position: absolute;
            top: 125px;
            right: 0;
            z-index: 1000;
            width: 100%;
        }
    }
}
.calibration-help {
    .ant-modal-content {
        padding: 0;
        .anticon-info-circle {
            display: none;
        }
        .ant-modal-confirm-content {
            // height: 400px;
        }
        .help-img {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }
    }
    .ant-modal-confirm-btns {
        display: none;
    }
}
.lil-gui.root
    .children
    .lil-gui
    .children
    .controller:first-child
    .widget
    input {
    width: 100%;
    height: 100%;
}
.lil-gui.autoPlace {
    top: 185px;
    border-radius: 3px;
    border: 1px solid #424242;
}
