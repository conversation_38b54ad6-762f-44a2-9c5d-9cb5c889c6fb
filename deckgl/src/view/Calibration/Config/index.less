@import "@/theme/mixin.less";
.version-btn {
    position: fixed;
    right: 10px;
    top: 21.5%;
    width: 50px;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    border: none !important;
    i {
        font-size: 18px;
        line-height: 16px;
        font-weight: bolder;
    }
}
.contents {
    max-height: 500px;
    overflow-y: auto;
    padding: 20px 10px;
}
.cailbratopn-dialog {
    .ant-modal-header {
        background: transparent;
        .ant-modal-title {
            color: @panel-font-color;
        }
    }

    .ant-modal-content {
        background: @panel-bg-color;
        box-shadow: @panel-bs-color;
        backdrop-filter: blur(18px);
        border-radius: 4px;
        .ant-modal-close {
            color: @panel-font-color;
        }
        .markdown-body {
            color: @panel-font-color;
        }
    }
}
