/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2023-02-01 19:25:52
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2023-07-24 16:03:47
 * @FilePath: /deckgl/src/view/Calibration/Config/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { useState, useEffect } from "react";
import { Modal } from "antd";
import "./index.less";
import { Form, Select, Switch } from "antd";
const { Option } = Select;

function Config({
    displayModal,
    param,
    changeDisplayConfig,
}: {
    displayModal: boolean;
    param: any;
    changeDisplayConfig: any;
}) {
    const [form] = Form.useForm();
    const [isOnline, setIsOnline] = useState<boolean>(false);
    /**
     * @description: 取消提交
     * @return {*}
     */
    const handleCancel = () => {
        form.resetFields();
        changeDisplayConfig(false);
    };
    /**
     * @description: 确定提交
     * @return {*}
     */
    const handleOk = () => {
        form.validateFields()
            .then((value) => {
                changeDisplayConfig(false, value);
            })
            .catch((err) => {
                console.log("error", err);
            });
    };
    /**
     * @description: 在线标定切换
     * @param {boolean} val
     * @return {*}
     */
    const handleSwitchChange = (val: boolean) => {
        setIsOnline(val);
    };
    return (
        <Modal
            title="雷达配置"
            maskClosable={true}
            open={displayModal}
            onCancel={handleCancel}
            onOk={handleOk}
            closable={false}
            centered
            okText={"确定"}
            cancelText={"取消"}
            className="version-cailbratopn light"
        >
            <div className="contents">
                <Form
                    form={form}
                    name="basic"
                    labelCol={{ span: 6 }}
                    style={{ maxWidth: 600 }}
                    initialValues={{ remember: true }}
                    className="config-form"
                >
                    <Form.Item
                        label="是否在线"
                        name="isOnline"
                        valuePropName="checked"
                        initialValue={false}
                    >
                        <Switch
                            checkedChildren="开"
                            unCheckedChildren="关"
                            onChange={handleSwitchChange}
                        />
                    </Form.Item>
                    <Form.Item
                        label="雷达配置文件"
                        name="lidarConfig"
                        validateTrigger={["onBlur", "onChange"]}
                        rules={[
                            {
                                required: true,
                                message: "请选择lidarConfig",
                            },
                        ]}
                    >
                        <Select placeholder="请选择雷达配置文件" allowClear>
                            {param?.lidarConfig?.map((item: any) => {
                                return (
                                    <Option key={item} value={item}>
                                        {item}
                                    </Option>
                                );
                            })}
                        </Select>
                    </Form.Item>
                    <Form.Item
                        label="urdf文件路径"
                        name="urdfPath"
                        validateTrigger={["onBlur", "onChange"]}
                        rules={[
                            {
                                required: true,
                                message: "请选择urdf路径",
                            },
                        ]}
                    >
                        <Select placeholder="请选择urdf路径" allowClear>
                            {param?.urdfPath?.map((item: any) => {
                                return (
                                    <Option key={item} value={item}>
                                        {item}
                                    </Option>
                                );
                            })}
                        </Select>
                    </Form.Item>
                    {!isOnline ? (
                        <>
                            <Form.Item
                                label="Tf来源"
                                name="tfFrom"
                                validateTrigger={["onBlur", "onChange"]}
                                rules={[
                                    {
                                        required: true,
                                        message: "请选择tf来源",
                                    },
                                ]}
                            >
                                <Select
                                    placeholder="请选择tf来源"
                                    allowClear
                                    options={[
                                        {
                                            value: "1",
                                            label: "bag包",
                                        },
                                        {
                                            value: "2",
                                            label: "urdf文件",
                                        },
                                    ]}
                                ></Select>
                            </Form.Item>
                            <Form.Item
                                label="Bag包路径"
                                name="bagPath"
                                validateTrigger={["onBlur", "onChange"]}
                                rules={[
                                    {
                                        required: true,
                                        message: "请选择bag路径",
                                    },
                                ]}
                            >
                                <Select placeholder="请选择bag路径" allowClear>
                                    {param?.bagPath?.map((item: any) => {
                                        return (
                                            <Option key={item} value={item}>
                                                {item}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            </Form.Item>
                        </>
                    ) : (
                        ""
                    )}
                </Form>
            </div>
        </Modal>
    );
}

export default Config;
