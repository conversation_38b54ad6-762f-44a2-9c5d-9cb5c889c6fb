/*
 * @Author: luofei luofei.trunk.tech
 * @Date: 2023-03-23 10:49:57
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2023-06-06 14:21:06
 * @FilePath: /deckgl/src/view/Calibration/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import "./index.less";
import { useEffect, useState } from "react";
import { load } from "protobufjs";
import axios from "axios";
import PointCloud from "@/components/PointCloud/index";
import { fillArray, throttle } from "@/utils/index";
import GUI from "lil-gui";
import { message as antdMessage, Modal, Input } from "antd";
import AxisHelpImg from "@/assets/img/axisHelp.png";
import Config from "./Config";

const colorList = {
    0: "#ff3d3d",
    1: "#3dff3d",
    2: "#3d3dff",
    3: "#ffff3d",
    4: "#3dffff",
};
const code = {
    0: "Unknown",
    1000: "操作成功",
    2001: "操作失败",
    2002: "操作超时",
    2003: "参数非法",
    2004: "mode异常",
    2005: "上一次标定未结束",
};
let gui: any = null;
let isLoading = false;
function Calibration() {
    const [data, setData] = useState<any>([]);
    const [newData, setNewData] = useState<any>([]);
    const [pbRoot, setPbRoot] = useState(null);
    const [displayConfig, setDisplayConfig] = useState(false);
    const [param, setParam] = useState(null);
    const [prot, setProt] = useState(localStorage.calibrationIP || "9020");

    // const baseUrl = "http://**********:" + prot;
    const baseUrl = "http://" + location.hostname + ":" + prot;
    useEffect(() => {
        init();
    }, []);
    useEffect(() => {
        init();
        localStorage.calibrationIP = prot;
    }, [prot]);
    useEffect(() => {
        if (data.length > 0) {
            if (gui) gui.destroy();
            gui = new GUI();
            data.map((item: any, index: number) => {
                const folder = gui.addFolder(item.name);
                folder.add(item, "visible");
                folder.add(item.translation, "x").step(0.01);
                folder.add(item.translation, "y").step(0.01);
                folder.add(item.translation, "z").step(0.01);
                folder.add(item.rotation, "roll").step(0.01);
                folder.add(item.rotation, "pitch").step(0.01);
                folder.add(item.rotation, "heading").step(0.01);
                folder.addColor(item, "color");
                if (index) folder.close();
                // folder.show(folder._hidden);
            });
            const buttons = {
                1: () => {
                    chage.disable(!isLoading);
                    const pcl = data.map((item: any) => {
                        return {
                            translation: item.translation,
                            rpy: item.rotation,
                            childId: item.name,
                        };
                    });
                    setCommand({ mode: 1, tf: pcl }, pbRoot);
                },
                2: () => {
                    seva.disable(!isLoading);
                    if (isLoading) {
                        antdMessage.info("正在标定中，请稍后");
                    }
                    setCommand({ mode: 2 }, pbRoot);
                },
            };
            let chage = gui.add(buttons, "1").name("微调").disable(isLoading); // Button
            let seva = gui.add(buttons, "2").name("保存").disable(isLoading); // Button

            gui.onChange(() => {
                // 修改参数
                setNewData([...data]);
            });
        }
        setNewData(data);
    }, [data]);

    const init = () => {
        load("/protobuf/calibration.proto", (err, root: any) => {
            if (err) {
                console.log(err);
                return;
            }
            setPbRoot(root);
            handleGetParams(root);
        });
        return () => {
            if (gui) gui.destroy();
        };
    };
    /**
     * @description: 获取参数
     * @return {*}
     */
    const handleGetParams = (root: any) => {
        console.log("请求");
        const calibrate = root?.lookupType("trunk.msgs.CalibrationParamInfo");
        axios
            .get(baseUrl + "/api/calibration/param/", {
                responseType: "arraybuffer",
            })
            .then((res) => {
                const data = calibrate?.decode(new Uint8Array(res.data));
                setParam(data);
                setDisplayConfig(true);
            })
            .catch((err) => {
                console.log("暂无点云数据无法进行标注！");
            });
    };
    /**
     * @description: 设置参数
     * @param {any} root
     * @return {*}
     */
    const handleSetParams = (val: object) => {
        if (!pbRoot) alert("pb加载异常");
        const calibrate = pbRoot?.lookupType("trunk.msgs.CalibrationSetParam");
        const message = calibrate?.create(val);
        const buffer = calibrate?.encode(message).finish();
        let blob = new Blob([buffer], { type: "buffer" });
        axios
            .put(baseUrl + "/api/calibration/param/", blob, {
                responseType: "arraybuffer",
            })
            .then((res) => {
                const data = calibrate?.decode(new Uint8Array(res.data));
                setCommand(
                    {
                        mode: 0,
                    },
                    pbRoot
                );
            })
            .catch((err) => {
                console.log("暂无点云数据无法进行标注！");
            });
    };
    const setCommand = (data: any, root: any) => {
        if (isLoading) return;
        isLoading = true;
        const calibrate = root?.lookupType("trunk.msgs.CalibrationRequest");
        const reply = root?.lookupType("trunk.msgs.CalibrationResponse");
        const message = calibrate?.create(data);
        const buffer = calibrate?.encode(message).finish();
        let blob = new Blob([buffer], { type: "buffer" });
        if (data.mode === 0) {
            getPointCloud(root);
        }
        // decode
        // 添加loading
        if (data.mode === 2) {
            antdMessage.loading("正在保存中...");
        } else {
            antdMessage.loading("正在标定中...");
        }
        axios
            .post(baseUrl + "/api/calibration/command/", blob, {
                responseType: "arraybuffer",
            })
            .then((res) => {
                const ms = reply?.decode(new Uint8Array(res.data));
                isLoading = false;
                antdMessage.info(code[ms?.code]);
                const name = ms.path.split("/").slice(-1)[0];
                if (ms.path) {
                    axios
                        .get(baseUrl + ms.path, {
                            responseType: "arraybuffer",
                        })
                        .then((res) => {
                            // 下载文件
                            const blob = new Blob([res.data]);
                            const link = document.createElement("a");
                            link.style.display = "none";
                            link.href = URL.createObjectURL(blob);
                            link.setAttribute("download", name);
                            link.click();
                        });
                }
                getPointCloud(root);
            });
    };
    /**
     * @description: 获取点云
     * @param {any} root
     * @return {*}
     */
    const getPointCloud = (root: any) => {
        let nroot;
        if (root) {
            nroot = root;
        } else if (pbRoot) {
            nroot = pbRoot;
        } else {
            alert("pb加载异常");
        }
        const calibrate = nroot?.lookupType("trunk.msgs.CalibrationPointCloud");
        axios
            .get(baseUrl + "/api/calibration/pointcloud/", {
                responseType: "arraybuffer",
            })
            .then((res) => {
                const lidar = calibrate?.decode(new Uint8Array(res.data)).pcl;
                const lidarData = lidar.map((item: any, index: any) => {
                    const resultMsg = fillArray(item.data);
                    return {
                        positions: new Float32Array(resultMsg),
                        translation: item.tf.translation,
                        rotation: item.tf.rpy,
                        name: item.header.frameId,
                        color: colorList[index],
                        visible: true,
                    };
                });
                setData(lidarData);
            })
            .catch((err) => {
                console.log("暂无点云数据无法进行标注！");
            });
    };
    /**
     * @description: 打开提示信息
     * @return {*}
     */
    const changeDisplayHelp = () => {
        Modal.info({
            content: <img className="help-img" src={AxisHelpImg}></img>,
            maskClosable: true,
            className: "calibration-help",
            width: "600px",
        });
    };
    /**
     * @description: 打开配置弹框
     * @return {*}
     */
    const changeDisplayConfig = (flag: boolean, val: any) => {
        setDisplayConfig(flag);
        if (typeof val === "object") {
            handleSetParams(val);
        }
    };
    return (
        <div className="calibration">
            <PointCloud
                data={{
                    pointCloud: newData,
                }}
            ></PointCloud>
            <div className="reset-box light">
                <i
                    className="iconfont icon-bangzhuyushuoming"
                    title="帮助"
                    onClick={changeDisplayHelp}
                ></i>
                <i
                    className="iconfont icon-setting"
                    title="雷达配置"
                    onClick={changeDisplayConfig}
                ></i>
                <div className="prot">
                    <Input
                        suffix="服务端口"
                        defaultValue={prot}
                        onChange={(e) => {
                            setProt(e.target.value);
                        }}
                    ></Input>
                </div>
            </div>

            <Config
                displayModal={displayConfig}
                param={param}
                changeDisplayConfig={changeDisplayConfig}
            />
        </div>
    );
}

export default Calibration;
