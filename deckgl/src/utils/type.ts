/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-10 10:33:23
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2023-02-06 17:01:53
 * @FilePath: /deckgl/src/utils/type.ts
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { Layout } from "react-grid-layout";

export enum LoadingState {
    IDLE = "IDLE",
    REQUEST = "REQUEST",
    SUCCESS = "DONE",
    FAILURE = "FAILURE",
}

export interface State {
    views: LayoutView[];
    components: Component[];
    loading: {
        state: LoadingState;
        error?: string;
    };
}

export interface LayoutView {
    id: string;
    name: string;
    componentLayout: ComponentLayout[];
}

export interface ComponentLayout extends Layout {
    component?: Component;
}

export interface Component {
    id: number;
    name: string;
}

export interface DataResponse {
    views: LayoutView[];
    components: Component[];
}
export interface DataType {
    key: string;
    name: string;
    age: number;
    address: string;
    tags: string[];
}
