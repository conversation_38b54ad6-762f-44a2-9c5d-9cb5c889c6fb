/*
 * @Author: luo<PERSON><PERSON> <EMAIL>
 * @Date: 2022-07-25 17:06:57
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2024-10-12 11:46:11
 * @FilePath: /deckgl/src/utils/mapTools.js
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import * as utm from "./utm.js";
// UTM坐标转换参数
const utmOptions = {
    zoneNum: 49,
    zoneLetter: "S",
};
const getZoneNum = (latitude) => {
    // 只要整数部分
    return Math.floor(latitude / 6) + Number(31);
};
const UTMGetZoneNum = (name) => {
    if (name.indexOf("nb") > -1) {
        return 51;
    } else if (name.indexOf("tj") > -1) {
        return 50;
    } else if (name.indexOf("hz") > -1) {
        return 50;
    } else if (name.indexOf("jjt") > -1) {
        return 50;
    } else if (name.indexOf("beijing") > -1) {
        return 50;
    } else if (name.indexOf("jh") > -1) {
        return 51;
    } else if (name.indexOf("shanghai") > -1) {
        return 51;
    } else if (name.indexOf("hf") > -1) {
        return 50;
    } else if (name.indexOf("ck") > -1) {
        return 47;
    } else if (name.indexOf("ns") > -1) {
        return 49;
    } else if (name.indexOf("xxl") > -1) {
        return -60;
    } else {
        return 51;
    }
};
/**
 * @description: 经纬度转UTM坐标
 * @param {*} pointOptons
 * @return {*}
 */
const fromLatLon = (point) => {
    const res = utm.fromLatLon(point[1], point[0], utmOptions.zoneNum);
    return {
        point: {
            x: res.easting,
            y: res.northing,
        },
        zoneNum: res.zoneNum,
        zoneLetter: res.zoneLetter,
    };
};
/**
 * @description: UTM坐标转换为经纬度
 * @param {*} point
 * @return {*}
 */
const toLatLon = (point, UTMZone, name, z) => {
    let Zone = UTMZone;

    if (UTMZone == 31 || UTMZone == 0) {
        Zone = UTMGetZoneNum(name);
    }
    const northern = Zone >= 0 ? true : false;

    let coordinates;
    if (point[0] && point[1]) {
        coordinates = {
            x: point[0],
            y: point[1],
        };
    } else {
        coordinates = point;
    }
    const res = utm.toLatLon(
        coordinates.x,
        coordinates.y,
        Math.abs(Zone),
        undefined,
        northern,
        false
    );
    if (Math.abs(res.longitude) <= 180 && Math.abs(res.latitude) < 90) {
        return [res.longitude, res.latitude, z || 0];
    } else {
        return [0, 0, z || 0];
    }
};

/**
 * @description: 包络计算
 * @param {*} options
 * @return {*}
 * todo 待完善
 * ! 注意：只适合UTM坐标系使用
 */
const calcPoints = (options) => {
    let { width, height, point, yaw } = options;

    let x = point.x;
    let y = point.y;
    // yaw 旋转90度
    let xo = Math.cos(yaw);
    let yo = Math.sin(yaw);
    let y1 = x + (height / 2) * yo;
    let x1 = y - (height / 2) * xo;
    let y2 = x - (height / 2) * yo;
    let x2 = y + (height / 2) * xo;
    // return [
    //     [y1 - (width / 2) * xo, x1 - (width / 2) * yo],
    //     [y2 - (width / 2) * xo, x2 - (width / 2) * yo],
    //     [y2 + (width / 2) * xo, x2 + (width / 2) * yo],
    //     [y1 + (width / 2) * xo, x1 + (width / 2) * yo],
    // ];
    const p1 = toLatLon({
            x: y1 - (width / 2) * xo,
            y: x1 - (width / 2) * yo,
        }).point,
        p2 = toLatLon({
            x: y2 - (width / 2) * xo,
            y: x2 - (width / 2) * yo,
        }).point,
        p3 = toLatLon({
            x: y2 + (width / 2) * xo,
            y: x2 + (width / 2) * yo,
        }).point,
        p4 = toLatLon({
            x: y1 + (width / 2) * xo,
            y: x1 + (width / 2) * yo,
        }).point;
    return [
        [
            [p1.x, p1.y],
            [p2.x, p2.y],
            [p3.x, p3.y],
            [p4.x, p4.y],
            [p1.x, p1.y],
        ],
    ];
};

/**
 * @description: 地址栏参数解析
 * @param {*} name
 * @return {*}
 */
const getQueryString = (name) => {
    let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    let r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return null;
};
export { fromLatLon, toLatLon, calcPoints, getQueryString };
