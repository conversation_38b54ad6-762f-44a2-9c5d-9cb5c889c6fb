import Dexie from "dexie";

export interface PointCloud {
    frame_id: string;
    seq: number;
    stamp: {};
    points: {};
}

export class PointCloudDB extends Dexie {
    pointClouds: Dexie.Table<PointCloud, number>;

    constructor() {
        super("PointCloudDB");
        // autoIncrement: true

        this.version(1).stores({
            //
            pointClouds: "++id,frame_id,seq,stamp,points",
        });
        this.pointClouds = this.table("pointClouds");
    }
}

export const db = new PointCloudDB();
