/*
 * @Author: fanmx <EMAIL>
 * @Date: 2024-03-27 17:33:59
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 16:31:18
 * @FilePath: \deckgl\src\utils\enum.ts
 * @Description:
 *
 * Copyright (c) 2025 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import Debug from "@/components/Debug";
import { MenuProps } from "antd";

/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-17 16:48:18
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-23 14:13:50
 * @FilePath: \deckgl\src\utils\enum.ts
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
export const decisionLatStateEnum: any = {
    0: "FOLLOW",
    1: "LEFT_LANE_CHANGE",
    2: "RIGHT_LANE_CHANGE",
    3: "CANCEL_LANE_CHANGE",
    4: "LEFT_NUDGE",
    5: "RIGHT_NUDGE",
    6: "UNKNOWN",
};

export const decisionLonStateEnum: any = {
    0: "KEEP",
    1: "OVERTAKE",
    2: "YIELD",
    3: "STOP",
    4: "E_STOP",
};

export const aebCollision: object = {
    0: "未触发",
    1: "即将碰撞",
    2: "输入异常",
};

export const PlayerContainerView: Record<
    string,
    {
        iconfont: string;
        deckWidth: string;
        title: string;
    }
> = {
    MINIMIZE: {
        iconfont: "icon-zuixiaohua",
        deckWidth: "0%",
        title: "playerView.minimize", // 翻译键：隐藏
    },
    CUSTOM: {
        iconfont: "icon-quxiaoquanping",
        deckWidth: "50%",
        title: "playerView.custom", // 翻译键：分屏
    },
    MAXIMIZE: {
        iconfont: "icon-quanping",
        deckWidth: "100%",
        title: "playerView.maximize", // 翻译键：全屏
    },
    FLOAT: {
        iconfont: "icon-xiaochuangkou",
        deckWidth: "30%",
        title: "playerView.float", // 翻译键：悬浮
    },
};
// export const replayMenuList = [
//     {
//         icon: "icon-database",
//         title: "数据",
//         key: "REPLAYDATA",
//     },
// ];
// export const pointCloudMenuList = [
//     {
//         icon: "icon-leidatance",
//         title: "点云",
//         key: "POINTCLOUD",
//     },
// ];

export const defaultMenuList = [
    {
        icon: "icon-database",
        title: "REPLAYDATA", //"数据",
        key: "REPLAYDATA",
    },
    {
        icon: "icon-leidatance",
        title: "POINTCLOUD", //"点云",
        key: "POINTCLOUD",
    },
    {
        icon: "icon-share",
        title: "SHARE", //"分享",
        key: "SHARE",
    },
    {
        icon: "icon-zhongwen1",
        isChange: true,
        title: "LANGUAGE", //"中英文切换",
        key: "LANGUAGE",
    },
    {
        icon: "icon-qingchuhuancun",
        title: "CLEARCACHE", // "清除缓存",
        key: "CLEARCACHE",
    },
    {
        icon: "icon-radar_line",
        title: "CALIBRATION", // "标定",
        key: "CALIBRATION",
    },
    {
        icon: "icon-wendang",
        title: "VERSION", // "版本信息",
        key: "VERSION",
    },
    {
        icon: "icon-setting",
        title: "SETTING", //"设置",
        key: "SETTING",
    },
];

export const TruckDebugInfo = {
    display: false,
    egoModel: "truck",
    truck: {
        width: {
            value: 2.5,
            min: 0,
            max: 1000,
            label: "truck.width", // 翻译键：宽度
        },
        length: {
            value: 6.9,
            min: 0,
            max: 1000,
            label: "truck.length", // 翻译键：长度
        },
    },
    trailer: {
        width: {
            value: 2.6,
            min: 0,
            max: 1000,
            label: "trailer.width", // 翻译键：宽度
        },
        length: {
            value: 12.92,
            min: 0,
            max: 1000,
            label: "trailer.length", // 翻译键：长度
        },
    },
    visualization: {
        trunkModel: {
            label: "visualization.model3d", // 翻译键：3D模型
            value: true,
        },
        speedLimit: {
            label: "visualization.speedLimitSign", // 翻译键：限速标志
            value: false,
        },
    },
    perception: {
        fusionDetection: {
            label: "perception.fusionPointCloud", // 翻译键：感知ogm点
            value: false,
        },
        ogm: {
            label: "perception.obstaclePointCloud", // 翻译键：障碍物点云
            value: true,
        },
        predictionObjs: {
            label: "perception.obstaclePrediction", // 翻译键：障碍物预测
            value: true,
        },
        objects: {
            label: "perception.obstacleEnvelope", // 翻译键：障碍物包络
            value: true,
        },
        perceptionDetails: {
            label: "perception.obstacleDetails", // 翻译键：障碍物详情
            value: false,
        },
        visionLanesDetails: {
            label: "perception.visualLaneLines", // 翻译键：视觉车道线
            value: false,
        },
        roadBoundary: {
            label: "perception.coastlinePointCloud", // 翻译键：海岸线点云
            value: false,
        },
        TrackObj: {
            label: "perception.modelTrackingBox", // 翻译键：模型追踪框
            value: false,
        },
        DebugObj: {
            label: "perception.modelDetectionBox", // 翻译键：模型检测框
            value: false,
        },
    },
    planning: {
        decisionReferenceLine: {
            label: "planning.decisionReferenceLine", // 翻译键：决策参考线
            value: true,
        },
        decisionBoundary: {
            label: "planning.decisionBoundary", // 翻译键：决策边界
            value: true,
        },
        planningTraj: {
            label: "planning.realTimeTrajectory", // 翻译键：实时轨迹
            value: true,
        },
        sectionMap: {
            label: "planning.mapSegmentation", // 翻译键：地图分段
            value: false,
        },
        planningBox: {
            label: "planning.trajectoryEnvelope", // 翻译键：轨迹包络
            value: false,
        },
        aebBox: {
            label: "planning.aebEnvelope", // 翻译键：AEB包络
            value: false,
        },
        lanes: {
            label: "planning.drivableArea", // 翻译键：可行驶区
            value: true,
        },
    },
    simulation: {
        details: {
            label: "simulation.details", // 翻译键：仿真详情
            value: false,
        },
    },
    distance: {
        label: "distance.dynamic", // 翻译键：动态距离
        value: {
            mask: {
                0: "0",
                50: "50m",
                100: "100m",
            },
            default: 0,
            value: 0,
        },
    },
    pathJSON: null,
};
export const VehicleParam = {
    head_length: 6.9,
    head_base2front: 5.45,
    head_base2tail: 1.45,
    head_width: 2.5,
    head_wheelbase: 4.5,
    trailer_length: 12.92,
    trailer_base2front: 0.85,
    trailer_base2tail: 12.07,
    trailer_width: 2.6,
    trailer_wheelbase: 9.0,
    trailer_base2head_base: 0.32,
    steering_ratio: 22.4,
    max_steering_angle: 38.0,
};

export const playbackRateTypeEnum: MenuProps["items"] = [
    {
        key: "3",
        label: "3.0x",
    },
    {
        key: "2",
        label: "2.0x",
    },
    {
        key: "1.5",
        label: "1.5x",
    },
    {
        key: "1",
        label: "1.0x",
    },
    {
        key: "0.5",
        label: "0.5x",
    },
];

export const playbackLoopTypeEnum: MenuProps["items"] = [
    {
        key: "single",
        label: "playback.single", //"单包循环",
    },
    {
        key: "list",
        label: "playback.list", //"列表循环",
        // disabled: true,
    },
    {
        key: "range",
        label: "playback.range", //"区间循环",
    },
    {
        key: "frame",
        label: "playback.frame", //"单帧播放",
    },
];

export const STOPREASON: Record<string, string> = {
    "0": "stopReason.driving", // 行驶中
    "1": "stopReason.softStop", // 缓停点停车
    "2": "stopReason.emergencyStop", // 急停点停车
    "3": "stopReason.obstacleDecision", // 决策障碍物停车
    "4": "stopReason.nonAutonomous", // 非自动驾驶
    "5": "stopReason.manualEstop", // 手拨ESTOP
    "6": "stopReason.mapPathDeviation", // 地图下发路径偏离过大
    "7": "stopReason.idle", // 空闲
    "10": "stopReason.speedLimitDecision", // 决策限速停车
    "11": "stopReason.obstaclePlanning", // 规划障碍物停车
    "12": "stopReason.destination", // 终点停车
    "13": "stopReason.trajectoryShortage", // 轨迹长度不足导致停车
    "14": "stopReason.highCurvature", // 曲率过大停车
    "15": "stopReason.controlDeviation", // 控制偏离路径过远停车
    "16": "stopReason.mapSpeedLimit", // 地图限速停车
    "20": "stopReason.insufficientThrottle", // 油门不足导致停车
    "21": "stopReason.dockingCompleted", // 对位完成
    "30": "stopReason.aebTriggered", // AEB导致停车
    "31": "stopReason.locationJump", // 定位跳变导致停车
    "32": "stopReason.antiFalling", // 防坠海触发
    "33": "stopReason.serpentineDriving", // 画龙
    "34": "stopReason.chassisDelay", // 监测到底盘延迟
    "35": "stopReason.abruptSteering", // 监测到急打转向
    "100": "stopReason.faultWithErrorCode", // 故障停车，需查看error_code
};
export const topicTabList = [
    {
        title: "Subscript",
        key: "subscript",
    },
    {
        title: "Lost",
        key: "lost",
    },
    {
        title: "Error",
        key: "error",
    },
];
