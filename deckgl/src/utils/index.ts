/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2023-01-03 13:29:52
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2024-10-23 17:25:37
 * @FilePath: /deckgl/src/utils/index.ts
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { Vec4 } from "@/types/index";

type Point3D = {
    x: number;
    y: number;
    z: number;
};
export const getColorArrayForRgbAndOpacity = (
    rgb: string | Vec4,
    opacity = 1
): Vec4 | null => {
    if (rgb instanceof Array) return rgb as Vec4;
    const start = rgb.match(/\d+/g)?.map((item) => Number(item));
    start?.push(opacity * 255);
    if (start && start.length === 4) {
        return start as Vec4;
    }
    return null;
};
/**
 * @description: 时间戳转换
 * @param {any} timestamp
 * @return {*}
 */
export const setTime = (timestamp: any, type: any) => {
    let date = new Date(timestamp * 1000);
    let Y = date.getFullYear(),
        M =
            date.getMonth() + 1 < 10
                ? "0" + (date.getMonth() + 1)
                : date.getMonth() + 1,
        D = date.getDate() < 10 ? "0" + date.getDate() : date.getDate(),
        h = date.getHours() < 10 ? "0" + date.getHours() : date.getHours(),
        m =
            date.getMinutes() < 10
                ? "0" + date.getMinutes()
                : date.getMinutes(),
        s =
            date.getSeconds() < 10
                ? "0" + date.getSeconds()
                : date.getSeconds(),
        milliSeconds = date.getMilliseconds();
    switch (type) {
        case "yyyy-MM-dd HH:mm:ss":
            return (
                Y +
                "-" +
                M +
                "-" +
                D +
                " " +
                h +
                ":" +
                m +
                ":" +
                s +
                "." +
                milliSeconds
            );
        case "yyyy-MM-dd":
            return Y + "-" + M + "-" + D;
        case "mm:ss":
            return m + ":" + s;
    }
};
/**
 * @description: 全屏
 * @return {*}
 */
export const requestFullScreen = () => {
    let element = document.documentElement;
    let requestMethod =
        element.requestFullScreen || element.webkitRequestFullScreen;
    if (requestMethod) {
        requestMethod.call(element);
    } else if (typeof window.ActiveXObject !== "undefined") {
        let wscript = new ActiveXObject("WScript.Shell");
        if (wscript !== null) {
            wscript.SendKeys("{F11}");
        }
    }
};
/**
 * @description: 判断是否为移动端
 * @return {*}
 */
export const isMobile = () => {
    let flag =
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            navigator.userAgent
        );
    return flag;
};
/**
 * @description: 判断ip格式是否正确
 * @param {*} val
 * @return {*}
 */
export const ipPattern =
    /^(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}(?:\/(?:3[0-2]|[1-2]?\d)\.(?:3[0-2]|[1-2]?\d))?(?:,(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}(?:\/(?:3[0-2]|[1-2]?\d)\.(?:3[0-2]|[1-2]?\d))?)*$/;

/**
 * @description: 通过中心点、宽高、yaw得到bounding四点
 * @return {*}
 */
type PoseArray = number[];
type IBounding = PoseArray[];
export const getBoundingBoxByCenter = ({
    center,
    yaw = 0,
    length,
    width,
}: {
    center: PoseArray;
    yaw: number;
    length: number;
    width: number;
}): IBounding | undefined => {
    if (!center || !length || !width) {
        return;
    }
    length = length < 0 ? 0 : length;
    width = width < 0 ? 0 : width;
    // A - Math.PI / 2
    // let x = center[0];
    // let y = center[1];
    // let xo = Math.cos(yaw);
    // let yo = Math.sin(yaw);
    // let y1 = x + (length / 2) * yo;
    // let x1 = y - (length / 2) * xo;
    // let y2 = x - (length / 2) * yo;
    // let x2 = y + (length / 2) * xo;
    // return [
    //     [y1 - (width / 2) * xo, x1 - (width / 2) * yo],
    //     [y2 - (width / 2) * xo, x2 - (width / 2) * yo],
    //     [y2 + (width / 2) * xo, x2 + (width / 2) * yo],
    //     [y1 + (width / 2) * xo, x1 + (width / 2) * yo],
    // ];
    // B
    let halfLength = length / 2;
    let halfWidth = width / 2;
    let cosYaw = Math.cos(yaw);
    let sinYaw = Math.sin(yaw);
    let cosLength = cosYaw * halfLength;
    let sinLength = sinYaw * halfLength;
    let sinWidth = sinYaw * halfWidth;
    let cosWidth = -cosYaw * halfWidth;

    let arr = [];
    arr[0] = {
        x: center[0] + cosLength + sinWidth,
        y: center[1] + sinLength + cosWidth,
    };

    arr[1] = {
        x: center[0] + cosLength - sinWidth,
        y: center[1] + sinLength - cosWidth,
    };
    arr[2] = {
        x: center[0] - cosLength - sinWidth,
        y: center[1] - sinLength - cosWidth,
    };
    arr[3] = {
        x: center[0] - cosLength + sinWidth,
        y: center[1] - sinLength + cosWidth,
    };
    arr[4] = arr[0];
    return arr;
};

/**
 * 旋转给定的点坐标
 * @param {Object} point - 要旋转的点，包含 x, y, z 坐标
 * @param {number} angle - 旋转角度（单位：弧度）
 * @returns {Object} 旋转后的点坐标，包含 x, y, z 坐标
 */
export const rotatePoint = (point: Point3D, angle: number) => {
    const cosAngle = Math.cos(angle); // 计算角度的余弦值
    const sinAngle = Math.sin(angle); // 计算角度的正弦值

    return {
        x: point.x * cosAngle - point.y * sinAngle, // 旋转后的 x 坐标
        y: point.x * sinAngle + point.y * cosAngle, // 旋转后的 y 坐标
        z: point.z, // Z 轴保持不变
    };
};

/**
 * 计算旋转后的盒子的 8 个顶点
 * @param {Object} center - 盒子的中心点坐标
 * @param {Object} size - 盒子的尺寸，包含 x, y, z
 * @param {number} heading - 盒子的旋转角度（弧度制）
 * @returns {Array} 旋转后的顶点坐标数组
 */
export const calculateBoxVertices = (
    center: Point3D,
    size: Point3D,
    heading: number
) => {
    const halfX = size.x / 2; // 计算 x 方向一半的长度
    const halfY = size.y / 2; // 计算 y 方向一半的长度
    const halfZ = size.z / 2; // 计算 z 方向一半的长度

    // 定义未旋转的 8 个顶点（前面的顶点已被注释）
    const vertices = [
        // { x: -halfX, y: -halfY, z: -halfZ }, // 0: 左下后
        // { x: halfX, y: -halfY, z: -halfZ }, // 1: 右下后
        // { x: halfX, y: halfY, z: -halfZ }, // 2: 右上后
        // { x: -halfX, y: halfY, z: -halfZ }, // 3: 左上后
        { x: -halfX, y: -halfY, z: halfZ }, // 4: 左下前
        { x: halfX, y: -halfY, z: halfZ }, // 5: 右下前
        { x: halfX, y: halfY, z: halfZ }, // 6: 右上前
        { x: -halfX, y: halfY, z: halfZ }, // 7: 左上前
    ];

    // 计算旋转后的顶点并调整到中心位置
    return vertices.map((vertex) => {
        // 先旋转顶点坐标
        const rotatedVertex = rotatePoint(vertex, heading);
        // 再加上中心点坐标
        return {
            x: rotatedVertex.x + center.x,
            y: rotatedVertex.y + center.y,
            z: rotatedVertex.z + center.z,
        };
    });
};

/**
 * @description: 节流
 * @return {*}
 */

export const throttle = (method: Function, delay: number) => {
    var timer: NodeJS.Timeout | null = null;
    return function () {
        const args = arguments;
        timer && clearTimeout(timer);
        timer = setTimeout(() => {
            method(args);
        }, delay);
    };
};

/**
 * @description: 数组填充
 * @param {*} origin
 * @param {*} MaxLength
 * @return {*}
 */
export const fillArray = (origin, MaxLength = 150000) => {
    origin = origin.flat();
    if (!(origin instanceof Array)) return [origin];
    if (origin.length < MaxLength) {
        return origin.concat(new Array(MaxLength - origin.length).fill(0));
    } else {
        return origin.splice(0, MaxLength);
    }
};

/**
 * @description: 复制
 * @param {string} text
 * @return {*}
 */
export const copyText = (text: object | string) => {
    if (typeof text === "object") {
        text = JSON.stringify(text);
    }
    // 将 JSON 对象转换为字符串

    // 创建一个临时文本输入框
    const tempInput = document.createElement("textarea");

    // 设置文本输入框的值为 JSON 字符串
    tempInput.value = text;

    // 将文本输入框添加到页面中
    document.body.appendChild(tempInput);

    // 选中文本输入框中的内容
    tempInput.select();

    // 复制选中的内容到剪贴板
    document.execCommand("copy");

    // 移除临时文本输入框
    document.body.removeChild(tempInput);
};

/**
 * @description:  获取 url 参数
 * @param {string} paramName
 * @return {*}
 */
export const getUrlParam = (paramName: string) => {
    const url = window.location.href;
    const queryString = url.split("?")[1];
    if (!queryString) {
        return null;
    }

    const params = new URLSearchParams(queryString);
    return params.get(paramName) as string;
};

/**
 * @description: 控制返回0
 * @param {any} val
 * @return {*}
 */
export const returnNumber = (val: any) => {
    if (val === null || val === undefined || val === "") {
        return 3;
    }
    return val;
};
