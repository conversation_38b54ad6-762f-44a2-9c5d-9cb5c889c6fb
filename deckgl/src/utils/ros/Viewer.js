import * as THREE from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
// import { OrbitControls } from "./OrbitControls";

export default class Viewer {
    constructor(options) {
        options = options || {};
        this.divID = options.divID;
        this.el = document.getElementById(this.divID);
        this.width = options.width;
        this.height = options.height;
        this.background = options.background || "#222222";
        this.antialias = options.antialias;
        this.intensity = options.intensity || 0.66;
        this.near = options.near || 0.01;
        this.far = options.far || 1000;
        this.alpha = options.alpha || 1.0;
        this.clearColor = options.clearColor || [0x000000, 1];
        this.cameraPosition = options.cameraPose || {
            x: 0,
            y: 0,
            z: 200,
        };
        this.targetPosition = options.targetPosition || {
            x: 0,
            y: 0,
            z: 0,
        };
        this.init();
        this.start();
    }
    init() {
        // render
        this.renderer = new THREE.WebGLRenderer({
            antialias: this.antialias,
            alpha: this.alpha,
        });
        this.renderer.setClearColor(this.background, 1);
        this.renderer.sortObjects = false;
        this.renderer.setSize(this.width, this.height);
        this.renderer.shadowMap.enabled = false;
        this.renderer.autoClear = false;
        //scene
        this.scene = new THREE.Scene();
        // axesHelper
        this.addAxiosHelper();
        // raycaster
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        this.raycasterModelArr = [];
        // create the global camera
        this.camera = new THREE.PerspectiveCamera(
            40,
            this.width / this.height,
            this.near,
            this.far
        );
        this.camera.position.set(
            this.cameraPosition.x,
            this.cameraPosition.y,
            this.cameraPosition.z
        );
        // 设置camera的up为y轴
        this.camera.up = new THREE.Vector3(0, 0, 1);
        // this.camera.target = this.targetPosition;
        // orbitControls
        this.orbitControls = new OrbitControls(
            this.camera,
            this.renderer.domElement
        );
        this.orbitControls.mouseButtons = {
            LEFT: THREE.MOUSE.PAN,
            MIDDLE: THREE.MOUSE.DOLLY,
            RIGHT: THREE.MOUSE.ROTATE,
        };

        // lights
        this.scene.add(new THREE.AmbientLight(0x555555));
        this.directionalLight = new THREE.DirectionalLight(
            0xffffff,
            this.intensity
        );
        this.scene.add(this.directionalLight);
        this.scene.rotateZ(Math.PI / 2);

        // const geometry = new THREE.PlaneGeometry(1000, 1000);
        // const material = new THREE.MeshBasicMaterial({
        //     color: 0xcccccc,
        //     depthWrite: false,
        //     transparent: true,
        //     opacity: 0,
        // });
        // 基础平面 用来提供识别点击位置
        // this.plane = new THREE.Mesh(geometry, material);
        // this.plane.position.z = 0.1;
        // this.plane.name = "plane";
        // this.scene.add(this.plane);
        this.animationRequestId = undefined;

        // add the renderer to the page
        var node = document.getElementById(this.divID);
        node.appendChild(this.renderer.domElement);
    }
    start() {
        // this.directionalLight.position.normalize();
        // this.renderer.clear(true, true, true);
        this.renderer.render(this.scene, this.camera);
        this.animationRequestId = requestAnimationFrame(this.start.bind(this));
    }
    resize(width, height) {
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }
    rerender({ name, size, color }) {
        const mesh = this.scene.getObjectByName(name);
        if (mesh) {
            const points = mesh.children[0];
            const { material } = points;
            material.size = size;
            material.color.setHex(`0x${color.toString(16)}`);
            return true;
        }
    }
    addAxiosHelper() {
        const axesHelper = new THREE.AxesHelper(2);
        this.scene.add(axesHelper);
    }
}
