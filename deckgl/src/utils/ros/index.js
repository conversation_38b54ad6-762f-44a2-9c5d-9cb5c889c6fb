/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> lish<PERSON>@trunk.tech
 * @Date: 2023-02-07 11:00:06
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @FilePath: /mapsets/deckgl/src/utils/ros/index.js
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import PointCloud2 from "./PointCloud2";
import Viewer from "./Viewer";
import * as THREE from "three";

function clearThreeScene(viewer) {
    if (viewer.scene) {
        viewer.scene.traverse((item) => {
            disposeChild(item);
        });
        viewer.scene.clear();
        // viewer.renderer.dispose();
        // viewer.renderer.forceContextLoss();
    }
    // THREE.Cache.clear();
}
function disposeChild(mesh) {
    if (mesh instanceof THREE.Mesh) {
        if (mesh.geometry?.dispose) {
            mesh.geometry.dispose(); //删除几何体
        }
        if (mesh.material?.dispose) {
            mesh.material.dispose(); //删除材质
        }
        if (mesh.material?.texture?.dispose) {
            mesh.material.texture.dispose();
        }
    }
    if (mesh instanceof THREE.Group) {
        mesh.clear();
    }
    if (mesh instanceof THREE.Object3D) {
        mesh.clear();
    }
}

export function decode64(inbytes, outbytes, record_size, pointRatio) {
    var x,
        b = 0,
        l = 0,
        j = 0,
        L = inbytes.length,
        A = outbytes.length;
    record_size = record_size || A; // default copies everything (no skipping)
    pointRatio = pointRatio || 1; // default copies everything (no skipping)
    var bitskip = (pointRatio - 1) * record_size * 8;
    for (x = 0; x < L && j < A; x++) {
        b = (b << 6) + decode64.e[inbytes.charAt(x)];
        l += 6;
        if (l >= 8) {
            l -= 8;
            outbytes[j++] = (b >>> l) & 0xff;
            if (j % record_size === 0) {
                // skip records
                // no    optimization: for(var i=0;i<bitskip;x++){l+=6;if(l>=8) {l-=8;i+=8;}}
                // first optimization: for(;l<bitskip;l+=6){x++;} l=l%8;
                x += Math.ceil((bitskip - l) / 6);
                l = l % 8;

                if (l > 0) {
                    b = decode64.e[inbytes.charAt(x)];
                }
            }
        }
    }
    return Math.floor(j / record_size);
}
// initialize decoder with static lookup table 'e'
decode64.S = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
decode64.e = {};
for (var i = 0; i < 64; i++) {
    decode64.e[decode64.S.charAt(i)] = i;
}

export { PointCloud2, Viewer, clearThreeScene };
