import * as THREE from "three";
// import * as ROS<PERSON><PERSON> from 'roslib';
// import {decode64} from './index';
import SceneNode from "./SceneNode";

export default class Points extends THREE.Object3D {
    constructor(options) {
        super();
        options = options || {};
        this.tfClient = options.tfClient;
        this.rootObject = options.rootObject || new THREE.Object3D();
        this.max_pts = options.max_pts || 10000;
        this.pointRatio = options.pointRatio || 1;
        this.messageRatio = options.messageRatio || 1;
        this.messageCount = 0;
        this.material = options.material || {};
        this.colorsrc = options.colorsrc;
        this.colormap = options.colormap;
        this.sceneNodeName = options.topic;

        if ("color" in options || "size" in options || "texture" in options) {
            console.warn(
                'toplevel "color", "size" and "texture" options are deprecated.' +
                    'They should beprovided within a "material" option, e.g. : ' +
                    " { tfClient, material : { color: mycolor, size: mysize, map: mytexture }, ... }"
            );
        }

        this.sn = null;
    }

    setup(frame, point_step, fields) {
        if (this.sn === null) {
            // turn fields to a map
            fields = fields || [];
            this.fields = {};
            for (var i = 0; i < fields.length; i++) {
                this.fields[fields[i].name] = fields[i];
            }
            this.geom = new THREE.BufferGeometry();

            this.positions = new THREE.BufferAttribute(
                new Float32Array(this.max_pts * 3),
                3,
                false
            );
            this.geom.setAttribute(
                "position",
                this.positions.setUsage(THREE.StaticDrawUsage)
            );
            if (!this.colorsrc && this.fields.rgb) {
                this.colorsrc = "rgb";
            }
            if (this.colorsrc) {
                var field = this.fields[this.colorsrc];
                if (field) {
                    this.colors = new THREE.BufferAttribute(
                        new Float32Array(this.max_pts * 3),
                        3,
                        false
                    );
                    this.geom.setAttribute(
                        "color",
                        this.colors.setUsage(THREE.StaticDrawUsage)
                    );
                    var offset = field.offset;
                    this.getColor = [
                        function (dv, base, le) {
                            return dv.getInt8(base + offset, le);
                        },
                        function (dv, base, le) {
                            return dv.getUint8(base + offset, le);
                        },
                        function (dv, base, le) {
                            return dv.getInt16(base + offset, le);
                        },
                        function (dv, base, le) {
                            return dv.getUint16(base + offset, le);
                        },
                        function (dv, base, le) {
                            return dv.getInt32(base + offset, le);
                        },
                        function (dv, base, le) {
                            return dv.getUint32(base + offset, le);
                        },
                        function (dv, base, le) {
                            return dv.getFloat32(base + offset, le);
                        },
                        function (dv, base, le) {
                            return dv.getFloat64(base + offset, le);
                        },
                    ][field.datatype - 1];
                    this.colormap =
                        this.colormap ||
                        function (x) {
                            return new THREE.Color(1, 1, 1);
                        };
                } else {
                    console.warn(
                        'unavailable field "' +
                            this.colorsrc +
                            '" for coloring.'
                    );
                }
            }
            if (!this.material.isMaterial) {
                // if it is an option, apply defaults and pass it to a PointsMaterial
                if (this.colors && this.material.vertexColors === undefined) {
                    this.material.vertexColors = THREE.VertexColors;
                }
                this.material = new THREE.PointsMaterial(this.material);
                this.material.needsUpdate = true;
            }

            this.object = new THREE.Points(this.geom, this.material);

            this.sn = new SceneNode({
                frameID: frame,
                tfClient: this.tfClient,
                object: this.object,
            });
            this.sn.name = this.sceneNodeName;

            this.rootObject.add(this.sn);
        }
        return this.messageCount++ % this.messageRatio === 0;
    }

    update(n) {
        this.geom.setDrawRange(0, n);

        this.positions.needsUpdate = true;
        this.positions.updateRange.count = n * this.positions.itemSize;

        if (this.colors) {
            this.colors.needsUpdate = true;
            this.colors.updateRange.count = n * this.colors.itemSize;
        }
    }
}
