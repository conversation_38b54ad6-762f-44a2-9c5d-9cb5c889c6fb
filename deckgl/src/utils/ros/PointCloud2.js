/*
 * @Author: jack <EMAIL>
 * @Date: 2023-02-13 13:35:52
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2023-02-16 14:10:52
 * @FilePath: /deckgl/src/utils/ros/PointCloud2.js
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
/*
 * @Author: l<PERSON><PERSON><PERSON> l<PERSON>@trunk.tech
 * @Date: 2022-06-29 18:10:50
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2023-02-14 11:11:15
 * @FilePath: /deckgl/src/utils/ros/PointCloud2.js
 * @Description:
 *
 * Copyright (c) 2022 by l<PERSON><PERSON><PERSON> lish<PERSON>@trunk.tech, All Rights Reserved.
 */
import * as THREE from "three";
import "roslib/build/roslib";

import { decode64 } from "./index";
import Points from "./Points";
// import { db } from "@/utils/db";

export default class PointCloud2 extends THREE.Object3D {
    constructor(options) {
        super(options);
        options = options || {};
        this.ros = options.ros;
        this.topicName = options.topic || "/points";
        this.throttle_rate = options.throttle_rate || null;
        this.compression = options.compression || "cbor";
        this.max_pts = options.max_pts || 10000;
        this.buffer = null;
        this.rosTopic = undefined;
        this.tfClient = options.tfClient;
        this.callback = options.callback;
        this.points = new Points(options);
        this.oldTime = 0;
        this.subscribe();
        this.callback = options.callback;
    }
    unsubscribe() {
        if (this.rosTopic) {
            this.rosTopic.unsubscribe(this.processMessage);
        }
    }
    subscribe() {
        this.unsubscribe();

        // subscribe to the topic
        this.rosTopic = new window.ROSLIB.Topic({
            ros: this.ros,
            name: this.topicName,
            messageType: "sensor_msgs/PointCloud2",
            throttle_rate: this.throttle_rate,
            queue_length: 1,
            compression: this.compression,
        });
        this.rosTopic.subscribe(this.processMessage.bind(this));
    }
    processMessage(msg) {
        // callback
        if (this.callback) {
            this.callback(msg);
        }
        if (
            !this.points.setup(msg.header.frame_id, msg.point_step, msg.fields)
        ) {
            return;
        }
        var n,
            pointRatio = this.points.pointRatio;
        var bufSz = this.max_pts * msg.point_step;
        if (msg.data.buffer) {
            this.buffer = msg.data.slice(
                0,
                Math.min(msg.data.byteLength, bufSz)
            );
            n = Math.min(
                (msg.height * msg.width) / pointRatio,
                this.points.positions.array.length / 3
            );
        } else {
            if (!this.buffer || this.buffer.byteLength < bufSz) {
                this.buffer = new Uint8Array(bufSz);
            }
            n = decode64(msg.data, this.buffer, msg.point_step, pointRatio);
            pointRatio = 1;
        }
        var dv = new DataView(this.buffer.buffer);
        var littleEndian = !msg.is_bigendian;
        var x = this.points.fields.x.offset;
        var y = this.points.fields.y.offset;
        var z = this.points.fields.z.offset;
        var base, color;
        for (var i = 0; i < n; i++) {
            base = i * pointRatio * msg.point_step;
            this.points.positions.array[3 * i] = dv.getFloat32(
                base + x,
                littleEndian
            );
            this.points.positions.array[3 * i + 1] = dv.getFloat32(
                base + y,
                littleEndian
            );
            this.points.positions.array[3 * i + 2] = dv.getFloat32(
                base + z,
                littleEndian
            );
            if (this.points.colors) {
                color = this.points.colormap(
                    this.points.getColor(dv, base, littleEndian)
                );
                this.points.colors.array[3 * i] = color.r;
                this.points.colors.array[3 * i + 1] = color.g;
                this.points.colors.array[3 * i + 2] = color.b;
            }
        }
        // 录制点云消息
        // const frame_id = msg.header.frame_id;
        // const seq = msg.header.seq;
        // const stamp = msg.header.stamp;
        // db.pointClouds.add({
        //     frame_id,
        //     seq,
        //     stamp,
        //     points: this.points.positions,
        // });

        this.points.update(n);
    }
}
