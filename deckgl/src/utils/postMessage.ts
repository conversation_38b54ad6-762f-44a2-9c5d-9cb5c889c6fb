/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> l<PERSON>@trunk.tech
 * @Date: 2023-02-10 13:25:27
 * @LastEditors: fanmixue <EMAIL>
 * @FilePath: /deckgl/src/utils/postMessage.ts
 * @Description: postMessage 向父容器发送命令
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
// 侧边栏
type messageType = "config" | "operation" | "data";
export interface IMessage {
    type: messageType;
    data: any;
}
export const postMessage = ({
    message,
    targetOrigin = "*",
}: {
    message: IMessage;
    targetOrigin?: string;
}) => {
    if (window == window.parent) return;
    window.parent.postMessage(message, targetOrigin);
};
