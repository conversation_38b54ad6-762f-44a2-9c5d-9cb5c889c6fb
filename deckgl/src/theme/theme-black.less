// 深色主题

.dark {
    --dialog-bg-color: rgba(38, 41, 51.1);
    --carInfo-bg-color: #11142a;
    --carInfo-font-color: #d2d8e5;
    --carInfo-highlight-font-color: #d8e6ff;
    --vehicleInfo-bg-color: #262933;
    --vehicleInfo-font-color: #e9eef9;
    --vehicleInfo-icon-color: #454b54;
    --vehicleInfo-bot-bg-color: #141b27;
    --vehicleInfo-D-bg-color: rgba(0, 86, 255, 0.12);
    --panel-bs-color: 0px 2px 8px 0px #141b27;
    --panel-bg-color: rgba(38, 41, 51, 0.4);
    --panel-font-color: #e9eef9;
    --fault-title-bg-color: #2b2e34;
    --fault-title-bd-color: #4e555b;
    --fault-default-bg-color: #103903;
    --fault-default-bd-color: #1d5004;
    --fault-highlight-bg-color: #38171b;
    --fault-highlight-bd-color: #58181c;
    --fault-highlight-btn-bg-color: #a61d24;
    --fault-highlight-btn-font-color: #e9eef9;
    --fault-warning-bg-color: rgba(249, 211, 5, 0.12);
    --fault-warning-bd-color: rgba(235, 164, 58, 0.5);
    --fault-warning-btn-bg-color: #eba43a;
    --fault-warning-btn-font-color: #e9eef9;
    --mapboxgl-ctrl-bg-color: rgba(0, 0, 0, 0.4);
    --mapBox-bg-color: #262933;
    --config-title-bg-color: #2b2e34;
    --config-title-bd-color: #4e555b;
    --config-title-font-color: #e9eef9;
    --config-title-bdb-color: #4e555b;
    --config-bg-color: #262933;
    --config-bs-color: #141b27;
    --config-input-bd-color: #5d656c;
    --config-input-bg-color: #262933;
    --config-tab-bs-color: #5d656c;
    --config-tab-select-bg-color: #172339;
    --config-panel-bd-color: #262933;
    --config-placeholder-color: rgba(233, 238, 249, 0.55);
    --config-scrollbar-thumb-bg-color: rgba(102, 89, 89);
    --config-scrollbar-track-bg-color: rgba(102, 89, 89, 0.3);
    --progress-bg-color: rgba(255, 255, 255, 0.2);
    --progress-play-font-color: #fff;
    --dropdown-bg-color: #464956;
    --dropdown-font-color: #e9eef9;
    --dropdown-select-hover-bg-color: #595656;
    --dropdown-select-item-option-selected: #797d8c;
    --point-cloud-bg: #222;
    --topic-title-background-color: #4e555b;
    --player-container-bg-color: #27272b;
    --player-container-bd-color: #3c3838;
    --replay-bag-list-bg-color: #242222;
    --replay-select-bag-bg-color: #000;
    --limit-font-color: #fff;
    --topic-btn-bg-color: rgba(24, 144, 255, 0.9);
    --topic-btn-font-color: #fff;

    .custom-table .ant-table-tbody > tr > td {
        background-color: #d11919 !important; /* 举例：修改单元格背景色 */
    }
}
