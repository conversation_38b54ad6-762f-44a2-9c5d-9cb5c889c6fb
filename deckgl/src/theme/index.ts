/*
 * @Author: luofei <EMAIL>
 * @Date: 2022-10-19 18:46:24
 * @LastEditors: luofei <EMAIL>
 * @LastEditTime: 2025-04-28 14:23:24
 * @FilePath: /deckgl/src/theme/index.ts
 * @Description: 主题色配置
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { Vec4, Vec3 } from "@/types";

// 定义主题的接口

interface DefaultTheme {
    pointColor: Vec4;
    lineColor: Vec4;
    boxColor: Vec4;
    boxFillColor: Vec4;
    smallFontSize: number;
    middleFontSize: number;
    largeFontSize: number;
    elevation: {};
}

interface Theme extends DefaultTheme {
    background: string;
    mapboxColor: string;
    markColor: Vec4;
    // 可以给每个模型的title正常文字大小和颜色配置
    idSizeUnits?: "pixels" | "meters" | "common";
    glbTitle?: {
        [key: string]: {
            fontColor: Vec4;
            fontSize: number;
        };
    };
    geoJson: {
        [key: string]: Vec4; // 使用索引签名来定义可变字段
    };
}

const defaultTheme: DefaultTheme = {
    pointColor: [76, 41, 255, 255],
    lineColor: [87, 255, 206, 255],
    boxColor: [150, 182, 255, 255],
    boxFillColor: [255, 150, 255, 100],
    smallFontSize: 12,
    middleFontSize: 16,
    largeFontSize: 20,
    elevation: {
        Coastline: 0,
        Building: 0,
        Checkpoint: 0,
        Yard: 0,
    },
};
const dark: Theme = {
    ...defaultTheme,
    background: "#282828",
    mapboxColor: "mapbox://styles/mapbox/dark-v10",
    markColor: [255, 255, 255, 180],
    geoJson: {
        Yard: [160, 177, 191, 200],
        Arrow: [255, 255, 255, 150],
        Park: [66, 255, 239, 10],
        Hatch: [119, 136, 153, 200],
        Lock: [255, 222, 16, 240],
        Epile: [53, 206, 117, 240],
        Ebox: [76, 255, 176, 240],
        Coastline: [255, 165, 0, 180],
        Inner: [0, 255, 10, 10],
        Outer: [160, 177, 191, 100],
        Building: [220, 220, 220, 100],
        Checkpoint: [100, 20, 255, 50],
        0: [255, 255, 255, 100],
        default: [22, 23, 30, 240],
    },
};
const light: Theme = {
    ...defaultTheme,
    background: "#EEF1F5",
    mapboxColor: "mapbox://styles/mapbox/light-v10",
    markColor: [0, 0, 0, 150],
    geoJson: {
        Yard: [207, 220, 230, 180],
        Arrow: [196, 202, 205, 240],
        Park: [66, 255, 239, 10],
        Hatch: [119, 156, 200, 200],
        Epile: [53, 206, 117, 240],
        Ebox: [76, 255, 176, 240],
        Lock: [255, 222, 16, 240],
        Coastline: [255, 165, 0, 180],
        Inner: [0, 255, 10, 10],
        Outer: [160, 177, 191, 100],
        Building: [220, 220, 220, 100],
        Checkpoint: [10, 20, 200, 50],
        0: [145, 150, 152, 255],
        default: [238, 241, 245, 240],
    },
};
const localStyle = JSON.parse(localStorage.getItem("themeStyle") || "{}");

export const themeStyle: {
    [key: string]: Theme;
} = {
    default: light,
    dark: localStyle?.dark || dark,
    light: localStyle?.light || light,
};

export const getIconfont = (name: string) => {
    if (name) {
        return {
            url: `geojsonImg/${name}.png`,
            width: 128,
            height: 128,
        };
    } else {
        return {
            url: "geojsonImg/default.png",
            width: 0,
            height: 0,
        };
    }
};
