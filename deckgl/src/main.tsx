/*
 * @Author: luo<PERSON><PERSON> <EMAIL>
 * @Date: 2022-08-24 10:11:34
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-23 15:44:35
 * @FilePath: \deckgl\src\main.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import ReactDOM from "react-dom/client";
import { Provider } from "react-redux";
import store from "./app/store";
import "./index.less";
import "../src/assets/font/iconfont.css";
// import "../node_modules/react-grid-layout/css/styles.css";
// import "../node_modules/react-resizable/css/styles.css";
import "antd/dist/reset.css";
import Rooter from "./rooter";
import { I18nProvider } from "./i18n/provider";
import "./i18n";

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
    <Provider store={store}>
        <I18nProvider>
            <Rooter />
        </I18nProvider>
    </Provider>
);
