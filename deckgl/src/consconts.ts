/*
 * @Author: jack <EMAIL>
 * @Date: 2023-07-12 17:06:27
 * @LastEditors: luofei <EMAIL>
 * @LastEditTime: 2025-04-28 15:49:46
 * @FilePath: /deckgl/src/consconts.ts
 * @Description:常量
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

export const DEFAULT_CAR = {
    origin: [0, 0, 0.7],
    color: [128, 128, 128],
    scale: [2, 1, 0.7],
};
export const DEFAULT_ORIGIN = [0, 0, 0];
export const CAR_DATA = [[0, 0, 0]];

// mapboxTOKEN
export const MAPBOX_TOKEN =
    "pk.eyJ1IjoibHVvZmVpIiwiYSI6ImNrY3gwanpldzBkcTIycXF6d2FmYmF0bzYifQ.VecZ41O_HKtxU4JSQUXlyw";

export const MAP_STYLE_DARK = "mapbox://styles/mapbox/dark-v10?optimize=true";
export const MAP_STYLE_LIGHT = "mapbox://styles/mapbox/light-v10?optimize=true";
export const MAP_STYLE = "mapbox://styles/luofei/clje1bp74004001pl27xr1kez";

// 默认坐标天安门
export const DEFAULT_VIEWPORT = {
    longitude: 116.3974,
    latitude: 39.9093,
};

// 视角模式
export const VIEW_MODE = {
    TOP_DOWN: {
        name: "top-down",
        initialViewState: {
            maxPitch: 0,
            pitch: 0,
        },
        orthographic: true,
        tracked: {
            position: true,
            heading: true,
        },
    },
    PERSPECTIVE: {
        name: "perspective",
        initialViewState: {
            maxPitch: 85,
            pitch: 60,
        },
        tracked: {
            position: true,
            heading: true,
        },
    },
    DRIVER: {
        name: "driver",
        initialProps: {
            maxPitch: 0,
        },
        firstPerson: {
            position: [0, 0, 1.5],
        },
        mapInteraction: {
            dragPan: false,
            scrollZoom: false,
        },
    },
};

// 主车模型
export const EGO_MODEL = [
    {
        label: "truck",
        value: "truck",
    },
    {
        label: "truck_all",
        value: "truck_all",
    },
    {
        label: "art",
        value: "art",
    },
    {
        label: "agv",
        value: "agv",
    },
    {
        label: "bus",
        value: "bus",
    },
];

// 模型列表
export const MODEL_LIST = {
    agv: {
        size: {
            x: 5.306,
            y: 2.961,
            z: 2.637,
        },
        height: 3.5,
    },
    anqiao: {
        size: {
            x: 47.95,
            y: 21.784,
            z: 23.545,
        },
        height: 28.5,
    },
    art: {
        size: {
            x: 10.02,
            y: 1.865,
            z: 1.36,
        },
        height: 3.5,
    },
    art_default: {
        size: {
            x: 10.02,
            y: 1.865,
            z: 1.36,
        },
        height: 3.5,
    },
    art_fs: {
        size: {
            x: 10.02,
            y: 1.865,
            z: 2.734,
        },
        height: 3.5,
    },
    art_mb: {
        size: {
            x: 10.02,
            y: 1.865,
            z: 2.734,
        },
        height: 3.5,
    },
    art_ms: {
        size: {
            x: 10.02,
            y: 1.865,
            z: 2.734,
        },
        height: 3.5,
    },
    art_rs: {
        size: {
            x: 10.02,
            y: 1.865,
            z: 2.734,
        },
        height: 3.5,
    },
    art_ts: {
        size: {
            x: 10.02,
            y: 1.865,
            z: 2.734,
        },
        height: 3.5,
    },
    barrel: {
        size: {
            x: 0.995,
            y: 0.995,
            z: 1.062,
        },
        height: 1.5,
    },
    bicycle: {
        size: {
            x: 1.194,
            y: 0.414,
            z: 1.054,
        },
        height: 1.5,
    },
    bus: {
        size: {
            x: 6.685,
            y: 1.972,
            z: 1.992,
        },
        height: 3.5,
    },
    changqiao: {
        size: {
            x: 17.521,
            y: 11.334,
            z: 14.682,
        },
        height: 15.5,
    },
    dbchangqiao: {
        size: {
            x: 24.214,
            y: 14.163,
            z: 19.406,
        },
        height: 20.5,
    },
    demo: {
        size: {
            x: 10.02,
            y: 1.865,
            z: 1.36,
        },
        height: 3.5,
    },
    eccentric: {
        size: {
            x: 3.845,
            y: 2.009,
            z: 2.11,
        },
        height: 3.5,
    },
    forklift: {
        size: {
            x: 3.823,
            y: 1.371,
            z: 2.569,
        },
        height: 3.5,
    },
    obstacle_truck: {
        size: {
            x: 3.666,
            y: 1.824,
            z: 2.308,
        },
        height: 3.5,
    },
    head: {
        size: {
            x: 3.659,
            y: 1.824,
            z: 2.361,
        },
        height: 3.5,
    },
    motorcycle: {
        size: {
            x: 1.561,
            y: 0.404,
            z: 0.857,
        },
        height: 1.5,
    },
    obstacle_trailter: {
        size: {
            x: 9.241,
            y: 1.71,
            z: 2.613,
        },
        height: 3.5,
    },
    person: {
        size: {
            x: 0.367,
            y: 0.377,
            z: 1.141,
        },
        height: 2.5,
    },
    ship: {
        size: {
            x: 25.002,
            y: 100,
            z: 30.001,
        },
        height: 35,
    },
    three_wheel: {
        size: {
            x: 3.066,
            y: 1.336,
            z: 1.924,
        },
        height: 1.5,
    },
    traffic_cone: {
        size: {
            x: 0.508,
            y: 0.508,
            z: 0.501,
        },
        height: 1.5,
    },
    "trailer-nobox": {
        size: {
            x: 9.221,
            y: 1.689,
            z: 0.769,
        },
        height: 3.5,
    },
    trailer: {
        size: {
            x: 9.236,
            y: 1.72,
            z: 2.538,
        },
        height: 3.5,
    },
    trailer_default: {
        size: {
            x: 9.22,
            y: 1.688,
            z: 0.769,
        },
        height: 3.5,
    },
    trailer_fs: {
        size: {
            x: 9.22,
            y: 1.688,
            z: 2.276,
        },
        height: 3.5,
    },
    trailer_mb: {
        size: {
            x: 9.22,
            y: 1.688,
            z: 2.276,
        },
        height: 3.5,
    },
    trailer_ms: {
        size: {
            x: 9.22,
            y: 1.688,
            z: 2.276,
        },
        height: 3.5,
    },
    trailer_rs: {
        size: {
            x: 9.22,
            y: 1.688,
            z: 2.276,
        },
        height: 3.5,
    },
    trailer_ts: {
        size: {
            x: 9.22,
            y: 1.688,
            z: 2.276,
        },
        height: 3.5,
    },
    truck: {
        size: {
            x: 3.666,
            y: 1.824,
            z: 2.308,
        },
        height: 3.5,
    },
    truck_all: {
        size: {
            x: 10.906,
            y: 1.824,
            z: 2.538,
        },
        height: 3.5,
    },
    truck_normal: {
        size: {
            x: 6.548,
            y: 1.95,
            z: 2.315,
        },
        height: 3.5,
    },
    vehicle: {
        size: {
            x: 2.958,
            y: 1.281,
            z: 0.887,
        },
        height: 3.5,
    },
    alien_vehicle: {
        size: {
            x: 2,
            y: 2,
            z: 2,
        },
        height: 2,
    },
};

// glbs
export const creat_glbs = () => {
    const glbs = {} as {
        [key: string]: [];
    };
    // MODEL_LIST.forEach((model) => {
    //     glbs[model] = [];
    // });
    // 遍历对象

    for (const key in MODEL_LIST) {
        glbs[key] = [];
    }
    return glbs;
};
//  模型配置
export const creat_glbs_config = () => {
    const glbs_config = {} as {
        [key: string]: {
            scale: {
                x: number;
                y: number;
                z: number;
            };
            translation: {
                x: number;
                y: number;
                z: number;
            };
            orientation: {
                x: number;
                y: number;
                z: number;
            };
            height: number;
        };
    };
    // MODEL_LIST.forEach((model) => {
    //     glbs_config[model] = {
    //         scale: {
    //             x: 1,
    //             y: 1,
    //             z: 1,
    //         },
    //         translation: {
    //             x: 0,
    //             y: 0,
    //             z: 0,
    //         },
    //         orientation: {
    //             x: 0,
    //             y: 0,
    //             z: 90,
    //         },
    //     };
    // });
    // 遍历对象
    for (const model in MODEL_LIST) {
        glbs_config[model] = {
            scale: {
                x: 1,
                y: 1,
                z: 1,
            },
            translation: {
                x: 0,
                y: 0,
                z: 0,
            },
            orientation: {
                x: 0,
                y: 0,
                z: 90,
            },
            height: MODEL_LIST[model].height,
            size: MODEL_LIST[model].size,
        };
    }
    return glbs_config;
};

export const GLBS = creat_glbs();
export const GLBS_CONFIG = creat_glbs_config();
