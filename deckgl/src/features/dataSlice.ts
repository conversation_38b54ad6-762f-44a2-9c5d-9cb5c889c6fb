/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2023-08-25 15:21:38
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-23 14:22:52
 * @FilePath: \deckgl\src\features\dataSlice.ts
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { defaultPanel } from "@/components/ConfigPage/dataPanelList";
import { createSlice } from "@reduxjs/toolkit";
import { IStoreData } from "../types";
import { TruckDebugInfo } from "@/utils/enum";
import { getUrlParam } from "@/utils";

const localConfig =
    getUrlParam("config") || localStorage.getItem("panelConfigNew");
const DEFAULTIP = location.hostname;
const initialState: IStoreData = {
    config: {
        parentApp: "",
        mapConfig: {
            mapName: "",
            egoModel: "truck_all",
            osm: false,
            zoom: 20,
            bearing: 0,
            pitch: 30,
            visualAngle: "", //视角
        },
        layoutConfig: defaultPanel,
        theme: "light",
        connectConfig: {
            ip: DEFAULTIP,
            port: 9090,
            versionPort: 9080,
            apiPort: 9070,
            rosPort: 9060,
            origin: "HMI",
        },
        mapList: [],
        tools: true,
        language: localStorage.getItem("locale") || "zh-CN",
    },
    customRender: {},
    customData: {},
    operation: {
        focus: {
            id: "",
        },
        fullScreen: false,
        showInfo: {
            id: "",
        },
        voice: false,
        clearCache: false, //清空缓存
        display: {
            type: "SETTING", //类型
            flag: false,
            isParent: false, //是否由父级传递
        },
        selectObj: {},
    },
    settings: {
        viewMode: "PERSPECTIVE", //"TOP_DOWN",
        lock: true, // 锁定视角
        measure: false, // 测量
        satelliteMap: false, // 卫星地图
        reset: false, // 重置
        debug: false, // 调试
        legend: false, // 图例
        console: false, // 控制台
        dotting: false, //打点
        transformModel: false, // 转换模型
        modelVisible: true, //3d图层是否显示 默认展示
    },
    status: {
        wsConnect: "false",
        loading: false, //是否展示全局loading
    },
    data: {},
    truckDebugInfo: TruckDebugInfo,
    showPointCloud: false,
    selectBagList: [],
    currentReplayBagInfo: null,
    playingStateHook: false,
    filterTopicList: null,
};

if (localConfig) {
    initialState.config = JSON.parse(localConfig);
    initialState.settings.viewMode = initialState.config.mapConfig.visualAngle;
}
const message = createSlice({
    name: "message",
    initialState,
    reducers: {
        setConfig: (state, action) => {
            const payload = action.payload;
            state.config = {
                ...state.config,
                ...payload,
            };
            if (!payload.parentApp) {
                localStorage.setItem(
                    "panelConfigNew",
                    JSON.stringify(state.config)
                );
            } else {
                //外层传参优先级最高
                state.settings.viewMode = payload.mapConfig.visualAngle;
            }
        },
        setOperation: (state, action) => {
            const payload = action.payload;
            state.operation = {
                ...state.operation,
                ...payload,
            };
        },
        updateData: (state, action) => {
            const payload = action.payload;
            state.data = {
                ...state.data,
                ...payload,
            };
        },
        setTruckDebugInfo: (state, action) => {
            const payload = action.payload;
            state.truckDebugInfo = {
                ...state.data,
                ...payload,
            };
        },
        setSelectBagList: (state, action) => {
            const payload = action.payload;
            state.selectBagList = payload;
        },
        setCurrentReplayBagInfo: (state, action) => {
            const payload = action.payload;
            state.currentReplayBagInfo = payload;
        },
        setFilterTopicList: (state, action) => {
            const payload = action.payload;
            state.filterTopicList = payload;
        },
        setPlayingStateHook: (state, action) => {
            const payload = action.payload;
            state.playingStateHook = payload;
        },
        setShowPointCloud: (state, action) => {
            const payload = action.payload;
            state.showPointCloud = payload;
        },
        setSettings: (state, action) => {
            const payload = action.payload;
            state.settings = {
                ...state.settings,
                ...payload,
            };
        },
        setStatus: (state, action) => {
            const payload = action.payload;
            state.status = {
                ...state.status,
                ...payload,
            };
        },
        setCustomData: (state, action) => {
            const payload = action.payload;
            state.customData = {
                ...state.customData,
                ...payload,
            };
        },
        setMapList: (state, action) => {
            const payload = action.payload;
            state.config.mapList = payload;
        },
    },
});

export const {
    setConfig,
    setOperation,
    updateData,
    setTruckDebugInfo,
    setSelectBagList,
    setCurrentReplayBagInfo,
    setFilterTopicList,
    setPlayingStateHook,
    setShowPointCloud,
    setSettings,
    setStatus,
    setCustomData,
    setMapList,
} = message.actions;

export default message.reducer;
