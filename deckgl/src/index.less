@import url(@/theme/mixin.less);
:root {
    font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;

    color-scheme: light dark;
    color: rgba(255, 255, 255, 0.87);
    background-color: #242424;

    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
}

a {
    font-weight: 500;
    color: #646cff;
    text-decoration: inherit;
}
a:hover {
    color: #535bf2;
}

body {
    margin: 0;
    display: flex;
    place-items: center;
}

h1 {
    font-size: 3.2em;
    line-height: 1.1;
}

button {
    border-radius: 8px;
    border: 1px solid transparent;
    padding: 0.6em 1.2em;
    font-size: 1em;
    font-weight: 500;
    font-family: inherit;
    background-color: #1a1a1a;
    cursor: pointer;
    transition: border-color 0.25s;
}
button:hover {
    border-color: #646cff;
}
button:focus,
button:focus-visible {
    outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
    :root {
        color: #213547;
        background-color: #ffffff;
    }
    a:hover {
        color: #747bff;
    }
    button {
        background-color: #f9f9f9;
    }
}

.ant-select {
    .ant-select-selector {
        border-radius: 2px;
    }
    .ant-select-selection-item {
        height: 100%;
        line-height: 22px;
        font-size: 14px;
        font-family: "SourceHanSansCNMedium";
        font-weight: 400;
        color: #0f151d;
    }
}

.dark {
    .ant-select {
        .ant-select-selector {
            border: 1px solid #5d656c !important;
            background-color: #262933;
            .ant-select-selection-item {
                color: #e9eef9 !important;
            }
            .ant-select-selection-search {
                .ant-select-selection-search-input {
                    color: @config-title-font-color !important;
                }
            }
        }
        .ant-select-arrow {
            color: #5d656c;
        }
    }
    .bag-tree {
        .ant-tree-treenode {
            &:hover {
                span {
                    color: @config-title-font-color !important;
                }
            }
        }
    }
}

.config-dropdown {
    border-radius: 2px;
    background: @dropdown-bg-color;
    .ant-select-item {
        min-height: 24px;
        padding: 5px;
        line-height: 24px;
        border-radius: 2px;
        font-size: 14px;
        color: @dropdown-font-color;
        &:hover {
            color: @dropdown-font-color;
            background-color: @dropdown-select-hover-bg-color !important;
        }
    }
    .ant-select-item-option-selected,
    .ant-select-item-option-active {
        border-radius: 2px;
        color: @dropdown-font-color !important;
        background-color: @dropdown-select-item-option-selected !important;
    }
}

.mapboxgl-ctrl-group button {
    background: @mapboxgl-ctrl-bg-color;
}

.ant-popover-inner {
    padding: 5px !important;
}
.ant-popover {
    .ant-popover-arrow {
        &:before {
            background: @dropdown-bg-color;
        }
    }
}
.speech-modal {
    .ant-modal-title,
    .ant-modal-footer {
        text-align: center !important;
    }
    .ant-modal-content {
        background: @dialog-bg-color;
        box-shadow: @panel-bs-color;
        .ant-modal-header {
            background: transparent;
            .ant-modal-title {
                color: @panel-font-color;
            }
        }
        .ant-modal-confirm-title {
            text-align: center;
            color: @panel-font-color;
        }
        .ant-modal-confirm-btns {
            text-align: center;
        }
    }
}

.topic-table {
    .ant-table {
        color: @dropdown-font-color;
        background: @dropdown-bg-color;
        .ant-table-thead {
            tr {
                th {
                    color: @dropdown-font-color;
                    background-color: @dropdown-select-item-option-selected;
                    padding: 8px !important;
                }
            }
        }
        .ant-table-tbody {
            tr:not(.ant-table-placeholder) {
                td {
                    padding: 8px !important;
                }

                &:hover {
                    td {
                        background-color: @dropdown-select-hover-bg-color !important;
                    }
                }
            }
            .ant-table-cell-row-hover {
                background-color: @dropdown-select-hover-bg-color !important;
            }
        }
    }
    .ant-table-empty {
        tr {
            &:hover {
                td {
                    background: @dropdown-bg-color !important;
                }
            }
        }
    }
}
.dot-table {
    width: 600px;
    margin: 10px;
    table {
        max-height: 200px;
        overflow-y: auto;
    }
    .ant-table {
        background: transparent !important;
    }
    .ant-table-container {
        border: 1px solid @config-input-bd-color !important;
        border-bottom: none !important;
    }
    .ant-table-cell.action {
        .ant-btn {
            margin: auto;
        }
    }
    .ant-table-cell {
        background: @config-bg-color !important;
        border-bottom: 1px solid @config-input-bd-color !important;
    }
}
// 自动移滚动条样式
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}
// 滑块部分
::-webkit-scrollbar-thumb {
    border-radius: 1em;
    background-color: @config-scrollbar-thumb-bg-color;
}
// 辊道部分
::-webkit-scrollbar-track {
    border-radius: 1em;
    background-color: @config-scrollbar-track-bg-color;
}

//地图告警样式
.map-alarm {
    &::after {
        content: "";
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        width: calc(100% - 200px);
        height: calc(100% - 200px);
        animation: blink 1s infinite;
        z-index: 1;
        box-shadow: 10px 10px 100px rgba(255, 0, 0, 0.3) inset;
        border-radius: 70px;
        margin: 100px;
        outline: 200px solid rgba(255, 0, 0, 0.3);
        filter: blur(50px);
    }
    #map-wrapper {
        z-index: 2 !important;
    }
}
