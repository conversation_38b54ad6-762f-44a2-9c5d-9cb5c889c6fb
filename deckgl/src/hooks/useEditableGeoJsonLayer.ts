import { useEffect, useState, useMemo, useCallback } from "react";
import {
    EditableGeoJsonLayer,
    DrawLineStringMode,
    DrawPolygonMode,
    ModifyMode,
    CompositeMode,
    MeasureDistanceMode,
    MeasureAreaMode,
} from "@deck.gl-community/editable-layers";
import { PathStyleExtension } from "@deck.gl/extensions";
// 使用 deck.gl 提供的类型作为类型断言
import type { FeatureCollection as DeckFeatureCollection } from "@deck.gl-community/editable-layers/dist/utils/geojson-types";

interface FeatureCollection {
    type: "FeatureCollection";
    features: Feature[];
}
interface Feature {
    type: "Feature";
    geometry: Geometry;
    properties: { [key: string]: any };
}
type Geometry = Point | LineString | Polygon;

interface Point {
    type: "Point";
    coordinates: [number, number]; // [经度, 纬度]
}

interface LineString {
    type: "LineString";
    coordinates: [number, number][]; // 多个点坐标
}

interface Polygon {
    type: "Polygon";
    coordinates: [number, number][][]; // 多个线环，由多个点坐标组成
}

const useEditableGeoJsonLayer = (mode: "polygon" | "line" | null) => {
    const [features, setFeatures] = useState<FeatureCollection>({
        type: "FeatureCollection",
        features: [],
    });

    const [selectedFeatureIndexes, setSelectedFeatureIndexes] = useState<
        number[]
    >([]);

    // 使用 useMemo 缓存 drawModeFct 的结果
    const drawMode = useMemo(() => {
        if (!mode) return null;

        switch (mode) {
            case "polygon":
                return new CompositeMode([
                    new DrawPolygonMode(),
                    new ModifyMode(),
                ]);
            case "line":
                // 使用 MeasureDistanceMode 以保持原有功能
                return new MeasureDistanceMode();
            default:
                return null;
        }
    }, [mode]);

    // 使用 useCallback 缓存 drawLayer 的创建
    const drawLayer = useCallback(() => {
        if (!drawMode) return null;

        return new EditableGeoJsonLayer({
            id: "draw-layer",
            pickable: true,
            // 使用类型断言解决类型问题
            data: features as unknown as DeckFeatureCollection,
            mode: drawMode,
            selectedFeatureIndexes,
            // 性能优化设置
            updateTriggers: {
                // 仅在真正需要更新时触发
                getLineColor: mode,
                getFillColor: mode,
            },
            modeConfig: {
                centerTooltipsOnLine: false, // 保持原有配置
                turfOptions: { units: "meters" },
                formatTooltip: (distance: number) => {
                    return " " + distance.toFixed(2) + " m";
                },
            },
            // 保持原有样式设置
            editHandleIconSizeScale: 10,
            getEditHandlePointColor: [0, 100, 255, 200],
            getEditHandlePointOutlineColor: [0, 100, 255, 255],
            _subLayerProps: {
                // 填充
                geojson: {
                    getLineColor: [255, 100, 0, 255],
                    getFillColor: [255, 100, 0, 10],
                    getDashArray: [10, 10],
                    dashJustified: true,
                    extensions: [
                        new PathStyleExtension({
                            dash: true,
                            highPrecisionDash: false,
                        }),
                    ],
                    // 性能优化
                    parameters: {
                        depthTest: false,
                    },
                },
                // 编辑线
                guides: {
                    getLineColor: [255, 100, 0, 255],
                    getDashArray: [5, 5],
                    dashJustified: true,
                    extensions: [
                        new PathStyleExtension({
                            dash: true,
                            highPrecisionDash: false,
                        }),
                    ],
                    getTextColor: [255, 0, 0, 255],
                    // 性能优化
                    parameters: {
                        depthTest: false,
                    },
                },
                tooltips: {
                    getColor: [0, 200, 200],
                    getSize: 14,
                    background: true,
                    backgroundPadding: [0, 1, 5, 0],
                    getBackgroundColor: [100, 200, 0, 20],
                    getBorderColor: [255, 200, 200],
                    getBorderWidth: 1,
                    fontWeight: "bold",
                    getTextAnchor: "start",
                    getPixelOffset: [10, 0],
                    fontFamily: "sans-serif",
                    // 性能优化
                    parameters: {
                        depthTest: false,
                    },
                },
            },
            // 优化编辑回调
            onEdit: ({
                updatedData,
                editType,
                featureIndexes,
                editContext,
            }) => {
                let updatedSelectedFeatureIndexes = selectedFeatureIndexes;

                // 性能优化：忽略移动位置的频繁更新
                if (editType === "movePosition") {
                    return;
                }

                if (editType === "addFeature") {
                    const { featureIndexes } = editContext;
                    updatedSelectedFeatureIndexes = [
                        ...selectedFeatureIndexes,
                        ...featureIndexes,
                    ];
                }

                // 使用类型断言解决类型问题
                setFeatures(updatedData as unknown as FeatureCollection);
                setSelectedFeatureIndexes(updatedSelectedFeatureIndexes);
            },
        });
    }, [features, drawMode, selectedFeatureIndexes, mode]);

    // 清除 features 和 selectedFeatureIndexes
    const clearFeatures = useCallback(() => {
        setFeatures({
            type: "FeatureCollection",
            features: [],
        });
        setSelectedFeatureIndexes([]);
    }, []);

    // 只有在 mode 变化时才清空 features 和 selectedFeatureIndexes
    useEffect(() => {
        clearFeatures();
    }, [mode, clearFeatures]);

    return {
        drawLayer: drawLayer(),
        features,
        clearFeatures,
    };
};

export default useEditableGeoJsonLayer;
