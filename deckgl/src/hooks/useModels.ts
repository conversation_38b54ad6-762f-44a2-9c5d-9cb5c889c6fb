/*
 * @Author: jack <EMAIL>
 * @Date: 2023-07-18 15:49:28
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2023-08-03 17:41:36
 * @FilePath: /deckgl/src/hooks/useModels.ts
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
// useModels.js

import { load } from "@loaders.gl/core";
import { GLTFLoader } from "@loaders.gl/gltf";
import { DracoLoader } from "@loaders.gl/draco";

import { useState, useEffect } from "react";

export default function useModels() {
    const modelPaths = [
        "art",
        "bicycle",
        "bus",
        "head",
        "motorcycle",
        "person",
        "trailer_nobox",
        "trailer",
        "truck_all",
        "truck_normal",
        "truck",
        "vehicle",
    ];
    const [models, setModels] = useState({});

    useEffect(() => {
        Promise.all(
            modelPaths.map((path) =>
                load("/glb/" + path + ".glb", GLTFLoader, {
                    DracoLoader,
                })
            )
        ).then((data) => {
            let models = {} as any;
            data.forEach((model, index) => {
                models[modelPaths[index]] = model;
            });
            setModels(models);
        });
    }, []);

    return models;
}
