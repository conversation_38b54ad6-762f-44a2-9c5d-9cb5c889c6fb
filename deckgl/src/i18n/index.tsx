/*
 * @Author: fanmx <EMAIL>
 * @Date: 2025-07-23 13:41:06
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 16:14:46
 * @FilePath: \deckgl\src\i18n\index.tsx
 * @Description:
 *
 * Copyright (c) 2025 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import zhCN from "antd/locale/zh_CN";
import enUS from "antd/locale/en_US";
import zh_CN from "./locales/zh-CN";
import en_US from "./locales/en-US";
import type { Locale } from "antd/es/locale";

// 定义支持的语言类型
export type SupportedLanguages = "zh-CN" | "en-US";

// 存储 AntD 语言包的映射关系
export const antdLocales: Record<SupportedLanguages, Locale> = {
    "zh-CN": zhCN,
    "en-US": enUS,
};

// 翻译资源
const messages = {
    "zh-CN": {
        ...zh_CN,
        ...zhCN,
    },
    "en-US": {
        ...en_US,
        ...enUS,
    },
};

// 初始化 i18n
i18n.use(initReactI18next) // 集成到 React
    .init({
        resources: messages,
        lng: localStorage.getItem("locale") || "zh-CN", // 默认语言，优先从本地存储读取
        fallbackLng: "zh-CN", //  fallback 语言
        interpolation: {
            escapeValue: false, // React 已经安全处理了 XSS
        },
        react: {
            useSuspense: false, // 不使用 Suspense
        },
    });

export default i18n;
