import {
    createContext,
    useContext,
    useState,
    useEffect,
    ReactNode,
} from "react";
import { ConfigProvider } from "antd";
import { useTranslation } from "react-i18next";
import { SupportedLanguages, antdLocales } from "../i18n";

// 定义上下文类型
type I18nContextType = {
    currentLang: SupportedLanguages;
    changeLang: (lang: SupportedLanguages) => void;
};

// 创建上下文
const I18nContext = createContext<I18nContextType | undefined>(undefined);

// 国际化提供者组件
export const I18nProvider = ({ children }: { children: ReactNode }) => {
    const { i18n } = useTranslation();
    const [currentLang, setCurrentLang] = useState<SupportedLanguages>(
        i18n.language as SupportedLanguages
    );
    const [antdLocale, setAntdLocale] = useState(antdLocales[currentLang]);

    // 当语言变化时更新 AntD 语言包
    useEffect(() => {
        setAntdLocale(antdLocales[currentLang]);
        localStorage.setItem("locale", currentLang);
    }, [currentLang]);

    // 切换语言的方法
    const changeLang = (lang: SupportedLanguages) => {
        if (lang !== currentLang) {
            setCurrentLang(lang);
            i18n.changeLanguage(lang);
        }
    };

    return (
        <I18nContext.Provider value={{ currentLang, changeLang }}>
            <ConfigProvider locale={antdLocale}>{children}</ConfigProvider>
        </I18nContext.Provider>
    );
};

// 自定义 Hook 方便组件使用
export const useI18n = () => {
    const context = useContext(I18nContext);
    if (!context) {
        throw new Error("useI18n must be used within an I18nProvider");
    }
    return context;
};
