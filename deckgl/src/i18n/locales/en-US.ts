export default {
    common: {
        emptyText: "No Data",
        operate: "Operate",
        ok: "Confirm",
        cancel: "Cancel",
        confirm: "Confirm",
    },
    menu: {
        REPLAYDATA: "Data",
        POINTCLOUD: "Point Cloud",
        SHARE: "Share",
        LANGUAGE: "Switch Language",
        CLEARCACHE: "Clear Cache",
        CALIBRATION: "Calibration",
        VERSION: "Version Info",
        SETTING: "Settings",
    },
    map: {
        viewMode: "View Mode",
        viewLock: "View Lock",
        reset: "Reset",
        debug: "Debug",
        measure: "Measure",
        mapMarker: "Map Marker",
        satelliteMap: "Satellite Map",
        enableMapFirst: "Please enable map first",
        mapLegend: "Map Legend",
        connectionStatus: "Connection Status",
        successAndPushing: "Connected successfully and data is pushing",
        successButNoData:
            "Connected successfully but no data received for more than 1ms",
        notConnected: "Not connected yet",
    },
    fault: {
        sensor: "Sensor",
        pnc: "PNC",
        location: "Location",
        perception: "Perception",
        map: "Map",
        guardian: "Guardian",
        system: "System",
        status: "Status",
    },
    debug: {
        upload: "Upload",
        selectFile: "Please select file",
        invalidJson: "File content is not valid JSON format",
        clear: "Clear",
        debug: "Debug",
        visualization: "Visualization",
        perception: "Perception",
        planning: "Planning & Control",
        simulation: "Simulation",
        generateTrackEnvelope: "Generate Track Envelope",
        vehicleFront: "Headstock",
        trailer: "Trailer",
        dynamicRanging: "Dynamic Ranging",
        model3d: "3D Model",
        speedLimitSign: "Speed Limit Sign",
    },
    description: {
        elementLegend: "Map Element Legend",
        shortcutLegend: "Shortcut Key Legend",
        satelliteMap: "Satellite Map",
        legend: "Legend",
        view2d: "2D View",
        view3d: "3D View",
        viewLock: "View Lock",
        measure: "Measure",
        modelTransparency: "Model Transparency",
        console: "Console",
        debug: "Debug",
        menuBar: "Menu Bar",
        reset: "Reset",
    },
    enum: {
        truck: {
            width: "Width",
            length: "Length",
        },
        trailer: {
            width: "Width",
            length: "Length",
        },
        visualization: {
            model3d: "3D Model",
            speedLimitSign: "Speed Limit Sign",
        },
        perception: {
            fusionPointCloud: "Perception OGM Points",
            obstaclePointCloud: "Obstacle Point Cloud",
            obstaclePrediction: "Obstacle Prediction",
            obstacleEnvelope: "Obstacle Envelope",
            obstacleDetails: "Obstacle Details",
            visualLaneLines: "Visual Lane Lines",
            coastlinePointCloud: "Coastline Point Cloud",
            modelTrackingBox: "Model Tracking Box",
            modelDetectionBox: "Model Detection Box",
        },
        planning: {
            decisionReferenceLine: "Decision Reference Line",
            decisionBoundary: "Decision Boundary",
            realTimeTrajectory: "Real-time Trajectory",
            mapSegmentation: "Map Segmentation",
            trajectoryEnvelope: "Trajectory Envelope",
            aebEnvelope: "AEB Envelope",
            drivableArea: "Drivable Area",
        },
        simulation: {
            details: "Simulation Details",
        },
        distance: {
            dynamic: "Dynamic Distance",
        },
        playerView: {
            minimize: "Minimize",
            custom: "Split Screen",
            maximize: "Full Screen",
            float: "Float",
        },
        stopReason: {
            driving: "Driving",
            softStop: "Stopped at soft stop point",
            emergencyStop: "Stopped at emergency stop point",
            obstacleDecision: "Stopped due to decision obstacle",
            nonAutonomous: "Non-autonomous driving",
            manualEstop: "Manual E-STOP",
            mapPathDeviation: "Excessive deviation from map path",
            idle: "Idle",
            speedLimitDecision: "Stopped due to speed limit decision",
            obstaclePlanning: "Stopped due to planning obstacle",
            destination: "Stopped at destination",
            trajectoryShortage: "Stopped due to insufficient trajectory length",
            highCurvature: "Stopped due to excessive curvature",
            controlDeviation: "Stopped due to excessive path deviation",
            mapSpeedLimit: "Stopped due to map speed limit",
            insufficientThrottle: "Stopped due to insufficient throttle",
            dockingCompleted: "Docking completed",
            aebTriggered: "Stopped due to AEB activation",
            locationJump: "Stopped due to positioning jump",
            antiFalling: "Anti-falling triggered",
            serpentineDriving: "Serpentine driving",
            chassisDelay: "Chassis delay detected",
            abruptSteering: "Abrupt steering detected",
            faultWithErrorCode: "Stopped due to fault, check error_code",
        },
        playback: {
            single: "Single Package Loop",
            list: "List Loop",
            range: "Range Loop",
            frame: "Single Frame Playback",
        },
    },
    operation: {
        zoomIn: "Zoom In",
        restore: "Restore",
        setSpeed: "Set Speed",
        noCameraInfo: "No Camera Information",
        observationValue: "Observation Value",
        planningValue: "Planning Value",
    },
    dotting: {
        invalidFormat: "Input is not a valid JSON format",
        missingUTMZone: "Input is missing the UTMZone field",
        missingXYYaw: "Input is missing x, y, yaw fields",
        utmZoneRange: "UTMZone value must be between 1-60",
        requiredFormat: "Please enter JSON format, must include UTMZone field",
        plotPoints: "Data Point Plotting",
    },
    bag: {
        list: "Bag List",
        details: "Bag Details",
        size: "Bag Size",
        startTime: "Start Time",
        endTime: "End Time",
        duration: "Bag Duration",
        placeholder: "Please enter filter criteria",
    },
    mapConfig: {
        mapConfiguration: "Map Configuration",
        mapName: "Map Name",
        searchMapPlaceholder: "Search Map Name",
        loading: "Loading",
        vehicleHot: "Vehicle Model",
        searchVehiclePlaceholder: "Search Host Vehicle Model",
    },
    message: {
        clearCache: "Are you sure you want to clear the cache?",
        finishEditFirst:
            "Please finish range loop editing first, then click the confirm button",
    },
    configPage: {
        connection: "Connection Configuration",
        inputPlaceholder: "Please enter",
        dataType: "Data Type",
        appearanceTheme: "Appearance Theme",
        on: "On",
        off: "Off",
        customComponent: "Custom Component",
        saveComponentConfig: "Save Component Configuration",
        noSuchComponent: "No custom component of this type",
    },
    version: {
        title: "Version Record",
    },
    replayControl: {
        mode: "Playback Mode",
        speed: "Speed",
        start: "Start",
        end: "End",
        switchTypeTip: "Click to switch time type",
    },
};
