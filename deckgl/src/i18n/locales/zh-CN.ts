export default {
    common: {
        emptyText: "暂无数据",
        operate: "操作",
        ok: "确定",
        cancel: "取消",
        confirm: "确认",
    },
    menu: {
        REPLAYDATA: "数据",
        POINTCLOUD: "点云",
        SHARE: "分享",
        LANGUAGE: "中英文切换",
        CLEARCACHE: "清除缓存",
        CALIBRATION: "标定",
        VERSION: "版本信息",
        SETTING: "设置",
    },
    map: {
        viewMode: "视角模式",
        viewLock: "视角锁定",
        reset: "复位",
        debug: "调试",
        measure: "测量",
        mapMarker: "地图打点",
        satelliteMap: "卫星地图",
        enableMapFirst: "请先开启地图",
        mapLegend: "地图图示",
        connectionStatus: "连接状态",
        successAndPushing: "连接成功且数据推送中",
        successButNoData: "连接成功但超过1ms未收到数据",
        notConnected: "暂未连接成功",
    },
    fault: {
        sensor: "传感器",
        pnc: "PNC",
        location: "定位",
        perception: "感知",
        map: "地图",
        guardian: "guardian",
        system: "系统",
        status: "状态",
    },
    debug: {
        upload: "上传",
        selectFile: "请选择文件",
        invalidJson: "文件内容不是有效的JSON格式",
        clear: "清空",
        debug: "调试",
        visualization: "可视化",
        perception: "感知",
        planning: "规控",
        simulation: "仿真",
        generateTrackEnvelope: "生成轨迹包络",
        vehicleFront: "车头",
        trailer: "车挂",
        dynamicRanging: "动态测距",
        model3d: "3D模型",
        speedLimitSign: "限速标志",
    },
    description: {
        elementLegend: "地图元素图示",
        shortcutLegend: "快捷键图示",
        satelliteMap: "卫星地图",
        legend: "图例",
        view2d: "2D视角",
        view3d: "3D视角",
        viewLock: "视角锁定",
        measure: "测量",
        modelTransparency: "模型透明",
        console: "控制台",
        debug: "调试",
        menuBar: "菜单栏",
        reset: "重置",
    },
    enum: {
        truck: {
            width: "宽度",
            length: "长度",
        },
        trailer: {
            width: "宽度",
            length: "长度",
        },
        visualization: {
            model3d: "3D模型",
            speedLimitSign: "限速标志",
        },
        perception: {
            fusionPointCloud: "感知ogm点",
            obstaclePointCloud: "障碍物点云",
            obstaclePrediction: "障碍物预测",
            obstacleEnvelope: "障碍物包络",
            obstacleDetails: "障碍物详情",
            visualLaneLines: "视觉车道线",
            coastlinePointCloud: "海岸线点云",
            modelTrackingBox: "模型追踪框",
            modelDetectionBox: "模型检测框",
        },
        planning: {
            decisionReferenceLine: "决策参考线",
            decisionBoundary: "决策边界",
            realTimeTrajectory: "实时轨迹",
            mapSegmentation: "地图分段",
            trajectoryEnvelope: "轨迹包络",
            aebEnvelope: "AEB包络",
            drivableArea: "可行驶区",
        },
        simulation: {
            details: "仿真详情",
        },
        distance: {
            dynamic: "动态距离",
        },
        playerView: {
            minimize: "隐藏",
            custom: "分屏",
            maximize: "全屏",
            float: "悬浮",
        },
        stopReason: {
            driving: "行驶中",
            softStop: "缓停点停车",
            emergencyStop: "急停点停车",
            obstacleDecision: "决策障碍物停车",
            nonAutonomous: "非自动驾驶",
            manualEstop: "手拨ESTOP",
            mapPathDeviation: "地图下发路径偏离过大",
            idle: "空闲",
            speedLimitDecision: "决策限速停车",
            obstaclePlanning: "规划障碍物停车",
            destination: "终点停车",
            trajectoryShortage: "轨迹长度不足导致停车",
            highCurvature: "曲率过大停车",
            controlDeviation: "控制偏离路径过远停车",
            mapSpeedLimit: "地图限速停车",
            insufficientThrottle: "油门不足导致停车",
            dockingCompleted: "对位完成",
            aebTriggered: "AEB导致停车",
            locationJump: "定位跳变导致停车",
            antiFalling: "防坠海触发",
            serpentineDriving: "画龙",
            chassisDelay: "监测到底盘延迟",
            abruptSteering: "监测到急打转向",
            faultWithErrorCode: "故障停车，需查看error_code",
        },
        playback: {
            single: "单包循环",
            list: "列表循环",
            range: "区间循环",
            frame: "单帧播放",
        },
    },
    operation: {
        zoomIn: "放大",
        restore: "还原",
        setSpeed: "设置车速",
        noCameraInfo: "无摄像头信息",
        observationValue: "观测值",
        planningValue: "规划值",
    },
    dotting: {
        invalidFormat: "输入内容不是有效的 JSON 格式",
        missingUTMZone: "输入内容缺少 UTMZone 字段",
        missingXYYaw: "输入内容缺少 x,y,yaw 字段",
        utmZoneRange: "UTMZone字段值必须在1-60之间",
        requiredFormat: "请输入json格式，需包含UTMZone字段",
        plotPoints: "数据点绘制",
    },
    bag: {
        list: "Bag列表",
        details: "Bag详情",
        size: "bag大小",
        startTime: "开始时间",
        endTime: "结束时间",
        duration: "bag时长",
        placeholder: "请输入过滤条件",
    },
    mapConfig: {
        mapConfiguration: "地图配置",
        mapName: "地图名称",
        searchMapPlaceholder: "搜索地图名称",
        loading: "加载中",
        vehicleHot: "主车模型",
        searchVehiclePlaceholder: "搜索主车模型",
    },
    message: {
        clearCache: "是否确定清空缓存？",
        finishEditFirst: "请先结束区间循环编辑，点击确认按钮",
    },
    configPage: {
        connection: "连接配置",
        inputPlaceholder: "请输入",
        dataType: "数据类型",
        appearanceTheme: "外观主题",
        on: "开",
        off: "关",
        customComponent: "自定义组件",
        saveComponentConfig: "保存组件配置",
        noSuchComponent: "暂无此类型自定义组件",
    },
    version: {
        title: "版本记录",
    },
    replayControl: {
        mode: "播放模式",
        speed: "倍 速",
        start: "开始",
        end: "结束",
        switchTypeTip: "点击切换时间类型",
    },
};
