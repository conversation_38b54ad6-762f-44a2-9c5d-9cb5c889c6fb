/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2023-02-01 16:22:36
 * @LastEditors: jack 501177081.com
 * @LastEditTime: 2025-07-22 10:27:16
 * @FilePath: /deckgl/src/request/bag.ts
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { IReplayTopic } from "@/types";
import http from "./index";
const bagBase = "/root/bags";

/**
 * @description: 构建基础URL
 * @param {string} ip
 * @param {number} apiPort
 * @return {string}
 */
const getBaseUrl = (ip: string, apiPort: number) => {
    return `http://${ip}:${apiPort}/api/replayer/`;
};

/**
 * @description: 获取bag列表
 * @param {string} ip
 * @param {number} apiPort
 * @param {string} path
 * @return {*}
 */
export const getBags = (
    ip: string,
    apiPort: number = 9070,
    path: any = null
) => {
    const baseUrl = getBaseUrl(ip, apiPort);
    return http.post(baseUrl + "/lists", {
        path: path ? bagBase + path : bagBase,
        match_bag_name: true,
    });
};
/**
 * @description: 获取bag包详情
 * @param {string} ip
 * @param {number} apiPort
 * @param {any} pathObj
 * @return {*}
 */
export const getDetail = (ip: string, apiPort: number = 9070, pathObj: any) => {
    const baseUrl = getBaseUrl(ip, apiPort);
    let bags: any;
    if (typeof pathObj === "string") {
        bags = bagBase + pathObj;
    } else {
        const { path, matched_group } = pathObj;

        if (path === "/") {
            // 根目录情况特殊处理
            bags = matched_group.map((item: any) => {
                return bagBase + "/" + item;
            });
        } else {
            bags = matched_group.map((item: any) => {
                return bagBase + path + "/" + item;
            });
        }
    }
    return http.post(
        baseUrl + "/select",
        {
            options: {
                bags,
            },
        },
        {
            showLoading: true,
        } as any
    );
};
/**
 * @description: 过滤topic
 * @param {string} ip
 * @param {number} apiPort
 * @param {string} topic
 * @param {boolean} filter
 * @return {*}
 */
export const filterTopic = ({
    ip,
    apiPort = 9070,
    topic,
    filter,
}: {
    ip: string;
    apiPort?: number;
    topic: string;
    filter: boolean;
}) => {
    const baseUrl = getBaseUrl(ip, apiPort);
    return http.post(baseUrl + "/filter_topic", {
        topic,
        filter,
    });
};

/**
 * @description: 进度条倍速控制
 * @param {string} ip
 * @param {number} apiPort
 * @param {number} speed_rate
 * @return {*}
 */
export const setSpeedControl = (
    ip: string,
    apiPort: number = 9070,
    speed_rate: number
) => {
    const baseUrl = getBaseUrl(ip, apiPort);
    return http.post(baseUrl + "/speed_control", {
        speed_rate,
    });
};

/**
 * @description: 开启播放 / 暂停播放
 * @param {string} ip
 * @param {number} apiPort
 * @param {string} status
 * @return {*}
 */
export const setStartPause = (
    ip: string,
    apiPort: number = 9070,
    status: string
) => {
    const baseUrl = getBaseUrl(ip, apiPort);
    return http.post(baseUrl + "/mode", {
        mode: status,
    });
};

/**
 * @description: 清空选中的包
 * @param {string} ip
 * @param {number} apiPort
 * @return {*}
 */
export const clearSelectBag = (ip: string, apiPort: number = 9070) => {
    const baseUrl = getBaseUrl(ip, apiPort);
    return http.get(baseUrl + "/clear");
};

/**
 * @description: 跳转
 * @param {string} ip
 * @param {number} apiPort
 * @param {number} time
 * @return {*}
 */
export const jump = (ip: string, apiPort: number = 9070, time: number) => {
    const baseUrl = getBaseUrl(ip, apiPort);
    return http.post(baseUrl + "/jump", {
        jump_time: time,
    });
};

export const adjustBagInfoByBagDetail = (
    bagName: string,
    {
        topics,
        topics_lost,
        bag_length,
        bag_start_time,
        bag_size,
        topics_md5,
    }: {
        topics: IReplayTopic[];
        topics_lost: string[];
        topics_md5: any[];
        bag_length: number;
        bag_start_time: number;
        bag_size: string;
    }
) => {
    const topics_error: string[] = [];
    if (topics_md5) {
        topics_md5.forEach((item) => {
            if (!item.check_md5) {
                topics_error.push(item.name);
            }
        });
    }
    let info = {
        topics,
        topics_lost,
        topics_error,
        bag_size,
        bag_length,
        bag_start_time,
        bag_end_time: bag_length + bag_start_time,
        bag_name: bagName,
    };
    return info;
};
