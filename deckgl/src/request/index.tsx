/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2023-02-01 16:22:36
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2023-09-08 13:29:11
 * @FilePath: /deckgl/src/request/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import axios from "axios";
import Loading from "@/components/Loading";
import { createRoot } from "react-dom/client";
const http = axios.create({
    baseURL: "", // `http://${localPlayerConfig?.connectConfig?.ip}:9070/api`,
    timeout: 30000,
});

// 当前正在请求的数量
let requestCount = 0;
/**
 * @description: 显示loading
 * @return {*}
 */
const handleShowLoading = () => {
    if (requestCount == 0) {
        var dom = document.createElement("div");
        dom.setAttribute("id", "loading");
        document.body.appendChild(dom);
        const root = createRoot(dom);
        root.render(<Loading />);
    }
    requestCount++;
};
/**
 * @description: 隐藏loading
 * @return {*}
 */
const hanldeHideLoading = () => {
    requestCount--;
    if (requestCount == 0) {
        document.body.removeChild(document.getElementById("loading"));
    }
};
// 请求前拦截
http.interceptors.request.use(
    (config) => {
        // requestCount为0，才创建loading, 避免重复创建
        if (config?.showLoading) {
            handleShowLoading();
        }
        return config;
    },
    (err) => {
        // 判断当前请求是否设置了不显示Loading
        if (err.config.showLoading) {
            hanldeHideLoading();
        }
        console.log(err.message);
        return Promise.reject(err);
    }
);

// 返回后拦截
http.interceptors.response.use(
    (res) => {
        let { status, config, data } = res;
        if (config?.showLoading) {
            hanldeHideLoading();
        }
        if (status === 200) {
            return Promise.resolve(data);
        }
        return Promise.reject(res);
    },
    (error) => {
        if (error?.config?.showLoading) {
            hanldeHideLoading();
        }
        try {
            let status = error?.response?.status,
                detail = error?.response?.data?.detail;
            if (status) {
                // 状态码超过 2xx 范围时都会调用该函数，处理错误响应
                switch (status) {
                    case 404:
                        message.error("请求路径找不到！");
                        break;
                    case 500:
                        message.error("服务器无法请求！");
                        break;
                    default:
                        message.error(detail);
                        break;
                }
            }
            return Promise.reject(error);
        } catch (error) {
            console.log(error.message);
            return Promise.reject(error);
        }
    }
);

export default http;
