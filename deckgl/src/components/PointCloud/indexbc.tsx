/*
 * @Author: luofei luofei.trunk.tech
 * @Date: 2023-03-16 18:07:02
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2023-04-28 15:41:57
 * @FilePath: /deckgl/src/components/PointCloud/indexbc.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { useRef, useState, Suspense, useEffect, useMemo } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import {
    OrbitControls,
    Sky,
    Points,
    Grid,
    Point,
    PerformanceMonitor,
    PivotControls,
    OrthographicCamera,
} from "@react-three/drei";
import * as THREE from "three";
import "./index.less";

function Lidar(props) {
    const mesh = useRef(null);
    const { positions, color, rotation, translation } = props;
    useFrame((state, delta) => {
        mesh.current.position.x = translation.x;
        mesh.current.position.y = translation.y;
        mesh.current.position.z = translation.z;
        mesh.current.quaternion.x = rotation.qx;
        mesh.current.quaternion.y = rotation.qy;
        mesh.current.quaternion.z = rotation.qz;
        mesh.current.quaternion.w = rotation.qw;
    });
    return (
        <Points ref={mesh} positions={positions} limit={100000} range={100000}>
            <pointsMaterial color={color} size={2} />
        </Points>
    );
}

export default function App({ data }: { data: any }) {
    const [positionsBuffer, setPositionsBuffer] = useState();
    const [bufferList, setBufferList] = useState<any>([]);

    const [topicList, setTopicList] = useState<{
        topic: "lidar";
        color: "red";
        buffer: Buffer;
        position: { x: number; y: number; z: number };
        rotation: { x: number; y: number; z: number };
    }>([]);

    // 生产长度为1000000的数组
    const positions = () => {
        const positions = [];
        for (let i = 0; i < 100000; i++) {
            positions.push(Math.random() * 10 - 5);
        }

        return new Float32Array(positions);
    };
    useEffect(() => {
        // setTimeout(() => {
        //     setInterval(() => {
        //         setBufferList([
        //             {
        //                 positions: positions(),
        //             },
        //         ]);
        //         // setPositionsBuffer(positions());
        //     }, 100);
        // }, 1000);
        // fetch("/lidar.json")
        //     .then((res) => {
        //         return res.json();
        //     })
        //     .then((res) => {
        //         console.log(res);
        //         const data = res[0];
        //         let list = [];
        //         res.map((item: any) => {
        //             list.push({
        //                 positions: new Float32Array(item.msg),
        //                 translation: item.tf.translation,
        //                 rotation: item.tf.rotation,
        //                 topic: item.topic,
        //             });
        //         });
        //         console.log(list);
        //         // setBufferList(list);
        //         // setBufferList([
        //         //     {
        //         //         positions: new Float32Array(data.msg),
        //         //         translation: data.tf.translation,
        //         //         rotation: data.tf.rotation,
        //         //         topic: data.topic,
        //         //     },
        //         // ]);
        //     });
    }, []);

    useEffect(() => {
        const pointCloud = data?.pointCloud;
        if (pointCloud?.length > 0) {
            setBufferList(pointCloud);
        }
    }, [data]);
    useEffect(() => {}, [bufferList]);
    const [dpr, setDpr] = useState(1.5);

    const colorList = ["red", "green", "blue", "yellow", "orange"];
    return (
        <Canvas dpr={dpr}>
            <PerformanceMonitor
                onIncline={() => setDpr(2)}
                onDecline={() => setDpr(1)}
            />
            <OrthographicCamera
                makeDefault
                position={[0, 0, 1000]}
                up={[0, 0, 1]}
                zoom={5}
            ></OrthographicCamera>

            {/* <Box position={[0, 0, 0]} /> */}
            {/* <Grid
                cellColor="white"
                args={[1000, 1000, 100]}
                cellSize={1000}
            /> */}
            <OrbitControls />
            {/* <Sky distance={3000} /> */}
            <axesHelper />
            <ambientLight />
            {bufferList.length > 0 &&
                bufferList.map((item: any, index: number) => {
                    return (
                        <Lidar key={index} {...item} color={colorList[index]} />
                    );
                })}
        </Canvas>
    );
}
