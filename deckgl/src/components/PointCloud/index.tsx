/*
 * @Author: luofei luofei.trunk.tech
 * @Date: 2023-03-16 18:07:02
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2023-04-28 15:40:40
 * @FilePath: /deckgl/src/components/PointCloud/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { useRef, useState, Suspense, useEffect, useMemo } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import {
    OrbitControls,
    Sky,
    Points,
    Grid,
    Point,
    PerformanceMonitor,
    PivotControls,
    OrthographicCamera,
    Line,
    GizmoHelper,
    GizmoViewport,
    SpotLight,
} from "@react-three/drei";
import * as THREE from "three";
import "./index.less";
function Lidar(props) {
    const mesh = useRef(null);
    const { positions, color, rotation, translation, pointSize } = props;
    // useFrame((state, delta) => {
    //     mesh.current.position.x = translation.x;
    //     mesh.current.position.y = translation.y;
    //     mesh.current.position.z = translation.z;
    //     // mesh.current.quaternion.x = rotation.qx;
    //     // mesh.current.quaternion.y = rotation.qy;
    //     // mesh.current.quaternion.z = rotation.qz;
    //     // mesh.current.quaternion.w = rotation.qw;
    //     mesh.current.rotation.x = rotation.heading;
    //     mesh.current.rotation.y = rotation.pitch;
    //     mesh.current.rotation.z = rotation.roll;
    //     // 修改颜色
    //     mesh.current.material.color.set(color);
    // });
    return (
        <Points
            ref={mesh}
            positions={positions}
            position={[translation.x, translation.y, translation.z]}
            rotation={[rotation.roll, rotation.pitch, rotation.heading]}
        >
            <pointsMaterial
                color={color}
                size={pointSize}
                transparent={true}
                opacity={1}
            />
        </Points>
    );
}

export default function App({ data }: { data: any }) {
    const [bufferList, setBufferList] = useState<any>([]);

    const pointCloud = data?.pointCloud;

    const [pointSize, changePointSize] = useState(1);

    return (
        <Canvas>
            <OrthographicCamera
                makeDefault
                position={[0, 0, 1000]}
                up={[0, 0, 1]}
                zoom={5}
            ></OrthographicCamera>

            {/* <Box position={[0, 0, 0]} /> */}
            {/* <Grid
                cellColor="white"
                args={[1000, 1000, 100]}
                cellSize={1000}
            /> */}
            <OrbitControls
                makeDefault
                enableDamping={false}
                mouseButtons={{
                    LEFT: THREE.MOUSE.PAN,
                    MIDDLE: THREE.MOUSE.DOLLY,
                    RIGHT: THREE.MOUSE.ROTATE,
                }}
                onChange={(event) => {
                    const { zoom = 5 } = event?.target.object || {};
                    const result = 1 + zoom * 0.01;
                    if (result == pointSize) return;
                    changePointSize(result);
                }}
            />
            <ambientLight />
            {/* <Sky distance={3000} /> */}
            <group>
                <Line
                    points={[
                        [0, 0, 0],
                        [30, 0, 0],
                    ]} // Array of points, Array<Vector3 | Vector2 | [number, number, number] | [number, number] | number>
                    color="red" // Default
                    lineWidth={5}
                />
                <Line
                    points={[
                        [0, 0, 0],
                        [0, 30, 0],
                    ]} // Array of points, Array<Vector3 | Vector2 | [number, number, number] | [number, number] | number>
                    color="green" // Default
                    lineWidth={5}
                />
                <Line
                    points={[
                        [0, 0, 0],
                        [0, 0, 30],
                    ]} // Array of points, Array<Vector3 | Vector2 | [number, number, number] | [number, number] | number>
                    color="blue" // Default
                    lineWidth={5}
                />
                <GizmoHelper alignment="top-right" margin={[205, 80]}>
                    <GizmoViewport
                        axisColors={["#F81015", "#218A00", "#0216E4"]}
                        labelColor="white"
                    />
                </GizmoHelper>
                {pointCloud?.length > 0 &&
                    data?.pointCloud.map((item: any, index: number) => {
                        if (item.visible) {
                            return (
                                <Lidar
                                    key={index}
                                    {...item}
                                    pointSize={pointSize}
                                />
                            );
                        }
                        return null;
                    })}
            </group>
        </Canvas>
    );
}
