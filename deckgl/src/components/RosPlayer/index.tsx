/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-12-01 17:40:34
 * @LastEditors: jack 501177081.com
 * @LastEditTime: 2025-07-22 10:42:07
 * @FilePath: /deckgl/src/components/RosPlayer/index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { useEffect, useLayoutEffect, useRef } from "react";
import { RootState } from "@/app/store";
import { useSelector } from "react-redux";
import "./index.less";
import "roslib/build/roslib";
import { Viewer, PointCloud2 } from "@/utils/ros";
import { themeStyle } from "@/theme";
import GUI from "lil-gui";
let gui: any = null;
let guiShow = false;
let viewer: any = null;
let ros: any = null;
function PointClouds({
    pageType,
    display,
}: {
    pageType: string;
    display: boolean;
}) {
    const config = useSelector((state: RootState) => state.dataReducer.config);
    const currentReplayBagInfo = useSelector(
        (state: RootState) => state.dataReducer.currentReplayBagInfo
    );
    const rosDom = useRef(null);
    const pageTypeRef = useRef("");
    pageTypeRef.current = pageType;

    const initRos = () => {
        guiShow = false;
        // 如果ros存在 销毁ros
        if (ros) {
            ros.close();
            ros = null;
        }
        if (gui) {
            gui.destroy();
        }
        gui = new GUI({
            container: document.getElementById("gui") as HTMLElement,
        });
        gui.hide();
        ros = new ROSLIB.Ros({
            url: `ws://${config?.connectConfig?.ip}:${
                config?.connectConfig?.rosPort || 9060
            }`,
        });
        initTipic();
    };
    const rosResize = () => {
        if (viewer) {
            viewer.resize(
                document.getElementById("ros")?.offsetWidth,
                document.getElementById("ros")?.offsetHeight
            );
        }
    };

    /**
     * @description:
     * @param {*} tipic 列表
     * @return {*}
     */
    const initTipic = () => {
        if (currentReplayBagInfo && currentReplayBagInfo.topics.length > 0) {
            // 点云样式
            const max_pts = 50000;
            const pointSize = 0.3,
                pointColor = 0x1677ff;
            // Setup a client to listen to TFs.
            const tfClient = new ROSLIB.TFClient({
                ros: ros,
                angularThres: 0.01,
                transThres: 0.01,
                rate: 10.0,
                fixedFrame: "base_link",
            });
            currentReplayBagInfo.topics.forEach((topic) => {
                const { name, type } = topic;
                let firstPoint = 0;
                // 点云订阅
                if (
                    name.indexOf("/velodyne_points") > -1 ||
                    name.indexOf("/points") > -1
                ) {
                    firstPoint++;
                    new PointCloud2({
                        ros: ros,
                        tfClient: tfClient,
                        rootObject: viewer.scene,
                        topic: name,
                        material: { size: pointSize, color: pointColor },
                        max_pts: max_pts,
                        queue_size: 1,
                        queue_length: 1,
                        callback: firstPoint == 1 ? controlDisplayGui : null,
                    });
                    const initPoint = {
                        name: name,
                        size: pointSize,
                        color: pointColor,
                    };
                    const folder = gui.addFolder(initPoint.name);
                    folder.add(initPoint, "size").step(0.01);
                    folder.addColor(initPoint, "color");
                }
            });
            gui.onChange(changeViewerStyle);
        }
    };
    // 修改对应点云信息
    const changeViewerStyle = ({
        object,
    }: {
        object: { name: string; size: number; color: number };
    }) => {
        if (viewer) {
            const bool = viewer.rerender(object);
            if (!bool) gui.reset();
        }
    };
    // 收到topic信息 gui展示
    const controlDisplayGui = () => {
        if (guiShow) return;
        if (!guiShow && pageTypeRef.current !== "FLOAT") {
            gui.show();
        }
        guiShow = true;
    };

    /**
     * @description: 事件绑定
     * @param {any} e
     * @return {*}
     */

    useEffect(() => {
        // Create the main viewer.
        if (!viewer) {
            viewer = new Viewer({
                divID: "ros",
                width: document.getElementById("ros")?.offsetWidth,
                height: document.getElementById("ros")?.offsetHeight,
                antialias: true,
                alpha: true,
                intensity: 0,
                background: themeStyle[config.theme || "light"].background,
                cameraPose: {
                    x: 0,
                    y: 0,
                    z: 200,
                },
            });
        }

        //清空
        return () => {
            // 删除viewer
            if (viewer) {
                viewer.scene.children = [];
                viewer = null;
                gui = null;
                guiShow = false;
            }
            // 清理播放状态
            ros && ros.close();
        };
    }, []);
    useEffect(() => {
        if (currentReplayBagInfo?.bag_length && viewer) {
            viewer.scene.children = [];
            viewer.addAxiosHelper();
            initRos();
        }
    }, [currentReplayBagInfo]);
    useEffect(() => {
        rosResize();
        if (gui && pageType !== "FLOAT" && guiShow) {
            gui.show();
        } else if (gui) {
            gui.hide();
        }
    }, [pageType, display]);
    useLayoutEffect(() => {
        // 第一次初始化就执行一遍
        let resizeObserver: any = new ResizeObserver(() => rosResize());
        // 定义一个检测变动的状态机
        resizeObserver.observe(document.documentElement);
        // 把这个检测机制绑定到当前元素
        return () => {
            resizeObserver.disconnect(document.documentElement);
            resizeObserver = null;
        };
    }, []);

    return (
        <>
            <div id="ros" ref={rosDom}></div>
            <div id="gui"></div>
        </>
    );
}

export default PointClouds;
