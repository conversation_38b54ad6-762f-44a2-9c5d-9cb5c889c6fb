/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2023-09-08 14:19:11
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 15:44:02
 * @FilePath: \deckgl\src\components\MapConfig\index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { Select } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { EGO_MODEL } from "@/consconts";
import { useTranslation } from "react-i18next";
const { Option } = Select;
function MapConfig({ handleUpdateConfig }: { handleUpdateConfig: any }) {
    const config = useSelector((state: RootState) => state.dataReducer.config);
    const { t } = useTranslation("mapConfig");
    return (
        <div className="class-item">
            <div className="class-title">
                <div className="title-border"></div>
                <div className="title-text">{t("mapConfiguration")}</div>
            </div>
            <div className="class-content">
                <div className="group-item">
                    <div className="group-title">{t("mapName")}:</div>
                    <div className="group-content">
                        <Select
                            defaultValue={config.mapConfig.mapName}
                            popupClassName={`config-dropdown ${config.theme}`}
                            onSelect={(value) =>
                                handleUpdateConfig({
                                    key: "mapConfig",
                                    type: "mapName",
                                    val: value,
                                })
                            }
                            showSearch
                            placeholder={t("searchMapPlaceholder")}
                            optionFilterProp="children"
                            filterOption={(input, option) =>
                                (option?.children as unknown as string)
                                    .toLowerCase()
                                    .indexOf(input.toLowerCase()) >= 0
                            }
                            style={{ width: "100%" }}
                        >
                            {config.mapList && config.mapList.length > 0 ? (
                                config.mapList.map((item: any) => {
                                    return (
                                        <Option
                                            key={item.name}
                                            value={item.name}
                                        >
                                            {item.name}
                                        </Option>
                                    );
                                })
                            ) : (
                                <Option key="loading" value="" disabled>
                                    {t("loading")}...
                                </Option>
                            )}
                        </Select>
                    </div>
                </div>
                <div className="group-item">
                    <div className="group-title">{t("vehicleHot")}:</div>
                    <div className="group-content">
                        <Select
                            defaultValue={config.mapConfig.egoModel}
                            popupClassName={`config-dropdown ${config.theme}`}
                            onSelect={(value) =>
                                handleUpdateConfig({
                                    key: "mapConfig",
                                    type: "egoModel",
                                    val: value,
                                })
                            }
                            options={EGO_MODEL}
                            showSearch
                            placeholder={t("searchVehiclePlaceholder")}
                            optionFilterProp="label"
                            filterOption={(input, option) =>
                                (option?.label as string)
                                    .toLowerCase()
                                    .indexOf(input.toLowerCase()) >= 0
                            }
                            style={{ width: "100%" }}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}
export default MapConfig;
