import { RootState } from "@/app/store";
import {
    setConfig,
    setCurrentReplayBagInfo,
    setFilterTopicList,
    setPlayingStateHook,
    setSelectBagList,
} from "@/features/dataSlice";
import {
    getBags,
    adjustBagInfoByBagDetail,
    getDetail,
    setStartPause,
    filterTopic,
} from "@/request/bag";
import { topicTabList } from "@/utils/enum";
import { setTime } from "@/utils";
import { Empty, Input, Select, Tabs, Tooltip, Tree, message } from "antd";
import { DownOutlined } from "@ant-design/icons";
import { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import "./index.less";
import { IReplayTopic } from "@/types";
import MapConfig from "@/components/MapConfig";
import { useTranslation } from "react-i18next";
import { useI18n } from "../../i18n/provider";
const { DirectoryTree } = Tree;

function ReplayDataPage() {
    const { currentLang } = useI18n();
    const { t } = useTranslation(["bag", "common"]);
    const dispatch = useDispatch();
    const config = useSelector((state: RootState) => state.dataReducer.config);
    const currentReplayBagInfo = useSelector(
        (state: RootState) => state.dataReducer.currentReplayBagInfo
    );
    const [treeData, setTreeData] = useState<any>([]);
    const [selectBagInfo, setSelectBagInfo] = useState<any>({
        key: "/" + currentReplayBagInfo?.bag_name,
        path: "/",
    });
    const [selectBagEnum, setSelectBagEnum] = useState<any>({}); //bag选中项枚举
    const playingStateHook = useSelector(
        (state: RootState) => state.dataReducer.playingStateHook
    );
    const [tabKey, setTabKey] = useState(topicTabList[0].key);
    const [filterCondition, setFilterCondition] = useState<string>("");
    const [showTopicList, setShowTopicList] = useState<
        IReplayTopic[] | string[]
    >([]);
    // 添加点击延迟计时器引用
    const clickTimerRef = useRef<number | null>(null);
    // 用于跟踪是否等待双击
    const [isAwaitingDoubleClick, setIsAwaitingDoubleClick] = useState(false);

    /**
     * @description: 获取bag详情
     * @return {*}
     */
    const getBagDetail = async (info: any = {}) => {
        const { key, path, matched_group } = info;
        const res: any = await getDetail(
            config?.connectConfig?.ip,
            config?.connectConfig?.apiPort,
            matched_group ? { path, matched_group } : key
        );
        if (res?.code == 200) {
            const bagInfo = adjustBagInfoByBagDetail(key, res.results);
            dispatch(setCurrentReplayBagInfo(bagInfo));
            dispatch(setFilterTopicList([]));
            return res;
        } else if (res?.code == 501) {
            message.error(res?.msg);
        } else {
            dispatch(setCurrentReplayBagInfo(""));
        }
    };
    /**
     * @description: 双击播放
     * @return {*}
     */
    const selectAndPlayBag = async (keys: any, info: any) => {
        // 注意：双击事件可能直接传入node对象，需要适配处理
        console.log("info", info);
        const node = info.node || info;
        // 处理根目录文件路径问题
        let { isLeaf, key } = node;
        // 判断key中/长度为1则代表为最外层散装文件
        let strObj: any = key.split("").reduce((res: any, cur: any) => {
            res[cur] ? res[cur]++ : (res[cur] = 1);
            return res;
        }, {});

        if (isLeaf && strObj["/"] == 1) {
            // 修改节点信息，确保在根目录时传递正确的path
            const modifiedNode = {
                ...node,
                path: "/",
            };
            getBagDetail(modifiedNode).then((res) => {
                setStartPause(
                    config?.connectConfig?.ip,
                    config?.connectConfig?.apiPort,
                    "run"
                );
                dispatch(setPlayingStateHook(!playingStateHook));
            });
        } else {
            getBagDetail(node).then((res) => {
                setStartPause(
                    config?.connectConfig?.ip,
                    config?.connectConfig?.apiPort,
                    "run"
                );
                dispatch(setPlayingStateHook(!playingStateHook));
            });
        }

        // 清除等待状态
        setIsAwaitingDoubleClick(false);
        // 清除任何等待中的计时器
        if (clickTimerRef.current !== null) {
            window.clearTimeout(clickTimerRef.current);
            clickTimerRef.current = null;
        }
    };

    const changeTabs = (value: string) => {
        setTabKey(value);
    };
    /**
     * @description: 地图配置更新
     * @return {*}
     */
    const setMapConfig = ({
        key,
        type,
        val,
    }: {
        key?: any;
        type: any;
        val: any;
    }) => {
        const mapConfig = JSON.parse(JSON.stringify(config[key]));
        mapConfig[type] = val;
        dispatch(
            setConfig({
                ...config,
                mapConfig,
            })
        );
    };
    const updateTreeData = (list: any, key: React.Key, children: any): [] =>
        list.map((node: any) => {
            if (node.key === key) {
                return {
                    ...node,
                    children,
                };
            }
            if (node.children) {
                return {
                    ...node,
                    children: updateTreeData(node.children, key, children),
                };
            }
            return node;
        });

    const onLoadData = ({ key, children }: any) => {
        return new Promise<void>((resolve) => {
            if (children) {
                resolve();
                return;
            }
            const ip = config?.connectConfig?.ip;
            const apiPort = config?.connectConfig?.apiPort;
            getBags(ip, apiPort, key)
                .then((res: any) => {
                    const list = res.results.items;
                    if (list?.length) {
                        const isFileList = list.filter((item: any) => {
                            return !item.is_directory;
                        });
                        if (isFileList.length) {
                            let selectList: Array<any> = [];
                            isFileList.forEach((item: any) => {
                                selectList.push({
                                    ...item,
                                    path: key + "/" + item.path,
                                });
                            });
                            setSelectBagEnum((prevState: any) => ({
                                ...prevState,
                                [key]: selectList,
                            }));
                        }
                    }
                    setTreeData((origin: any) =>
                        updateTreeData(
                            origin,
                            key,
                            list.map((item: any) => {
                                return {
                                    title: item.path,
                                    key: key + "/" + item.path,
                                    isLeaf: !item.is_directory,
                                    path: key,
                                    expandAction: false,
                                    matched_group: item.matched_group,
                                };
                            })
                        )
                    );
                    resolve();
                })
                .catch((err) => {
                    resolve();
                });
        });
    };

    /**
     * @description: 点击树形结构
     * @param {Array} keys
     * @param {any} info
     * @return {*}
     */
    const onSelect = (keys: Array<any>, info: any) => {
        // 如果当前正在等待双击，不处理单击事件
        if (isAwaitingDoubleClick) {
            return;
        }

        // 设置等待双击状态
        setIsAwaitingDoubleClick(true);

        // 清除任何已存在的计时器
        if (clickTimerRef.current !== null) {
            window.clearTimeout(clickTimerRef.current);
        }

        // 设置新的计时器，延迟处理单击事件
        clickTimerRef.current = window.setTimeout(() => {
            // 如果到达这里，说明没有发生双击，可以处理单击逻辑
            setIsAwaitingDoubleClick(false);

            let { isLeaf, key, path } = info.node;
            if (isLeaf && key !== selectBagInfo.key) {
                // 判断key中/长度为1则代表为最外层散装文件
                let strObj: any = key.split("").reduce((res: any, cur: any) => {
                    res[cur] ? res[cur]++ : (res[cur] = 1);
                    return res;
                }, {});

                if (isLeaf && strObj["/"] == 1) {
                    path = "/";
                    // 修改节点信息，确保在根目录时传递正确的path
                    const modifiedNode = {
                        ...info.node,
                        path: "/",
                    };
                    getBagDetail(modifiedNode);

                    let selectBagList: Array<any> = treeData.filter(
                        (item: any) => {
                            return item.isLeaf;
                        }
                    );
                    setSelectBagEnum((prevState: any) => ({
                        ...prevState,
                        ["/"]: selectBagList,
                    }));
                    dispatch(setSelectBagList(selectBagList));
                } else {
                    getBagDetail(info.node);
                }

                setSelectBagInfo({
                    ...selectBagInfo,
                    key,
                });

                if (path !== selectBagInfo.path) {
                    if (selectBagEnum[path]) {
                        //拿到选中bag列表文件夹下的所有bag包，方便后面做列表循环播放
                        dispatch(setSelectBagList(selectBagEnum[path]));
                    }
                    setSelectBagInfo({
                        ...selectBagInfo,
                        path,
                    });
                }
            }

            // 清除计时器引用
            clickTimerRef.current = null;
        }, 250); // 250毫秒的延迟，这是常见的双击时间窗口
    };
    /**
     * @description: 获取bag回放列表
     * @return {*}
     */
    const initReplayBag = async () => {
        let listRes: any = null;
        try {
            listRes = await getBags(
                config?.connectConfig.ip,
                config?.connectConfig.apiPort
            );
        } catch (error) {
            console.log("err", error);
            return dispatch(setCurrentReplayBagInfo(""));
        }
        let list = listRes?.results?.items || [];
        if (list.length) {
            list = list.map((item: any) => {
                return {
                    title: item.path,
                    key: "/" + item.path,
                    path: "/" + item.path,
                    isLeaf: !item.is_directory,
                    expandAction: false,
                    matched_group: item.matched_group,
                };
            });
        } else {
            return dispatch(setCurrentReplayBagInfo(""));
        }
        setTreeData([...list]);
    };
    /**
     * @description: 订阅or取消订阅操作
     * @param {any} val
     * @return {*}
     */
    const handleSubscribe = async (val: any) => {
        const res: any = await filterTopic({
            ip: config?.connectConfig?.ip,
            apiPort: config?.connectConfig?.apiPort,
            topic: val.name,
            filter: !val.filter || false,
        });
        if (res?.code == 200 && res?.results?.length) {
            res?.results.map((item: any) => {
                item.type = item.topic;
                item.name = item.topic;
                return item;
            });
            setShowTopicList(res?.results || []);
            dispatch(
                setFilterTopicList(
                    res?.results?.filter(
                        (item: any) => item.filter === false
                    ) || []
                )
            );
        }
    };
    useEffect(() => {
        initReplayBag();

        // 组件卸载时清除计时器
        return () => {
            if (clickTimerRef.current !== null) {
                window.clearTimeout(clickTimerRef.current);
            }
        };
    }, []);

    useEffect(() => {
        const {
            topics = [],
            topics_lost = [],
            topics_error = [],
            bag_name = "",
        } = currentReplayBagInfo || {};
        let list: IReplayTopic[] | string[] = [];
        //列表循环需要主动高亮nextBag
        if (bag_name !== selectBagInfo.key) {
            setSelectBagInfo({
                ...selectBagInfo,
                key: bag_name.indexOf("/") == -1 ? "/" + bag_name : bag_name,
            });
        }
        if (tabKey == "subscript") {
            list = [];
            topics.map((item: any) => {
                if (
                    item.name.indexOf(filterCondition) > -1 ||
                    item.type.indexOf(filterCondition) > -1
                ) {
                    const obj = Object.assign({}, item); // 创建对象的副本
                    obj.filter = true; // 向过滤后的对象添加新字段
                    list.push(obj);
                }
            });
        } else if (tabKey == "lost") {
            list = topics_lost.filter((item) => {
                if (item.indexOf(filterCondition) > -1) return item;
            });
        } else if (tabKey == "error") {
            list = topics_error.filter((item) => {
                if (item.indexOf(filterCondition) > -1) return item;
            });
        }
        setShowTopicList(list);
    }, [currentReplayBagInfo, filterCondition, tabKey]);
    return (
        <div className={`replay-page-main ${currentLang}`}>
            <div className="class-item">
                <div className="class-title">
                    <div className="title-border"></div>
                    <div className="title-text">{t("list")}</div>
                </div>
                <div className="class-content">
                    <div className="group-item">
                        <div className="group-content">
                            <div className="bag-name-list empty-parent">
                                {treeData?.length ? (
                                    <DirectoryTree
                                        treeData={treeData}
                                        onSelect={onSelect}
                                        loadData={onLoadData}
                                        defaultSelectedKeys={[
                                            selectBagInfo.key,
                                        ]}
                                        selectedKeys={[selectBagInfo.key]}
                                        showLine
                                        switcherIcon={<DownOutlined />}
                                        onDoubleClick={selectAndPlayBag}
                                        className="bag-tree"
                                    />
                                ) : (
                                    <Empty
                                        className="config-table-empty"
                                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                                        styles={{
                                            image: {
                                                height: 100,
                                                width: 100,
                                            },
                                        }}
                                        description={
                                            <span>{t("common:emptyText")}</span>
                                        }
                                    />
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="class-item">
                <div className="class-title">
                    <div className="title-border"></div>
                    <div className="title-text">{t("details")}</div>
                </div>
                <div className="class-content">
                    <div className="group-item">
                        <div className="group-title">{t("size")}:</div>
                        <div className="group-content">
                            <span className="bag-text">
                                {currentReplayBagInfo?.bag_size || "0KB"}
                            </span>
                        </div>
                    </div>
                    <div className="group-item">
                        <div className="group-title">{t("startTime")}:</div>
                        <div className="group-content">
                            <Tooltip
                                title={
                                    currentReplayBagInfo?.bag_start_time || 0
                                }
                            >
                                <span className="bag-text">
                                    {currentReplayBagInfo?.bag_start_time
                                        ? setTime(
                                              currentReplayBagInfo?.bag_start_time,
                                              "yyyy-MM-dd HH:mm:ss"
                                          )
                                        : "00:00:00"}
                                </span>
                            </Tooltip>
                        </div>
                    </div>
                    <div className="group-item">
                        <div className="group-title">{t("endTime")}:</div>
                        <div className="group-content">
                            <Tooltip
                                title={currentReplayBagInfo?.bag_end_time || 0}
                            >
                                <span className="bag-text">
                                    {currentReplayBagInfo?.bag_end_time
                                        ? setTime(
                                              currentReplayBagInfo?.bag_end_time,
                                              "yyyy-MM-dd HH:mm:ss"
                                          )
                                        : "00:00:00"}
                                </span>
                            </Tooltip>
                        </div>
                    </div>
                    <div className="group-item">
                        <div className="group-title">{t("duration")}:</div>
                        <div className="group-content">
                            <span className="bag-text">
                                {currentReplayBagInfo?.bag_length?.toFixed(2) ||
                                    "0"}
                                s
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <MapConfig handleUpdateConfig={setMapConfig} />
            <div className="class-item">
                <div className="class-title">
                    <div className="title-border"></div>
                    <div className="title-text">Topics</div>
                </div>
                <div className="class-content">
                    <div className="group-item">
                        <div className="group-content">
                            <Tabs
                                className="group-tabs"
                                activeKey={tabKey}
                                items={topicTabList.map((item) => {
                                    return {
                                        label: item.title,
                                        key: item.key,
                                        children: [
                                            <Input
                                                key={item.key}
                                                value={filterCondition}
                                                placeholder={t("placeholder")}
                                                prefix={
                                                    <i className="iconfont icon-search" />
                                                }
                                                onChange={(e) =>
                                                    setFilterCondition(
                                                        e.target.value
                                                    )
                                                }
                                            />,
                                        ],
                                    };
                                })}
                                onChange={changeTabs}
                            />
                            <div className="tabs-topic-list">
                                {tabKey == topicTabList[0].key
                                    ? (showTopicList as IReplayTopic[]).map(
                                          (item, index) => {
                                              return (
                                                  <div
                                                      className="topic-item-des"
                                                      key={index}
                                                      onClick={() =>
                                                          handleSubscribe(item)
                                                      }
                                                  >
                                                      <Tooltip
                                                          title={item.type}
                                                      >
                                                          <div
                                                              className={`topic-item-title ${
                                                                  item?.filter
                                                                      ? ""
                                                                      : "dis"
                                                              }`}
                                                          >
                                                              {index}:
                                                              {item.name}
                                                          </div>
                                                      </Tooltip>
                                                      {/* <div className="topic-item-type">
                                                          {item.type}
                                                      </div> */}
                                                  </div>
                                              );
                                          }
                                      )
                                    : (showTopicList as string[]).map(
                                          (item, index) => {
                                              return (
                                                  <div
                                                      className={`topic-item-des ${tabKey}`}
                                                      key={index}
                                                  >
                                                      <div className="topic-item-title">
                                                          {index}:
                                                          {typeof item ==
                                                          "string"
                                                              ? item
                                                              : ""}
                                                      </div>
                                                  </div>
                                              );
                                          }
                                      )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
export default ReplayDataPage;
