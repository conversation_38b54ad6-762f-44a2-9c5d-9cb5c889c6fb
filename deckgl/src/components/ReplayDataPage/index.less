@import "@/theme/mixin.less";
.replay-page-main {
    padding: 0 16px;
    overflow-y: hidden;

    .class-item {
        padding: 20px 0 10px;
        border-bottom: 1px solid @player-container-bd-color;
        .class-title {
            width: 100%;
            height: 14px;
            display: flex;
            margin-bottom: 10px;
            .title-border {
                width: 0;
                height: 14px;
                border-right: 3px solid #0056ff;
                margin-right: 8px;
            }
            .title-text {
                height: 14px;
                font-size: 14px;
                font-family: "SourceHanSansCNMedium";
                font-weight: 500;
                color: @config-title-font-color;
                line-height: 1;
            }
            .anticon-download {
                color: #0056ff;
                margin-left: auto;
                padding: 0 5px;
                cursor: pointer;
            }
        }
        .class-content {
            .group-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;

                .group-title {
                    width: 25%;
                    text-align: right;
                    line-height: 24px;
                    font-size: 14px;
                    font-family: "SourceHanSansCNMedium";
                    font-weight: 400;
                    color: @config-title-font-color;
                }
                .group-content {
                    width: 75%;
                    flex: 1;
                    margin: 0 5px;
                    color: @config-title-font-color;

                    .ant-form .ant-form-item {
                        margin-bottom: 0;
                        .ant-form-item-control-input {
                            min-height: 0 !important;
                        }
                    }
                    .ant-input {
                        height: 24px;
                        border-radius: 2px;
                        border: 1px solid @config-input-bd-color;
                        background: @config-input-bg-color;
                        font-size: 12px;
                        color: @config-title-font-color;
                        font-family: SourceHanSansCN-Normal, SourceHanSansCN;
                        font-weight: 400;
                    }
                    .ant-select {
                        width: 100%;
                        height: 24px;

                        .ant-select-selector {
                            width: 100%;
                            height: 24px;
                            // box-sizing: border-box;
                            border-radius: 2px;
                            border: 1px solid @config-input-bd-color;
                            background: @config-input-bg-color;
                            .ant-select-selection-item {
                                height: 22px;
                                line-height: 22px;
                                font-size: 14px;
                                font-family: SourceHanSansCN-Normal,
                                    SourceHanSansCN;
                                font-weight: 400;
                                color: @config-title-font-color;
                            }
                        }
                    }
                    .topic-title {
                        text-align: center;
                        background-color: @topic-title-background-color;
                        border-radius: 10px 10px 0 0;
                    }
                    .topic-title,
                    .topic-item {
                        height: 40px;
                        line-height: 40px;
                        border-bottom: 1px solid @config-input-bd-color;
                        padding: 0 10px;
                        overflow: hidden;
                    }
                    .bag-name-list {
                        width: 100%;
                        height: 200px;
                        overflow-y: auto;
                        border-radius: 4px;
                        background-color: @replay-bag-list-bg-color;

                        .row-bag {
                            width: 100%;
                            overflow: hidden;
                            display: flex;
                            cursor: pointer;
                            flex-wrap: nowrap;
                            i {
                                width: 30px;
                                text-align: center;
                                color: #0056ff;
                            }
                            .cl-name {
                                &:extend(.ellipsis);
                                width: 200px;
                                color: @dropdown-font-color;
                                user-select: none;
                            }
                            margin: 2px 0;
                            &:hover {
                                background-color: @replay-select-bag-bg-color;
                            }
                        }
                        .select-row {
                            background-color: @replay-select-bag-bg-color;
                        }
                    }
                    .bag-text {
                        &:extend(.ellipsis);
                    }
                    .group-tabs {
                        margin-bottom: 5px;
                        .ant-tabs-nav {
                            width: 100%;
                            color: @config-title-font-color;
                            margin-bottom: 6px;
                            .ant-tabs-tab-btn {
                                color: @config-title-font-color;
                            }
                            .ant-tabs-nav-list {
                                .ant-tabs-tab {
                                    font-size: 12px;
                                    padding: 0;
                                }
                            }
                            .ant-tabs-tab-active {
                                .ant-tabs-tab-btn {
                                    color: #0056ff !important;
                                }
                            }
                        }
                        .ant-tabs-content-holder {
                            .ant-input-affix-wrapper {
                                height: 25px;
                                background-color: @player-container-bg-color;
                                border-radius: 2px;
                                border: 1px solid @player-container-bd-color;
                                color: @config-title-font-color;
                                padding: 0 5px;
                                width: 90%;
                                box-sizing: border-box;
                                .ant-input {
                                    border: none;
                                    height: 23px;
                                    line-height: 1;
                                    background: none;
                                    &::placeholder {
                                        color: #999;
                                    }
                                }
                            }
                        }
                    }
                    .tabs-topic-list {
                        // border: 1px solid #ccc;
                        height: 250px;
                        overflow-y: auto;
                        padding: 0 5px;
                        .topic-item-des {
                            border-bottom: 1px solid @player-container-bd-color;
                            margin-bottom: 2px;
                            display: flex;
                            justify-content: space-between;
                            padding: 0 5px;
                            &:hover {
                                cursor: pointer;
                            }
                            .topic-item-title {
                                font-size: 14px;
                                font-weight: 500;
                            }
                            .topic-item-type {
                                font-size: 12px;
                                padding-left: 20px;
                            }
                        }
                    }
                }
                .ant-radio-group {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    .ant-radio-button-wrapper {
                        width: 140px;
                        height: 93px;
                        padding: 0;
                        border: 0;
                        background-color: rgba(0, 0, 0, 0);

                        .ant-radio-button {
                            display: none;
                            border-radius: 4px;
                        }
                        span {
                            width: 140px;
                            height: 93px;
                            display: block;
                            background-color: rgba(0, 0, 0, 0);
                            position: relative;
                        }
                        .radio-png {
                            width: 140px;
                            height: 93px;
                            border-radius: 4px;
                            // border: 1px solid #ccc;
                        }
                        .select-png {
                            position: absolute;
                            right: 10px;
                            top: 10px;
                            height: 20px;
                            width: 20px;
                            display: none;
                        }
                    }
                    .ant-radio-button-wrapper-checked {
                        .select-png {
                            display: block;
                        }
                        .radio-png {
                            border: 1px solid #0056ff;
                        }
                    }
                    .ant-radio-button-wrapper::before {
                        display: none;
                    }
                }
            }
            .group-osm {
                margin-top: 16px;
                padding-top: 12px;
                border-top: 1px solid @config-title-bdb-color; //#cbcfd5;
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: @config-title-font-color;
                .class-title {
                    margin: 0;
                    height: auto;
                    display: flex;
                    align-items: center;
                    .title-border {
                        width: 0;
                        height: 14px;
                        border-right: 2px solid #0056ff;
                        margin-right: 8px;
                    }
                    .group-title {
                        font-size: 14px;
                    }
                }

                .group-content {
                    .ant-switch {
                        font-size: 14px;
                        background-color: #5d656c;
                    }
                    .ant-switch-checked {
                        background-color: #1677ff;
                    }
                }
            }
            .compoennt-type-group {
                width: 288px;
                display: flex;
                margin-top: 33px;
                margin-bottom: 12px;
                border-collapse: collapse;

                .type-item {
                    flex: 1;
                    height: 32px;
                    color: @config-title-font-color;
                    box-shadow: 0 0 0 0.5px @config-tab-bs-color; //#d1cece;
                    box-sizing: border-box;
                    border: none !important;
                    text-align: center;
                    line-height: 32px;
                    font-size: 14px;
                    font-family: "SourceHanSansCNMedium";
                    font-weight: 400;
                    cursor: pointer;
                }
                .type-item:first-child {
                    border-radius: 2px 0 0 2px;
                }
                .type-item:last-child {
                    border-radius: 0 2px 2px 0;
                }
                .select-type {
                    border: none !important;
                    color: #0056ff;
                    // z-index: 100;
                    box-shadow: 0 0 0 1px #0056ff;
                    background-color: @config-tab-select-bg-color;
                }
            }
            .panel-list {
                // width: 288px;
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;

                .panel-item {
                    width: 140px;
                    height: 93px;
                    border-radius: 4px;
                    box-sizing: border-box;
                    // background-color: #eee;
                    margin: 4px 0;
                    cursor: pointer;
                    position: relative;
                    border: 1px solid @config-panel-bd-color;
                    .select-png {
                        width: 20px;
                        height: 20px;
                        position: absolute;
                        right: 10px;
                        top: 10px;
                        display: none;
                    }
                    .desc-png {
                        width: 100%;
                        height: 100%;
                        border-radius: 4px;
                    }
                    .desc-text {
                        width: 60px;
                        position: absolute;
                        left: 10px;
                        top: 10px;
                        background-color: #dee4e5;
                        text-align: center;
                        color: rgb(0, 0, 0);
                        border-radius: 4px;
                    }
                }
                .select-panel {
                    border: 1px solid #0056ff;
                    .select-png {
                        display: block;
                    }
                }
                .hide-div {
                    display: none;
                }
                .ant-empty {
                    width: 100%;
                }
            }
        }
    }
    .class-item:last-child {
        border: 0;
    }
}

.empty-parent {
    width: 100%;
    height: 100%;
    background-color: @dropdown-bg-color !important;

    .config-table-empty {
        width: auto !important;
        .ant-empty-image {
            margin: auto;
        }
    }
}
.error {
    color: #f40000;
}
.lost {
    color: #fd5e0e;
}
.dis {
    text-decoration: line-through;
}
.ant-tree {
    background-color: @dropdown-bg-color;
    color: @config-title-font-color;
}

.en-US {
    .class-item .class-content .group-item .group-title {
        min-width: 100px;
    }
}
