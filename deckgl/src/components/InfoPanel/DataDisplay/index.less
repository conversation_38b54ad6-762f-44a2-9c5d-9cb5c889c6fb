@import "@/theme/mixin.less";
.data-box {
    width: 146px;
    height: 98px;
    background: @panel-bg-color;
    box-shadow: @panel-bs-color;
    border-radius: 4px;
    padding: 16px 0 16px 10px;
    font-family: "SourceHanSansCNMedium";
    margin-bottom: 8px;
    color: @panel-font-color;
    position: relative;
    &:nth-child(odd) {
        margin-right: 8px;
    }
    .title {
        height: 16px;
        font-size: 16px;
        font-weight: 500;
        line-height: 18px;
        margin-bottom: 16px;
    }
    .val {
        height: 16px;
        font-size: 12px;
        font-family: "SourceHanSansCNRegular";
        font-weight: 400;
        line-height: 18px;
        margin-bottom: 2px;
    }
    .unit {
        font-size: 10px;
        color: @panel-font-color;
        transform: scale(0.8);
    }
    .img {
        width: 30px;
        height: 28px;
        display: block;
        position: absolute;
        right: 2px;
        top: 4px;
    }
}
