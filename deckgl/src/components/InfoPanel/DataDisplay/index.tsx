/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-23 14:49:28
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2023-01-12 16:14:41
 * @FilePath: /deckgl/src/components/InfoPanel/DataDisplay/index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import "./index.less";
import { chartObj } from "../Charts/config";
import _ from "lodash";
import dataOne from "@/assets/img/dataOne.png";
import dataTwo from "@/assets/img/dataTwo.png";
import { useTranslation } from "react-i18next";
interface Props {
    id: string | number;
    now: string | number;
    plan: string | number;
    theme: string;
    position?: {
        width: string;
        height: string;
    };
}
function DataDisplay({ id, now, plan, position }: Props) {
    const { t } = useTranslation("operation");
    return (
        <div
            className={`data-box ${id}`}
            // style={{
            //     width: position?.width || "100%",
            //     height: position?.height || "100%",
            // }}
        >
            <div className="title">{chartObj[id]?.title}</div>
            <img src={dataOne} alt="" className="img" />
            <div className="val">
                {t("observationValue")}：{now || ""}
                <span className="unit">{chartObj[id]?.unit}</span>
            </div>
            <div className="val">
                {t("planningValue")}：{plan || ""}
                <span className="unit">{chartObj[id]?.unit}</span>
            </div>
        </div>
    );
}
export default DataDisplay;
