/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-17 16:52:02
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 13:38:31
 * @FilePath: \deckgl\src\components\InfoPanel\Constrains\index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import "./index.less";
import { useEffect, useState } from "react";
import { Table, Empty, ConfigProvider } from "antd";
import { useTranslation } from "react-i18next";
function ConstrainsPanel({ data }: any) {
    const { t } = useTranslation("common");
    const [tables, setTables] = useState([]);
    let obj = {
        speedConstrains: [
            {
                speed: 22.22222222222222,
                distance: 0,
            },
        ],
    };

    function generateTablesData(sourceObj) {
        const tablesData = [];
        let globalIndex = 0; // 用于生成唯一 key 的全局索引

        for (const key in sourceObj) {
            if (sourceObj.hasOwnProperty(key)) {
                const property = sourceObj[key];
                if (Array.isArray(property) && property.length > 0) {
                    const columns = Object.keys(property[0]).map((column) => {
                        // 取两位小数

                        return {
                            title: column,
                            dataIndex: column,
                            key: column, // 列的 key 是属性名
                        };
                    });
                    const dataSource = property.map((item, index) => {
                        // 创建一个新的对象，其属性值保留两位小数
                        const roundedItem = {} as any;
                        for (const key in item) {
                            if (typeof item[key] === "number") {
                                // 确保只有数字类型的值才保留两位小数
                                roundedItem[key] = item[key].toFixed(2);
                            } else {
                                roundedItem[key] = item[key];
                            }
                        }
                        // 为每个数据项生成唯一的 key
                        return {
                            ...roundedItem,
                            key: `${key}_${index}_${globalIndex++}`,
                        };
                    });

                    tablesData.push({
                        title: key, // 动态生成标题
                        dataSource,
                        columns: columns,
                    });
                }
            }
        }

        return tablesData || [];
    }

    useEffect(() => {
        if (data) {
            setTables(generateTablesData(data) || []);
        }
        return () => {
            setTables([]);
        };
    }, [data]);
    return (
        <div className="constrains-box">
            {tables.length > 0 ? (
                <div className="constrains-table">
                    {tables.map((item, index) => (
                        <div
                            key={"table-item-" + index}
                            className="constrains-table-item"
                        >
                            <div
                                className="constrains-table-item-title"
                                key={"title-" + index}
                            >
                                {item.title}
                            </div>
                            <Table
                                key={"table-" + item.key}
                                dataSource={item.dataSource}
                                columns={item.columns}
                                pagination={false}
                                size="small"
                                bordered
                            />
                        </div>
                    ))}
                </div>
            ) : (
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={t("emptyText")}
                    style={{ width: "200px" }}
                />
            )}
        </div>
    );
}
export default ConstrainsPanel;
