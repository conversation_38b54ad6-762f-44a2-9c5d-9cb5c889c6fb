@import "@/theme/mixin.less";

.constrains-table {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    color: var(--panel-font-color);
    padding: 0.833333vw 0 0.833333vw 0.520833vw;
    font-family: "SourceHanSansCNMedium";
}
.constrains-table-item {
    margin-top: 20px;
    margin-right: 10px;
}
.constrains-table-item:first-child {
    margin-top: 0;
}
.constrains-table-item-title {
    margin-bottom: 5px;
}
table {
    .ant-table-cell {
        color: @panel-font-color !important;
        background: @config-title-bg-color !important;
    }
}
