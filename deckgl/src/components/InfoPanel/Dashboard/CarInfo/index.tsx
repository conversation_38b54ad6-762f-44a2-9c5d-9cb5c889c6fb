/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-03 14:34:36
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 13:24:09
 * @FilePath: \deckgl\src\components\InfoPanel\Dashboard\CarInfo\index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { useEffect } from "react";
import "./index.less";
import _ from "lodash";
import { useTranslation } from "react-i18next";

//档位列表
const gearList: Array<object> = [
    {
        key: 0,
        icon: "icon-Pdang",
    },
    {
        key: 1,
        icon: "icon-Rdang",
    },
    {
        key: 2,
        icon: "icon-Ndang",
    },
    {
        key: 3,
        icon: "icon-Ddang",
    },
];

interface Props {
    steeringAngle: number;
    vehicleSpeed: number;
    lightStatus: number;
    longAccel: number;
    autoDriverStatus: number;
    gear: number;
    aebCollision: boolean;
    presetCruiseSpeed: number;
    currentSpeedLimit: number;
    diff: {
        fcwLevel: number;
    };
}

function CarInfo({
    steeringAngle,
    vehicleSpeed,
    lightStatus,
    longAccel,
    autoDriverStatus,
    gear,
    aebCollision,
    presetCruiseSpeed,
    currentSpeedLimit,
    diff,
}: Props) {
    const { t } = useTranslation("operation");
    //二进制的灯判断 倒序
    let lightStatusBinary = lightStatus?.toString(2)?.split("")?.reverse();
    const { fcwLevel } = diff || {};
    useEffect(() => {
        const audio = document.getElementById("audio") as HTMLAudioElement;
        if (audio) {
            switch (fcwLevel) {
                case 0:
                    audio.pause();
                    break;
                case 1:
                    audio.playbackRate = 0.5;
                    audio.play();
                    break;
                case 2:
                    audio.playbackRate = 1;
                    audio.play();
                    break;
                case 3:
                    audio.playbackRate = 1.5;
                    audio.play();
                    break;
                default:
                    audio.pause();
                    break;
            }
        }
    }, [fcwLevel]);
    return (
        <div className="dashboard-one">
            <div className="angle">
                <p className="top">
                    {steeringAngle?.toFixed(2) || 0}
                    <span className="unit du">。</span>
                </p>
                <p className="icon-auto">
                    <i
                        className={`iconfont icon-zidongjiashi ${
                            autoDriverStatus == 0 ? "highlight" : ""
                        }`}
                    ></i>
                    <i
                        className={`iconfont icon-aeb ${
                            aebCollision ? "error" : ""
                        }`}
                    ></i>
                </p>
            </div>
            <div className="speed">
                <p className="top">
                    <span className="text">
                        {vehicleSpeed?.toFixed(2) || 0}
                    </span>
                    <span className="unit">km/h</span>
                </p>
                <p className="icon-turn">
                    <i
                        className={`iconfont icon-zuozhuan ${
                            lightStatusBinary?.[0] == "1" ? "highlight" : ""
                        }`}
                    ></i>
                    {/* <span>{presetCruiseSpeed?.toFixed() || 0}</span> */}
                    <i
                        className={`iconfont icon-youzhuan ${
                            lightStatusBinary?.[1] == "1" ? "highlight" : ""
                        }`}
                    ></i>
                </p>
            </div>
            <div className="acceleration">
                <p className="top">
                    {longAccel?.toFixed(2) || 0}
                    <span className="unit">m/s</span>
                </p>
                <p className="icon-gear">
                    {_.map(gearList, (item: any, index: number) => {
                        return (
                            <i
                                key={index}
                                className={`iconfont ${item.icon} ${
                                    gear == item.key ? "highlight" : ""
                                }`}
                            ></i>
                        );
                    })}
                </p>
            </div>
            {/* 音频 */}
            <audio id="audio" src="/mp3/didi.wav" loop></audio>

            {(currentSpeedLimit || presetCruiseSpeed) != 0 &&
                (currentSpeedLimit || presetCruiseSpeed) && (
                    <div className="limit-box">
                        <div className="round">
                            <b>{currentSpeedLimit?.toFixed() || "--"}</b>
                            <span>km/h</span>
                        </div>
                        <div className="set-speed">
                            <b>{presetCruiseSpeed?.toFixed() || "--"}</b>
                            <span>{t("setSpeed")}</span>
                        </div>
                    </div>
                )}
        </div>
    );
}

export default CarInfo;
