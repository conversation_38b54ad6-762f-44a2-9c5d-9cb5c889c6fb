@import "@/theme/mixin.less";
.dashboard-one {
    // width: 100%;
    height: 100%;
    padding: 10px 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
    transform: scale(0.75);
    position: relative;
    .angle,
    .speed,
    .acceleration {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 160px;
        margin: 0 10px;
        .top {
            text-align: center;
            width: 90px;
            height: 90px;
            font-size: 24px;
            font-family: YouSheBiaoTiHei;
            color: @carInfo-highlight-font-color;
            display: flex;
            justify-content: center;
            align-content: center;
            flex-wrap: wrap;
            background: url("../../../../assets/img/acce.png") no-repeat;
            background-size: 100% 100%;
            position: relative;
            margin: 0 auto !important;
            .text {
                height: 32px;
                line-height: 32px;
                margin-bottom: 10px;
                font-size: 26px;
                color: @carInfo-highlight-font-color;
            }
            .unit {
                width: 100%;
                font-size: 12px;
                font-family: SourceHanSansCN-Regular, SourceHanSansCN;
                font-weight: 400;
                color: #639cff;
                line-height: 20px;
                position: absolute;
                bottom: 7px;
                &.du {
                    top: 10px;
                    right: 5px;
                    text-align: right;
                }
            }
        }
    }
    .angle,
    .acceleration {
        .top {
            margin: 23px auto !important;
        }
    }
    .speed {
        .top {
            width: 128px;
            height: 128px;
            font-size: 40px;
            background: url("../../../../assets/img/speed.png") no-repeat;
            background-size: cover;
            .unit {
                bottom: 34px;
            }
        }
    }

    .icon-auto {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 90px;
        i {
            font-size: 20px;
            color: @carInfo-font-color;
        }
        .icon-zidongjiashi {
            margin-right: 10px;
        }
    }

    .icon-gear {
        display: flex;
        justify-content: center;
        align-items: center;

        i {
            font-size: 14px;
            color: @carInfo-font-color;
            margin-right: 16px;

            &:last-child {
                margin-right: 0px;
            }

            &.highlight {
                font-size: 20px;
                color: #0056ff;
            }
        }
    }

    .icon-turn {
        display: flex;
        justify-content: center;
        align-items: center;
        i {
            font-size: 20px;
            color: #d2d8e5;

            &:first-child {
                margin-right: 88px;
            }

            &.highlight {
                color: #32cd07;
                animation: blink 0.9s infinite;
            }
        }
        span {
            display: block;
            position: absolute;
            font-size: 25px;
            font-weight: 900;
        }
    }

    //限速标识
    .limit-box {
        position: fixed;
        right: -125px;
        padding: 11px 12px;
        border-radius: 4px;
        background: var(--carInfo-bg-color);
        box-shadow: @panel-bs-color;
        backdrop-filter: blur(18px);
        height: 180px;
        .round {
            height: 85px;
            width: 85px;
            border-radius: 85px;
            border: 5px solid red;
            color: @limit-font-color;
            text-align: center;
            position: relative;
            margin: auto;
            b {
                font-weight: bolder;
                display: block;
                font-size: 35px;
                line-height: 60px;
            }
            span {
                font-size: 18px;
                display: flex;
                margin: -15px 0 20px 0;
                align-items: center;
                justify-content: center;
            }
        }
        .set-speed {
            text-align: center;
            b {
                font-weight: bolder;
                display: block;
                font-size: 35px;
                line-height: 50px;
            }
            span {
                font-size: 16px;
            }
        }
    }
}
// 闪烁动画
@keyframes blink {
    0% {
        opacity: 0.2;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.2;
    }
}
i {
    &.highlight {
        color: #1959ff !important;
    }
    &.error {
        color: #ff4f4f !important;
    }
}
