/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-16 11:07:13
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2023-03-01 13:48:16
 * @FilePath: /deckgl/src/components/InfoPanel/Dashboard/VehicleInfo/index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import "./index.less";
import { useEffect, useState } from "react";
import { speech, play, stop } from "@/utils/speech";
interface Props {
    distance: number;
    targetSpeed: number;
    vehicleSpeed: number;
    lightStatus: number;
    gear: number;
    TNPMode: number;
}

function VehicleInfo({
    vehicleSpeed,
    lightStatus,
    distance,
    gear,
    targetSpeed,
    TNPMode,
}: Props) {
    /**
     * @description: 获取不同状态图标
     * @return {*}
     */

    const [info, setInfo] = useState({
        icon: "icon-huise",
        name: "TNP",
    });
    const getIcon = () => {
        switch (TNPMode) {
            case 0:
            case 1:
            case 2:
                play("退出自动驾驶！");
                return {
                    icon: "icon-huise",
                    name: "TNP",
                };
            case 3:
            case 7:
            case 10:
                return {
                    icon: "icon-lvse",
                    name: "TNP",
                };
            case 4:
                return {
                    icon: "icon-lvse",
                    name: "HWA",
                };
            case 5:
                play("进入自动驾驶！");
                return {
                    icon: "icon-lvse",
                    name: "TJA",
                };
            case 6:
                return {
                    icon: "icon-lvse",
                    name: "ACC",
                };
            case 8:
            case 9:
                return {
                    icon: "icon-hongse",
                    name: "TNP",
                };
            default:
                play("退出自动驾驶！");
                return {
                    icon: "icon-huise",
                    name: "TNP",
                };
        }
    };

    useEffect(() => {
        setInfo(getIcon());
    }, [TNPMode]);

    return (
        <div className="dashboard-two">
            <div className="top">
                <div className="title">
                    <h1>{vehicleSpeed?.toFixed(1) || "--"}</h1>
                    <span className="unit">km/h</span>
                </div>
                <div className="title target">
                    <p className="meter">{targetSpeed?.toFixed(0) || "--"}</p>
                </div>
                <div className="title">
                    <h1>{distance?.toFixed(1) || "--"}</h1>
                    <span className="unit">m</span>
                </div>
            </div>
            <div className="bot">
                <div className="left">
                    <i
                        className={`iconfont icon-zuodeng ${
                            lightStatus == 1 ? "highlight" : ""
                        }`}
                    ></i>
                    <i className="iconfont icon-d">
                        {gear == 0
                            ? "N"
                            : gear == 251
                            ? "P"
                            : gear > 0
                            ? "D"
                            : gear < 0
                            ? "R"
                            : ""}
                    </i>
                    <i
                        className={`iconfont icon-youdeng ${
                            lightStatus == 2 ? "highlight" : ""
                        }`}
                    ></i>
                </div>
                <div className="right">
                    <i className={`iconfont ${info.icon}`}></i>
                    <span>{info.name}</span>
                </div>
            </div>
        </div>
    );
}

export default VehicleInfo;
