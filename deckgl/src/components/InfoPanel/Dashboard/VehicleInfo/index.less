@import "@/theme/mixin.less";
.dashboard-two {
    width: 100%;
    height: 100%;
    .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // padding: 21px 16px 26px;
        padding: 31px 28px 44px;

        width: 100%;
        height: 182px;
        background: @vehicleinfo-bg-color;
        border-radius: 4px 4px 0px 0px;
        position: relative;
        .title {
            text-align: center;
            position: relative;
            h1 {
                letter-spacing: 3px;
                margin: 0;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                text-align: center;
                // height: 47px;
                // font-size: 40px;
                font-size: 80px;
                font-family: DINAlternate-Bold, DINAlternate;
                font-weight: bold;
                color: @vehicleInfo-font-color;
                // line-height: 47px;
                line-height: 93px;
                letter-spacing: 1px;
            }
            .unit {
                display: block;
                height: 28px;
                font-size: 28px;
                // height: 14px;
                // font-size: 14px;
                font-family: SourceHanSansCN-Regular, SourceHanSansCN;
                font-weight: 400;
                color: @vehicleInfo-font-color;
                line-height: 21px;
                letter-spacing: 1px;
                display: flex;
                align-items: flex-end;
                justify-content: center;
            }
            .meter {
                width: 79px;
                height: 67px;
                background: url(../../../../assets/img/set.png) no-repeat;
                background-size: cover;
                font-size: 32px;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                line-height: 45px;
                letter-spacing: 1px;
                display: flex;
                justify-content: center;
                align-items: center;
                color: @vehicleInfo-font-color;
                transform: scale(0.57);
            }
        }
        .target {
            position: absolute;
            left: 50%;
            // top: 12.5px;
            top: 47px;
            transform: translate(-50%);
        }
    }
    .bot {
        // height: 50px;
        height: 88px;
        display: flex;
        // padding: 20px 62px;
        padding: 11px 35px;
        background: @vehicleInfo-bot-bg-color;
        .left {
            display: flex;
            align-items: center;
            margin-right: 67px;
            // margin-right: 117px;
            i {
                // font-size: 16px;
                font-size: 28px;
                color: @vehicleInfo-icon-color;
            }
            .icon-d {
                display: block;
                // width: 27px;
                // height: 27px;
                // text-align: center;
                // line-height: 27px;
                // font-size: 16px;
                width: 47px;
                height: 47px;
                text-align: center;
                line-height: 47px;
                font-size: 32px;
                font-family: DINAlternate-Bold, DINAlternate;
                font-weight: bold;
                color: #0056ff;
                -webkit-text-stroke: 1px #0056ff;
                background: @vehicleInfo-D-bg-color;
                // margin: 0 23px;
                margin: 0 40px;
                border-radius: 50px;
            }
            .highlight {
                color: #0056ff;
            }
        }
        .right {
            position: relative;
            // top: 11px;
            display: flex;
            justify-content: center;
            align-items: center;
            i {
                // font-size: 19px;
                font-size: 34px;
            }
            .icon-lvse {
                color: #32cd07;
            }
            .icon-huise {
                color: #afafaf;
            }
            .icon-hongse {
                color: #ee4332;
            }
            span {
                // height: 11px;
                // font-size: 9px;
                height: 22px;
                font-size: 18px;
                // font-size: 34px;
                font-family: DIN-Regular, DIN;
                font-weight: 400;
                color: @vehicleInfo-font-color;
                line-height: 11px;
                line-height: 22px;
                position: absolute;
                left: 51px;
                top: 7px;
            }
        }
    }
}
