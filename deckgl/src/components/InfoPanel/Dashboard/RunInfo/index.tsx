/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-16 11:07:13
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON> l<PERSON>@trunk.tech
 * @LastEditTime: 2023-03-30 13:51:15
 * @FilePath: /mapsets/deckgl/src/components/InfoPanel/Dashboard/RunInfo/index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import "./index.less";
import { useEffect, useState } from "react";
import _ from "lodash";

//档位列表
const gearList: Array<object> = [
    {
        key: 0,
        icon: "icon-Pdang",
    },
    {
        key: 1,
        icon: "icon-Rdang",
    },
    {
        key: 2,
        icon: "icon-Ndang",
    },
    {
        key: 3,
        icon: "icon-Ddang",
    },
];
interface Props {
    steeringAngle: number;
    vehicleSpeed: number;
    autoDriverStatus: number;
    gear: number;
    brake: number;
    throttle: number;
}
function RunInfo({
    steeringAngle,
    vehicleSpeed,
    autoDriverStatus,
    gear,
    brake,
    throttle,
}: Props) {
    return (
        <div
            className={`dashboard-three ${autoDriverStatus == 0 ? "auto" : ""}`}
        >
            <div className="speed">
                <h1>{vehicleSpeed?.toFixed(2) || 0} </h1>
                <span className="unit"> km/h</span>
            </div>
            <div className="con">
                <div className="left">
                    <div className="box">
                        <span> {brake?.toFixed(2) || 0}</span>
                        <div
                            className="val"
                            style={{ height: brake / 90 + "px" }}
                        ></div>
                    </div>
                    <p className="meter">Brake</p>
                </div>
                <div className="center">
                    <p className="du">
                        {steeringAngle?.toFixed(2) || 0}
                        <b>°</b>
                    </p>
                    <div className="anim">
                        <i
                            className={`iconfont icon-left ${
                                steeringAngle > 0 ? "hight" : ""
                            }`}
                        ></i>
                        <i
                            className="iconfont icon-yibiaopan"
                            style={{
                                transform: "rotate(" + -steeringAngle + "deg)",
                            }}
                        ></i>
                        <i
                            className={`iconfont icon-right ${
                                steeringAngle < 0 ? "hight" : ""
                            }`}
                        ></i>
                    </div>
                    <p className="meter">Steering</p>
                </div>
                <div className="right">
                    <div className="box">
                        <span> {throttle?.toFixed(2) || 0}</span>
                        <div
                            className="val"
                            style={{ height: throttle / 0.9 + "px" }}
                        ></div>
                    </div>
                    <p className="meter">Throttle</p>
                </div>
            </div>
            <div className="bot">
                {_.map(gearList, (item: any, index: number) => {
                    return (
                        <i
                            key={index}
                            className={`iconfont ${item.icon} ${
                                gear == item.key ? "highlight" : ""
                            }`}
                        ></i>
                    );
                })}
            </div>
        </div>
    );
}

export default RunInfo;
