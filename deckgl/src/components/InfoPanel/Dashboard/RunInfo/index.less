@import "@/theme/mixin.less";
.dashboard-three {
    width: 100%;
    height: 100%;
    padding: 20px;
    border-radius: 4px;
    .speed {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 10px;
        h1 {
            margin: 0;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 40px;
            font-family: DINAlternate-Bold, DINAlternate;
            font-weight: bold;
            color: @vehicleInfo-font-color;
            letter-spacing: 1px;
        }
        .unit {
            display: block;
            font-size: 25px;
            font-family: SourceHanSansCN-Regular, SourceHanSansCN;
            font-weight: bold;
            color: @vehicleInfo-font-color;
            line-height: 21px;
            letter-spacing: 1px;
            padding-left: 10px;
        }
    }
    .con {
        display: flex;
        justify-self: center;
        align-items: flex-end;
        height: 120px;
        .meter {
            font-size: 16px;
            font-family: SourceHanSansCN-Regular, SourceHanSansCN;
            font-weight: bold;
            color: @vehicleInfo-font-color;
            line-height: 30px;
        }
        .left,
        .right {
            width: 70px;
            height: 100%;
            text-align: center;
            .box {
                width: 30px;
                height: 90px;
                background: #e9eef9;
                border-radius: 4px;
                margin: auto;
                display: flex;
                align-items: flex-end;
                position: relative;
                span {
                    color: #0056ff;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
                .val {
                    width: 100%;
                    background: #0056ff;
                    border-radius: 4px;
                    transition: all 2s linear;
                }
            }
        }
        .center {
            flex: 1;
            text-align: center;
            .du {
                font-size: 20px;
                font-family: SourceHanSansCN-Regular, SourceHanSansCN;
                font-weight: bold;
                color: @vehicleInfo-font-color;
                line-height: 30px;
            }
            .anim {
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 10px 0;
                height: 50px;
                .icon-left,
                .icon-right {
                    font-size: 30px;
                    color: @vehicleInfo-font-color;
                    &.hight {
                        color: #0056ff;
                    }
                }
                .icon-yibiaopan {
                    font-size: 60px;
                    color: @vehicleInfo-font-color;
                    transition: all 2s linear infinite;
                }
                .icon-right {
                    transform: rotateX(180deg);
                }
            }
        }
    }
    .bot {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 10px;

        i {
            font-size: 20px;
            color: @vehicleInfo-font-color;
            margin-right: 18px;
            &:last-child {
                margin-right: 0px;
            }

            &.highlight {
                font-size: 20px;
                color: #0056ff;
            }
        }
    }
}
.auto {
    background: linear-gradient(180deg, #32cd07 0%, #64d882 100%);
}
