/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-17 16:52:02
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 11:56:45
 * @FilePath: \deckgl\src\components\InfoPanel\Topic\Decision\index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import "./index.less";
import { useState } from "react";
import { Empty } from "antd";
import { decisionLatStateEnum, decisionLonStateEnum } from "@/utils/enum";
import Item from "antd/es/list/Item";
import { useTranslation } from "react-i18next";
interface Props {
    data?: any;
    idc?: string;
}

let localizationEum: {
    icons: string[];
    eum: {
        [key: string]: number[];
    };
    state: {
        [key: string]: object;
    };
} = {
    icons: ["GPSxinghao", "shexia<PERSON><PERSON>", "leidatance"],
    eum: {
        "-1": [0, 0, 0],
        "0": [1, 1, 1],
        "1": [1, 0, 1],
        "2": [1, 1, 0],
        "3": [0, 1, 1],
        "4": [1, 0, 0],
        "5": [0, 0, 1],
        "6": [0, 1, 0],
        "7": [0, 0, 0],
        "99": [0, 0, 0],
    },
    state: {
        "4": { color: "rgb(0,255,0)" },
        "3": { color: "rgb(0,0,255)" },
        "2": { color: "rgb(200,220,100)" },
        "1": { color: "rgb(255,165,0)" },
        "0": { color: "rgb(255,0,0)" },
    },
};

function TopicDecision({ data, idc }: Props) {
    const { t } = useTranslation(["common", "operation"]);
    const zoomIn = (z) => {
        const doc = document.getElementById(idc);
        if (doc) {
            doc.style.scale = z;
        }
    };

    return (
        <div className="decision-box">
            <div className="tools">
                <p
                    title={t("operation:zoomIn")}
                    onClick={() => {
                        zoomIn(1.5);
                    }}
                ></p>
                <p
                    title={t("operation:restore")}
                    onClick={() => {
                        zoomIn(1);
                    }}
                ></p>
            </div>

            {Object.keys(data || {}).length ? (
                <>
                    {Object.keys(data)
                        .filter((item) => item != "localizationStatus")
                        .map((item, index) => {
                            return (
                                <div className="row" key={index}>
                                    <span className="title">{item}</span>:
                                    <span className="val">
                                        {Array.isArray(data[item]) ? (
                                            <span>
                                                {data[item][0]?.toFixed(2)} /{" "}
                                                {data[item][1]?.toFixed(2)}
                                            </span>
                                        ) : item == "decisionLatState" ? (
                                            decisionLatStateEnum[data[item]]
                                        ) : item == "decisionLonState" ? (
                                            decisionLonStateEnum[data[item]]
                                        ) : item == "decisionScenarioName" ||
                                          item == "decisionStageName" ? (
                                            data[item]
                                        ) : item == "state" ||
                                          item == "craneStop" ||
                                          item == "dragStop" ||
                                          item == "craneDis" ? (
                                            JSON.stringify(data[item])
                                        ) : (
                                            Number(data[item])?.toFixed(2)
                                        )}
                                    </span>
                                </div>
                            );
                        })}

                    {idc === "controls" && (
                        <div className="row">
                            <span className="title">localizationStatus</span>:
                            <span className="val">
                                {data.localizationStatus}
                                {localizationEum.icons.map((item, index) => {
                                    return (
                                        <span
                                            className={
                                                "iconfont icon-" +
                                                localizationEum.icons[index]
                                            }
                                            key={index}
                                            style={
                                                localizationEum.eum[
                                                    data.localizationStatus
                                                ][index]
                                                    ? { color: "#07ff07" }
                                                    : { color: "#ccc" }
                                            }
                                        ></span>
                                    );
                                })}
                            </span>
                        </div>
                    )}
                </>
            ) : (
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={t("emptyText")}
                />
            )}
        </div>
    );
}
export default TopicDecision;
