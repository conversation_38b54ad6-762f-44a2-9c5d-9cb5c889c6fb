@import "@/theme/mixin.less";
.decision-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    color: @panel-font-color;
    padding: 16px 0 16px 10px;
    font-family: "SourceHanSansCNMedium";
    .row {
        width: 100%;
        display: flex;
        align-items: center;
        .title {
            width: 160px;
            display: block;
            float: left;
            position: relative;
            font-size: 14px;
            font-weight: 500;
            line-height: 14px;
            text-align: right;
            padding-right: 10px;
            color: @panel-font-color;
        }
        .val {
            flex: 1;
            margin: 0 10px 0 10px;
            font-size: 12px;
            font-family: "SourceHanSansCNMedium";
            font-weight: 400;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: @panel-font-color;
        }
        .iconfont {
            font-size: 12px;
            padding-left: 10px;
        }
    }
    .tools {
        position: absolute;
        right: 2px;
        top: -5px;
        p {
            line-height: 15px;
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 8px;
            margin: 0 2px !important;
            cursor: pointer;
        }
        p:nth-child(1) {
            background-color: #ffbd4f;
        }
        p:nth-child(2) {
            background-color: #5fff87;
        }
    }
}
