/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-17 16:52:02
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 11:57:26
 * @FilePath: \deckgl\src\components\InfoPanel\Topic\Status\index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import "./index.less";
import _ from "lodash";
import { Empty } from "antd";
import { useTranslation } from "react-i18next";
interface Props {
    data?: any;
}
function TopicStatus({ data }: Props) {
    const { t } = useTranslation("common");
    return (
        <div className="topic-one">
            {Object.keys(data || {}).length ? (
                Object.keys(data).map((item, index) => {
                    return (
                        <div className="li" key={index}>
                            <span className="title">{item}</span>：
                            <span
                                className={` status  ${
                                    data[item] ? "success" : "error"
                                }`}
                            ></span>
                        </div>
                    );
                })
            ) : (
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={t("emptyText")}
                />
            )}
        </div>
    );
}
export default TopicStatus;
