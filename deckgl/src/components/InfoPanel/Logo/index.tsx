/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> l<PERSON>@trunk.tech
 * @Date: 2023-01-03 10:28:37
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2024-07-08 16:08:35
 * @FilePath: \deckgl\src\components\InfoPanel\Logo\index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import "./index.less";
import { Theme } from "@/types";
import logoLight from "@/assets/img/logo_light.png";
import logoDark from "@/assets/img/logo_dark.png";
function Logo({ theme }: { theme: Theme }) {
    return (
        <div className="logo-box ">
            <img src={theme == "dark" ? logoLight : logoDark} />
        </div>
    );
}

export default Logo;
