/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-14 10:50:12
 * @LastEditors: luofei <EMAIL>
 * @LastEditTime: 2024-09-24 16:47:50
 * @FilePath: \deckgl\src\components\InfoPanel\Fault\config.ts
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

// const faultList: any = [
//     {
//         title: "传感器",
//         isHas: false,
//         codeList: [],
//         otherLength: 0,
//         number: [1000, 1999],
//         isOpen: false,
//     },
//     {
//         title: "PNC",
//         isHas: false,
//         codeList: [],
//         otherLength: 0,
//         number: [2000, 2999],
//         isOpen: false,
//     },
//     {
//         title: "定位",
//         isHas: false,
//         codeList: [],
//         otherLength: 0,
//         number: [3000, 3999],
//         isOpen: false,
//     },
//     {
//         title: "感知",
//         isOpen: false,
//         isHas: false,
//         codeList: [],
//         otherLength: 0,
//         number: [4000, 4999],
//     },
//     {
//         title: "地图",
//         isOpen: false,
//         isHas: false,
//         codeList: [],
//         otherLength: 0,
//         number: [5000, 5999],
//     },
//     {
//         title: "guardian",
//         isOpen: false,
//         isHas: false,
//         codeList: [],
//         otherLength: 0,
//         number: [6000, 6999],
//     },
//     {
//         title: "系统",
//         isOpen: false,
//         isHas: false,
//         codeList: [],
//         otherLength: 0,
//         number: [7000, 7999],
//     },
//     {
//         title: "状态",
//         isOpen: false,
//         isHas: false,
//         codeList: [],
//         otherLength: 0,
//         number: [8000, 8999],
//     },
// ];
// src/constants/faultList.ts
const faultList: Array<{
    title: string; // 改为翻译键
    isHas: boolean;
    codeList: any[];
    otherLength: number;
    number: [number, number];
    isOpen: boolean;
}> = [
    {
        title: "sensor", // 翻译键：传感器
        isHas: false,
        codeList: [],
        otherLength: 0,
        number: [1000, 1999],
        isOpen: false,
    },
    {
        title: "pnc", // 翻译键：PNC（保留大写，符合技术术语习惯）
        isHas: false,
        codeList: [],
        otherLength: 0,
        number: [2000, 2999],
        isOpen: false,
    },
    {
        title: "location", // 翻译键：定位
        isHas: false,
        codeList: [],
        otherLength: 0,
        number: [3000, 3999],
        isOpen: false,
    },
    {
        title: "perception", // 翻译键：感知
        isOpen: false,
        isHas: false,
        codeList: [],
        otherLength: 0,
        number: [4000, 4999],
    },
    {
        title: "map", // 翻译键：地图
        isOpen: false,
        isHas: false,
        codeList: [],
        otherLength: 0,
        number: [5000, 5999],
    },
    {
        title: "guardian", // 翻译键：guardian（保留原词，技术术语）
        isOpen: false,
        isHas: false,
        codeList: [],
        otherLength: 0,
        number: [6000, 6999],
    },
    {
        title: "system", // 翻译键：系统
        isOpen: false,
        isHas: false,
        codeList: [],
        otherLength: 0,
        number: [7000, 7999],
    },
    {
        title: "status", // 翻译键：状态
        isOpen: false,
        isHas: false,
        codeList: [],
        otherLength: 0,
        number: [8000, 8999],
    },
];
const getNumberSection: any = (str: any, m: number, n: number) => {
    var re = /(\d+)/g;
    while (re.exec(str)) {
        var int = parseInt(RegExp.$1);
        if (int < m || int > n) return false;
    }
    return true;
};
export { faultList, getNumberSection };
