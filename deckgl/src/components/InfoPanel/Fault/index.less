@import "@/theme/mixin.less";
.fault-box {
    width: 100%;
    height: 100%;
    .title {
        height: 56px;
        background: @fault-title-bg-color;
        font-size: 16px;
        font-weight: bold;
        font-size: 16px;
        font-family: "SourceHanSansCNMedium";
        font-weight: 500;
        color: @panel-font-color;
        line-height: 56px;
        padding-left: 16px;
        border-bottom: 1px solid @fault-title-bd-color;
    }
    .content {
        margin: 0;
        padding: 16px;
        color: @panel-font-color;
        .list {
            list-style: none;
            width: 100%;
            margin: 0 auto 4px;
            transition: height 0.5s ease-in-out;
            background: @fault-default-bg-color;
            border-radius: 4px;
            border: 1px solid @fault-default-bd-color;
            font-size: 14px;
            font-family: "SourceHanSansCNMedium";
            font-weight: 400;
            line-height: 40px;
            position: relative;
            padding: 0 4px 0 10px ;
            &:hover{
                cursor: pointer;
            }
            .top{
                height: inherit;
                display: flex;
                align-items: center;
                justify-content: center;
                .name {
                    width: auto;
                    font-size: 16px;
                    font-weight: bold;
                }
            }
            .box,
            .bot {
                flex: 1;
                display: flex;
                justify-content: flex-end;
                flex-wrap: wrap;
                .btn {
                    display: block;
                    height: 20px;
                    background: @fault-highlight-btn-bg-color;
                    border-radius: 2px;
                    font-size: 14px;
                    font-family: "SourceHanSansCNMedium";
                    font-weight: 400;
                    line-height: 20px;
                    margin: 4px;
                    padding: 0 4px;
                    color: @fault-highlight-btn-font-color;
                }
                .warning{
                    background: @fault-warning-btn-bg-color;
                    color: @fault-warning-btn-font-color;
                }
            }
            .bot{
                justify-content: flex-start;
                padding-bottom: 10px;
            }
        }

        .old{
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .error-box,
        .warning-box {
            position: relative;
            overflow: hidden;
            width: 100%;
            height: auto;
            z-index: 0;
            border-radius: 4px;
        }
        .error-box{
            background: @fault-highlight-bg-color;
            border: 1px solid @fault-highlight-bd-color;
        }
        .warning-box{
            background: @fault-warning-bg-color;
            border: 1px solid @fault-warning-bd-color;
        }
        .auto{
            height: auto !important;
        }
        .no-auto{
            height: 40px;
        }
    }
}
// .icon-up,
// .icon-down{
//     font-size: 20px;
// }