/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-23 14:49:28
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 11:47:26
 * @FilePath: \deckgl\src\components\InfoPanel\Fault\index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import "./index.less";
import { faultList, getNumberSection } from "./config";
import _ from "lodash";
import { useState, useMemo } from "react";
import { UpSquareOutlined, DownSquareOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
interface Props {
    errorCode?: Array<any>;
    dragg?: boolean;
}
function Fault({ errorCode, dragg }: Props) {
    const { t } = useTranslation("fault");
    const [isHasLevel, setIsHasLevel] = useState(false);
    const faultData = useMemo(() => {
        setIsHasLevel(errorCode?.[0]?.toString()?.length > 4 ? true : false);
        // 区分sany tad 不加8开头故障码
        faultList.map((item: any) => {
            item.isHas = false;
            item.codeList = [];
            item.otherLength = 0;
            errorCode?.map((v) => {
                let numStr = v + "";
                let firstChar = numStr[0]; // 获取第一位字符
                let remainingDigits = numStr.slice(1); // 获取除第一位以外的字符
                let flag = getNumberSection(
                    Number(numStr.length > 4 ? remainingDigits : v),
                    item.number[0],
                    item.number[1]
                );
                if (flag) {
                    item.isHas = true;
                    if (numStr.length > 4 && firstChar !== "3") {
                        item.otherLength += 1;
                    }
                    item.codeList.push({
                        errorLevel: numStr.length > 4 ? firstChar : null,
                        errorCode: numStr.length > 4 ? remainingDigits : v,
                    });
                }
            });
        });
        let is8 = false;
        errorCode?.some((number) => {
            // 检查第二位是否为8
            if (number.toString()[1] === "8") {
                is8 = true; // 如果是，设置 is8 为 true
                // 可以在这里中断循环，如果只需要找到第一个第二位为8的数字
                return true; // 取消注释以中断循环
            }
            return false;
        });
        let faultListFilter = is8
            ? faultList
            : faultList.filter((item: any) => item.title !== "status"); //状态;
        return faultListFilter;
    }, [errorCode]);

    const handleOpen = (e: any, val: any) => {
        if (dragg) return;
        if (!val.otherLength) return;
        val.isOpen = !val.isOpen;
        e.stopPropagation(); // 阻止事件冒泡
    };
    return (
        <div className="fault-box">
            {/* <h1 className="title">故障诊断</h1> */}
            {!isHasLevel ? (
                <ul className="content">
                    {faultData.map((item: any, index: number) => {
                        return (
                            <li
                                className={`list old ${
                                    item.isHas ? "error-box" : ""
                                }`}
                                key={index}
                            >
                                <p className="name">{t(item.title)}</p>
                                {item?.codeList?.length ? (
                                    <p className="box">
                                        {item.codeList.map(
                                            (k: any, index: number) => {
                                                return (
                                                    <span
                                                        className="btn"
                                                        key={index}
                                                    >
                                                        {k.errorCode}
                                                    </span>
                                                );
                                            }
                                        )}
                                    </p>
                                ) : (
                                    ""
                                )}
                            </li>
                        );
                    })}
                </ul>
            ) : (
                <ul className="content">
                    {faultData.map((item: any, index: number) => {
                        return (
                            <li
                                key={index}
                                className={`list new ${
                                    item?.codeList.filter((v: any) => {
                                        return v.errorLevel == 3;
                                    })?.length
                                        ? "error-box"
                                        : item.otherLength
                                        ? "warning-box"
                                        : ""
                                }  ${item.isOpen ? "auto" : "no-auto"}`}
                                onClick={(e) => handleOpen(e, item)}
                                onTouchStart={(e) => handleOpen(e, item)}
                            >
                                <div className="top">
                                    <p className="name">
                                        {t(item.title)}
                                        {item.otherLength
                                            ? "(" + item.otherLength + ")"
                                            : ""}
                                    </p>
                                    <div className="box">
                                        {item?.codeList?.map(
                                            (v: any, index: number) => {
                                                return v.errorLevel === "3" ? (
                                                    <span
                                                        className="btn"
                                                        key={index}
                                                    >
                                                        {v.errorCode}
                                                    </span>
                                                ) : (
                                                    ""
                                                );
                                            }
                                        )}
                                    </div>
                                    {/* 备选方案 按钮点击 */}
                                    {/* {item.otherLength ? (
                                        item.isOpen ? (
                                            <UpSquareOutlined
                                                className="icon-up"
                                                onClick={(e) =>
                                                    handleOpen(e, item)
                                                }
                                                onTouchStart={(e) =>
                                                    handleOpen(e, item)
                                                }
                                            />
                                        ) : (
                                            <DownSquareOutlined
                                                className="icon-down"
                                                onClick={(e) =>
                                                    handleOpen(e, item)
                                                }
                                                onTouchStart={(e) =>
                                                    handleOpen(e, item)
                                                }
                                            />
                                        )
                                    ) : (
                                        <></>
                                    )} */}
                                </div>
                                <div className="bot">
                                    {item?.codeList.map(
                                        (v: any, index: number) => {
                                            return v.errorLevel !== "3" ? (
                                                <span
                                                    className="btn warning"
                                                    key={index}
                                                >
                                                    {v.errorCode}
                                                </span>
                                            ) : (
                                                ""
                                            );
                                        }
                                    )}
                                </div>
                            </li>
                        );
                    })}
                </ul>
            )}
        </div>
    );
}
export default Fault;
