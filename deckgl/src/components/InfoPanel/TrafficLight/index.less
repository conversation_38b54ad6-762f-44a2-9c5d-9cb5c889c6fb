@import "@/theme/mixin.less";
.traffic-light {
    padding: 10px;
    display: flex;
    position: relative;
    background: var(--carInfo-bg-color);

    .traffic-item {
        width: 60px;
        // height: 60px;
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: 10px;
        // border: 1px solid #000;
        // padding: 0px;

        .circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 10px;
        }
        .circle-light {
            background-color: #c7c7c7;
        }
        .circle-dark {
            background-color: #857b7b;
        }
        .red {
            background-color: #ff0000;
            background-image: radial-gradient(circle, #ff0000, #a3170a);
        }
        .red-light {
            box-shadow: 0 0 10px 10px #f29292;
        }
        .red-dark {
            box-shadow: 0 0 10px 10px #901d1d;
        }
        .yellow {
            background-color: #ffff00;
            background-image: radial-gradient(circle, #ffff00, #818106);
        }
        .yellow-light {
            box-shadow: 0 0 10px 10px #f3f36c;
        }
        .yellow-dark {
            box-shadow: 0 0 10px 10px #6b6b1b;
        }
        .green {
            background-color: #00fb00;
            background-image: radial-gradient(circle, #00fb00, #018401);
        }
        .green-light {
            box-shadow: 0 0 10px 10px #84f584;
        }
        .green-dark {
            box-shadow: 0 0 10px 10px #0e570e;
        }
        .opa {
            opacity: 0.4;
            box-shadow: none;
        }
        .animation {
            animation: bounce 1s linear infinite;
        }
    }
    .traffic-item:nth-child(n) {
        margin-right: 10px;
    }
    .traffic-item:last-child {
        margin-right: 0;
    }
    .empty {
        width: 60px;
        height: 60px;
    }
}

@keyframes bounce {
    0% {
        opacity: 0.4;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.4;
    }
}
