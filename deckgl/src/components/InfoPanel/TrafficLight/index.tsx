/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> l<PERSON>@trunk.tech
 * @Date: 2023-01-03 10:28:37
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2023-01-12 15:39:34
 * @FilePath: /deckgl/src/components/InfoPanel/Table/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import "./index.less";
import { Theme } from "@/types";
interface ITrafficLight {
    color: number; // 信号灯颜色 0: unknown 1: 红 2: 黄 3: 绿 4:黑(未亮起)
    shapeType: number; // 信号灯形状 0: unknown : 水平 2:垂直 3: 其它
    blink: number; // 闪烁状态 0: 闪烁中 1: 未闪烁 3: unknown
    turnType: number; // 转向类型 0: 无转向 1: 左转 2: 右转 3: unknown
}
const lightType: {
    [key: number]: string;
} = {
    1: "red",
    2: "yellow",
    3: "green",
};
const getColumnLight = (data: ITrafficLight, theme: Theme) => {
    const { color, blink } = data;
    // class 基础 基础色 灯色 动画
    return (
        <div
            className={`circle circle-${theme} ${lightType[color]} ${
                lightType[color] + "-" + theme
            } ${blink === 0 ? "animation" : ""}`}
        ></div>
    );
};
function TrafficLight({
    data,
    theme,
}: {
    data: ITrafficLight[];
    theme: Theme;
}) {
    return data.length ? (
        <div className="traffic-light">
            {data.map((item, index) => {
                return (
                    <div
                        className={`traffic-item traffic-item-${theme}`}
                        key={index}
                    >
                        {getColumnLight(item, theme)}
                    </div>
                );
            })}
        </div>
    ) : null;
}

export default TrafficLight;
