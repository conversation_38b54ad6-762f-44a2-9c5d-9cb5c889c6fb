@import "@/theme/mixin.less";
.panel-box {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    // height: 100%;
    // pointer-events: none;
    position: absolute;
    top: 0;
    z-index: 10;
    .panel {
        width: auto;
        height: auto;
        position: absolute;
        color: @panel-font-color;
        background: @panel-bg-color;
        box-shadow: @panel-bs-color;
        backdrop-filter: blur(18px);
        border-radius: 4px;
        // z-index: 12;
        box-sizing: border-box;
        overflow: hidden;
        -webkit-user-select: none; /* Safari */
        -moz-user-select: none; /* Firefox */
        -ms-user-select: none; /* IE/Edge */
        user-select: none;
    }
    .carInfo {
        width: 300px;
        height: 135px;
        background: @carInfo-bg-color;
        // top: 10px;
        // left: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: inherit !important;
    }
    .vehicleInfo {
        width: 430px;
        height: 270px;
        // left: 40px;
        // bottom: 64px;
        border: none;
        border-radius: 8px;
        overflow: hidden;
    }
    .runInfo {
        width: 300px;
        background: @carInfo-bg-color;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .topicOne {
        width: 430px;
        height: auto;
        // left: 10px;
        // top: 420px;
    }
    .decision,
    .controls {
        width: 300px;
        // height: auto;
        // left: 10px;
        // top: 594px;
    }
    .mapBox {
        width: 430px;
        height: 270px;
        border-radius: 8px;
        box-shadow: @panel-bs-color;
        border: none;
        // right: 164px;
        // bottom: 64px;
        background: @mapBox-bg-color;
        overflow: hidden;
    }

    .table {
        background: transparent !important;
        box-shadow: none !important;
    }
    .empty {
        background: @panel-bg-color;
        box-shadow: @panel-bs-color;
    }
    .vehicleSpeed {
        // right: 230px;
        // bottom: 380px;
        //top: 220px;
    }
    .throttle {
        //left: 230px;
        //top: 220px;
        // right: 230px;
        // bottom: 490px;
    }
    .steeringAngle {
        // right: 10px;
        // bottom: 490px;
        // right: 10px;
        // bottom: 380px;
        // top: 440px;
    }
    .brake {
        //left: 230px;
        //top: 440px;
        // right: 10px;
        // bottom: 490px;
    }
    .yaw {
        // left: 10px;
        // top: 650px;
    }
    .fault {
        width: 300px;
        height: auto;
        // right: 10px;
        // bottom: 20px;
    }
    .logo {
        width: 300px;
        height: 110px;
    }
}

.eme-messege {
    .ant-message-notice-content {
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 6px 16px 0 rgb(0 0 0 / 8%),
            0 3px 6px -4px rgb(0 0 0 / 12%), 0 9px 28px 8px rgb(0 0 0 / 5%);
        pointer-events: all;
        width: 360px;
        height: 80px;
        .ant-message-custom-content {
            display: flex;
            justify-content: flex-start;
            padding-left: 26px;
            align-items: center;
            height: 100%;
            span {
                font-size: 28px;
                font-family: "SourceHanSansCNMedium";
                font-weight: 500;
                letter-spacing: 1px;
                &:last-child {
                    color: #141b27;
                }
            }
        }
    }
}

.ant-message-notice .ant-message-warning .anticon {
    color: #ff4f4f !important;
}
.eme-messege.dark {
    .ant-message-notice-content {
        pointer-events: all;
        width: 360px;
        height: 80px;
        background: #262933;
        box-shadow: 0px 4px 16px 0px #141b27;
        border-radius: 8px;
        span {
            &:last-child {
                color: #e9eef9;
            }
        }
    }
}
.ant-empty-description {
    color: @panel-font-color !important;
}

//Thmi 竖屏适配
.carInfo.vertical {
    width: 500px;
    height: 260px;
    .dashboard-one {
        width: 100%;
        transform: scale(1);
    }
    .angle,
    .speed,
    .acceleration {
        transform: scale(1.2);
        margin: 0;
    }
    .limit-box {
        padding: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        right: -120px;
        .round {
            margin: 0 0 30px 0;
        }
    }
}
.carInfo.vertical {
    .limit-box {
        height: 100%;
    }
}
.decision.vertical,
.controls.vertical {
    width: 500px;
    height: 280px;
    .row {
        .title {
            width: 300px;
            font-size: 24px;
        }
        .val {
            font-size: 24px;
            span.iconfont {
                font-size: 24px;
            }
        }
    }
    .tools {
        top: 10px;
        right: 10px;
        p {
            width: 16px;
            height: 16px;
        }
    }
}
.controls.vertical {
    height: 418px;
}
.mapBox.vertical {
    width: 500px !important;
}
.fault.vertical {
    width: 500px;
    .old {
        height: 60px !important;
    }
    .list {
        border-radius: 4px;
        margin: 0 auto 4px !important;
        .name {
            font-size: 24px !important;
        }
        .btn {
            height: 30px !important;
            line-height: 30px !important;
            font-size: 24px !important;
        }
    }
    .new {
        height: 60px !important;
        .top {
            height: 60px !important;
        }
    }
    // .icon-up,
    // .icon-down{
    //     font-size: 32px;
    // }
    .no-auto {
        height: 60px !important;
    }
}
.logo.vertical {
    width: 500px;
    height: 183px;
}
//THMI横屏适配
.carInfo.hmi-landscape {
    width: 300px;
    height: 135px;
    .angle,
    .speed,
    .acceleration {
        height: 160px;
        .top {
            width: 90px;
            height: 90px;
            font-size: 24px;
            .text {
                height: 32px;
                line-height: 32px;
                margin-bottom: 10px;
                font-size: 26px;
            }
            .unit {
                font-size: 12px;
                bottom: 7px;
                &.du {
                    top: 10px;
                    right: 5px;
                }
            }
        }
    }
    .speed {
        .top {
            width: 128px;
            height: 128px;
            font-size: 40px;
            .unit {
                bottom: 34px;
            }
        }
    }
    .icon-auto {
        width: 90px;
        i {
            font-size: 20px;
        }
    }
    .icon-gear {
        i {
            font-size: 14px;
            &.highlight {
                font-size: 20px;
            }
        }
    }
    .icon-turn {
        i {
            font-size: 20px;
        }
    }
    .limit-box {
        height: 180px;
        right: -135px;
        padding: 11px 12px;
        border-radius: 4px;
        .round {
            height: 85px;
            width: 85px;
            border: 5px solid red;
            b {
                font-size: 35px;
                line-height: 60px;
            }
            span {
                font-size: 18px;
                margin: -15px 0 20px 0;
            }
        }
        .set-speed {
            b {
                font-size: 35px;
                line-height: 50px;
            }
            span {
                font-size: 16px;
            }
        }
    }
}
.decision.hmi-landscape,
.controls.hmi-landscape,
.fault.hmi-landscape {
    width: 300px;
    .title,
    .val {
        font-size: 14px !important;
        line-height: 20px !important;
        span.iconfont {
            font-size: 14px;
        }
    }
    .title {
        width: 160px !important;
    }
    .old {
        height: 35px !important;
        border-radius: 4px;
        .name {
            font-size: 14px !important;
        }
    }
    .no-auto {
        height: 35px !important;
    }
    .new {
        .name {
            font-size: 14px !important;
        }
        .top {
            height: 35px !important;
        }
    }
    .list {
        margin: 0 auto 4px !important;
        // .icon-up,
        // .icon-down{
        //     font-size: 20px;
        // }
    }
    .tools {
        top: 6px;
        right: 6px;
        p {
            width: 8px;
            height: 8px;
        }
    }
    .btn {
        height: 20px !important;
        line-height: 20px !important;
        font-size: 12px !important;
        margin: 4px !important;
        padding: 0 4px !important;
    }
}
.mapBox.hmi-landscape {
    width: 300px;
}
.logo.hmi-landscape {
    width: 300px;
    height: 110px;
}
