/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-30 19:50:18
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 16:04:26
 * @FilePath: \deckgl\src\components\InfoPanel\index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import React, { useRef, useEffect, useLayoutEffect, useState } from "react";
import "./index.less";
import CarInfo from "./Dashboard/CarInfo";
import VehicleInfo from "./Dashboard/VehicleInfo";
import RunInfo from "./Dashboard/RunInfo";
import TopicDecision from "./Topic/Decision";
import TrafficLight from "./TrafficLight";
import MapBox from "../Map/MapBox";
import Draggable from "react-draggable";
import { message } from "antd";
import Fault from "./Fault";
import Logo from "./Logo";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { setConfig } from "@/features/dataSlice";
import { play, stop } from "@/utils/speech";
import Videos from "./Videos";
import Voice from "@/components/Voice";
import Constrains from "@/components/InfoPanel/Constrains";
import { STOPREASON } from "@/utils/enum";
import { useTranslation } from "react-i18next";
let timeTicket: any = null;
function InfoPanel() {
    const { t } = useTranslation("enum");
    const dispatch = useDispatch();

    const config = useSelector((state: RootState) => state.dataReducer.config);
    const UTMZone = useSelector(
        (state: RootState) => state.dataReducer.data.UTMZone
    );
    const customData = useSelector(
        (state: RootState) => state.dataReducer.customData
    );

    const operation = useSelector(
        (state: RootState) => state.dataReducer.operation
    );
    const data = useSelector((state: RootState) => state.dataReducer.data.data);
    const [bounds, setBounds] = useState({
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    });
    const [dragg, setDragg] = useState(false); //是否在拖拽
    const { theme, layoutConfig = [], parentApp } = config;
    const { info } = data?.focus?.properties || {};

    const errorCodes = data?.errorCodes || [];
    const { stopReason, autoDriverStatus, laneChangeStatus, severityLevel } =
        info || {};
    const [estopMessage, contextHolder] = message.useMessage();
    const panelBox = useRef<HTMLDivElement>(null);

    const playVoice = (text: string) => {
        if (operation.voice) {
            play(text);
        }
    };
    useEffect(() => {
        if (stopReason && stopReason !== 0) {
            estopMessage.open({
                type: "warning",
                key: "estop",
                duration: 0,
                content: t(STOPREASON[stopReason]),
                className: `eme-messege ${theme}`,
            });
        } else {
            estopMessage?.destroy("estop");
        }
    }, [stopReason]);

    useEffect(() => {
        if (autoDriverStatus == 1) {
            playVoice("退出自动驾驶");
        }
        if (autoDriverStatus == 0) {
            playVoice("进入自动驾驶");
        }
    }, [autoDriverStatus]);
    useEffect(() => {
        if (autoDriverStatus == 0) {
            switch (laneChangeStatus) {
                case 1:
                    playVoice("即将向左变道");
                    break;
                case 2:
                    playVoice("即将向右变道");
                    break;
                default:
                    break;
            }
        }
    }, [laneChangeStatus]);
    useEffect(() => {
        if (autoDriverStatus == 0) {
            if (severityLevel > 2) {
                playVoice("严重故障,请人工接管!");
            }
        }
    }, [severityLevel]);

    const setComponent = (id: string) => {
        // todo:  子组件多次渲染 data
        switch (id) {
            case "carInfo":
                return <CarInfo {...info} />;
            case "vehicleInfo":
                return <VehicleInfo {...info} />;
            case "runInfo":
                return <RunInfo {...info} />;
            case "decision":
                return <TopicDecision data={info?.decision} idc={"decision"} />;
            case "controls":
                return <TopicDecision data={info?.diff} idc={"controls"} />;
            case "constrains":
                return <Constrains data={info?.constrains} />;
            case "mapBox":
                return (
                    <MapBox
                        workData={{
                            ...data?.focus,
                            UTMZone,
                            customData,
                        }}
                        theme={theme || "light"}
                    />
                );
            case "fault":
                return <Fault errorCode={errorCodes} dragg={dragg} />;
            case "videos":
                return <Videos />;
            case "traffic":
                return (
                    <TrafficLight
                        data={info?.trafficLights || []}
                        theme={theme || "light"}
                    />
                );
            case "logo":
                return <Logo theme={theme || "light"} />;
            default:
                break;
        }
    };
    // 拖拽结束的到当前位置百分比
    const onSetDragablePosition = (type: string) => {
        // if (!panelBox || !panelBox.current) return;
        const deepClone = JSON.parse(JSON.stringify(config.layoutConfig));
        const dom = document.getElementById(type);
        const { clientWidth, clientHeight } = dom || {};
        // const { clientHeight, clientWidth } = panelBox && panelBox.current;
        // 处理得到transform属性的x,y
        if (
            clientWidth &&
            clientHeight &&
            dom &&
            dom.style.transform &&
            panelBox &&
            panelBox.current
        ) {
            const tanslateString = dom.style.transform;
            const [x, y] = tanslateString
                .slice(10, tanslateString.length - 1)
                .split(",")
                .map((item) => item.slice(0, item.length - 2));
            const newConfig = deepClone.map((item: any) => {
                if (item.name === type) {
                    item.position = {
                        left: Number(x) / (panelBox.current?.clientWidth || 1),
                        top:
                            Number(y) /
                            (document.documentElement.clientHeight || 1),
                    };
                }
                return item;
            });
            dispatch(
                setConfig({
                    ...config,
                    layoutConfig: newConfig,
                })
            );
        }
        //故障诊断里有点击事件 避免拖拽结束后立即触发click事件
        if (type == "fault") {
            if (timeTicket) {
                clearTimeout(timeTicket);
                timeTicket = null;
            }
            timeTicket = setTimeout(() => {
                setDragg(false);
            }, 500);
        }
    };
    // 返回panel组件渲染位置
    const clacTranslatePos = ({ left, top }: { left: number; top: number }) => {
        if (panelBox && panelBox.current) {
            const { clientWidth } = panelBox && panelBox.current;
            const y = document.documentElement.clientHeight * top;
            const x = clientWidth * left;
            return { x, y };
        }
        return { x: 0, y: 0 };
    };
    const handleResize = () => {
        if (panelBox && panelBox.current) {
            const { offsetWidth } = panelBox.current;
            setBounds({
                left: 0,
                top: 0,
                right: offsetWidth - 100,
                bottom: document.documentElement.clientHeight - 100,
            });
        }
    };
    /**
     * @description: 拖拽事件
     * @param {any} event
     * @param {string} type
     * @return {*}
     */
    const handleDrag = (event: any, type: string) => {
        event.preventDefault();
        if (type == "fault") {
            setDragg(true);
        }
    };
    useLayoutEffect(() => {
        handleResize();
        // 第一次初始化就执行一遍
        let resizeObserver: any = new ResizeObserver(() => handleResize());
        // 定义一个检测变动的状态机
        resizeObserver.observe(panelBox.current);
        // 把这个检测机制绑定到当前元素
        return () => {
            resizeObserver.disconnect(panelBox.current);
            resizeObserver = null;
            stop("");
            if (timeTicket) {
                clearTimeout(timeTicket);
                timeTicket = null;
            }
        };
    }, []);
    return (
        <div ref={panelBox} id="panel-box" className="panel-box">
            {contextHolder}
            {layoutConfig.map((item: any, index: number) => {
                if (item.display) {
                    const position = item.position
                        ? clacTranslatePos(item.position)
                        : { x: 0, y: 0 };
                    return (
                        <Draggable
                            key={index}
                            position={position}
                            bounds={bounds}
                            onDrag={(event) => handleDrag(event, item.name)}
                            onStop={(e) => {
                                onSetDragablePosition(item.name);
                            }}
                            onStart={(event) => event.preventDefault()}
                            allowAnyClick={false}
                        >
                            <div
                                key={index}
                                id={item.name}
                                className={`panel ${item.name}
                                ${
                                    parentApp.indexOf("T-HMI") !== -1
                                        ? item.isVertical
                                            ? "vertical"
                                            : "hmi-landscape"
                                        : ""
                                }
                                `}
                            >
                                {setComponent(item.name)}
                            </div>
                        </Draggable>
                    );
                }
            })}
            <Voice theme={config.theme}></Voice>
        </div>
    );
}
export default InfoPanel;
