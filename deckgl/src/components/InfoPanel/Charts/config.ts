/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-14 10:50:12
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2023-01-13 14:46:01
 * @FilePath: /deckgl/src/components/InfoPanel/Charts/config.ts
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

const velocityOption: object = {
    id: "vehicleSpeed",
    title: "车速",
    unit: "m/s",
    nowList: [],
    planList: [],
    xAxisList: [],
    color: "rgba(147, 52, 255, 1)",
};
const throttleOption: object = {
    id: "throttle",
    title: "油门踏板开度",
    unit: "°",
    nowList: [],
    planList: [],
    xAxisList: [],
    color: "rgba(52, 255, 243, 1)",
};
const steering_angleOption: object = {
    id: "steeringAngle",
    title: "方向盘转角",
    unit: "°",
    nowList: [],
    planList: [],
    xAxisList: [],
    color: "rgba(255, 125, 125, 1)",
};

const brakeOption: object = {
    id: "brake",
    title: "制动踏板开度",
    unit: "°",
    nowList: [],
    planList: [],
    xAxisList: [],
    color: "rgba(167, 255, 52, 1)",
};

const yawOption: object = {
    id: "yaw",
    title: "Yaw",
    unit: "°",
    nowList: [],
    planList: [],
    xAxisList: [],
    color: "rgba(167, 255, 52, 1)",
};
/**
 * @description: 所需图表
 * @return {*}
 */
const chartList: Array<any> = [
    velocityOption,
    steering_angleOption,
    throttleOption,
    brakeOption,
    yawOption,
];

/**
 * @description: 图表字体不同分辨率适配
 * @return {*}
 */
const setScale = () => {
    let clientWidth: any = document.body.clientWidth;
    let scale12 = clientWidth * (12 / 1920); //针对svg计算等比缩放
    let scale14 = clientWidth * (14 / 1920); //针对svg计算等比缩放
    let scale16 = clientWidth * (16 / 1920); //针对svg计算等比缩放
    let scale60 = clientWidth * (60 / 1920); //针对svg计算等比缩放
    let scaleTop = clientWidth * (60 / 1920); //针对svg计算等比缩放
    let scaleLeft = clientWidth * (60 / 1920); //针对svg计算等比缩放
    let scaleBottom = clientWidth * (60 / 1920); //针对svg计算等比缩放
    let legendWidth = clientWidth * (12 / 1920);
    let legendTop = clientWidth * (25 / 1920);
    switch (clientWidth) {
        case clientWidth < 1000:
            scaleTop = clientWidth * (80 / 1920);
            scaleLeft = clientWidth * (60 / 1920);
            scaleBottom = clientWidth * (60 / 1920);
            legendWidth = clientWidth * (10 / 1920);
            legendTop = clientWidth * (10 / 1920);
            break;
        case clientWidth > 1000 && clientWidth <= 1920:
            scaleLeft = clientWidth * (50 / 1920);
            scaleBottom = clientWidth * (50 / 1920);
            legendWidth = clientWidth * (20 / 1920);
            legendTop = clientWidth * (20 / 1920);
            break;
        case clientWidth > 1920 && clientWidth <= 2560:
            scaleLeft = clientWidth * (70 / 2560);
            scaleBottom = clientWidth * (70 / 2560);
            legendWidth = clientWidth * (18 / 1920);
            legendTop = clientWidth * (20 / 1920);
            break;
        case clientWidth > 2560 && clientWidth <= 3840:
            scaleLeft = clientWidth * (100 / 3840);
            scaleBottom = clientWidth * (90 / 3840);
            legendWidth = clientWidth * (25 / 1920);
            legendTop = clientWidth * (20 / 1920);
            break;
        default:
    }

    return {
        scale12,
        scale14,
        scale16,
        scale60,
        scaleTop,
        scaleLeft,
        scaleBottom,
        legendWidth,
        legendTop,
    };
};

let xAxisData = Array.from(Array(90), (v, k) => k);

const chartObj: any = {
    vehicleSpeed: {
        id: "vehicleSpeed",
        title: "车速",
        unit: "km/h",
        color: "rgba(147, 52, 255, 1)",
    },
    throttle: {
        id: "throttle",
        title: "油门踏板开度",
        unit: "°",
        color: "rgba(52, 255, 243, 1)",
    },
    steeringAngle: {
        id: "steeringAngle",
        title: "方向盘转角",
        unit: "°",
        color: "rgba(255, 125, 125, 1)",
    },
    brake: {
        id: "brake",
        title: "制动踏板开度",
        unit: "°",
        color: "rgba(167, 255, 52, 1)",
    },
    yaw: {
        id: "yaw",
        title: "Yaw",
        unit: "°",
        color: "rgba(167, 255, 52, 1)",
    },
};

const setTextColor: any = {
    dark: "rgba(255, 255, 255, 0.7)",
    light: "rgba(0, 0, 0, 0.7)",
};

export { chartObj, xAxisData, chartList, setScale, setTextColor };
