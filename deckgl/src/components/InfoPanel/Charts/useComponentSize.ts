/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-07 13:39:13
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2022-11-30 18:48:47
 * @FilePath: /deckgl/src/components/InfoPanel/Charts/useComponentSize.ts
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import React, { useState, useLayoutEffect } from "react";

function getSize(el: HTMLDivElement) {
    if (!el) {
        return {};
    }
    // 之前求高度都是用的getComputedClientRect()这个方法
    return {
        width: el.offsetWidth,
        height: el.offsetHeight,
    };
}

function useComponentSize(ref: React.MutableRefObject<HTMLDivElement>) {
    let [size, setSize] = useState(getSize(ref.current));

    function handleResize() {
        if (ref && ref.current) {
            setSize(getSize(ref.current));
        }
    }
    useLayoutEffect(() => {
        handleResize();
        // 第一次初始化就执行一遍
        let resizeObserver: any = new ResizeObserver(() => handleResize());
        // 定义一个检测变动的状态机
        resizeObserver.observe(ref.current);
        // 把这个检测机制绑定到当前元素
        return () => {
            resizeObserver.disconnect(ref.current);
            resizeObserver = null;
        };
    }, []);

    return size;
}
export default useComponentSize;
