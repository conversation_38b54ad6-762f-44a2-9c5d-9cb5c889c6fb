import { useEffect, useState, useRef } from "react";
import "./index.less";
import { setScale, xAxisData, chartObj, setTextColor } from "./config";
import * as echarts from "echarts";
import _ from "lodash";
import useEchartResizer from "./useEchartResizer";

interface LineProps {
    id: string | number;
    now: string | number;
    plan: string | number;
    theme: any;
    stemp: string | number;
    position?: {
        width: string;
        height: string;
    };
}

function LineChart({ id, now, plan, stemp, theme, position }: LineProps) {
    const chartRef: any = useRef<HTMLDivElement>(null);
    useEchartResizer(chartRef);
    let [chart, setChart] = useState<echarts.ECharts | null>(null);
    let [n, setN] = useState(0); //节流
    let textColor = setTextColor[theme];
    /**
     * @description: 数据组装
     * @return {*}
     */
    const setOption = () => {
        const {
            scale12,
            scale14,
            scale16,
            scaleTop,
            scaleBottom,
            scaleLeft,
            legendWidth,
            legendTop,
        } = setScale();
        return {
            title: {
                text: chartObj[id].title + chartObj[id].unit,
                itemGap: 5,
                textStyle: {
                    fontSize: scale14,
                    color: textColor,
                },
            },
            color: [chartObj[id].color, "#0C83FF"],
            tooltip: {
                trigger: "axis",
                textStyle: {
                    fontSize: scale16,
                    fontWeight: "normal",
                },
            },
            legend: {
                type: "scroll",
                top: legendTop,
                data: id !== "yaw" ? ["观测值", "规划值"] : ["观测值"],
                itemWidth: legendWidth,
                itemHeight: legendWidth,
                textStyle: {
                    color: textColor,
                    fontSize: scale12,
                },
            },
            grid: {
                top: scaleTop,
                bottom: scaleBottom,
                left: scaleLeft,
            },
            xAxis: {
                type: "category",
                boundaryGap: false,
                data: xAxisData,
                splitNumber: 10,
                axisTick: {
                    show: false,
                    interval: "auto",
                },
                axisLabel: {
                    color: textColor,
                    fontSize: scale16,
                    hideOverlap: true,
                },
            },
            yAxis: {
                type: "value",
                axisLabel: {
                    color: textColor,
                    fontSize: scale16,
                    hideOverlap: true,
                    showMinLabel: false,
                    showMaxLabel: false,
                    formatter: "{value}",
                },
                axisLine: {
                    show: true,
                },
                splitLine: {
                    show: false,
                },
            },
            series: [
                {
                    name: "观测值",
                    type: "line",
                    data: [],
                    smooth: true,
                    symbol: "none",
                },
                {
                    name: "规划值",
                    type: "line",
                    data: [],
                    smooth: true,
                    symbol: "none",
                },
            ],
        };
    };
    /**
     * @description: 初始化执行
     * @return {*}
     */
    useEffect(() => {
        setChart(echarts?.init(chartRef?.current as HTMLElement));
        return () => {
            chart?.dispose();
        };
    }, []);
    useEffect(() => {
        chart?.setOption(setOption());
    }, [chart, theme]);

    useEffect(() => {
        if (stemp) {
            let oldOptions = chart?.getOption();
            if (!oldOptions) {
                chart?.setOption(setOption());
            }
            oldOptions?.series.map((item: any, index: number) => {
                if (item.data.length <= 90) {
                    item.data.push(index == 0 ? now : plan);
                } else {
                    item.data.shift();
                    item.data.push(index == 0 ? now : plan);
                }
            });
            setN((n += 1));
            if (n == 20) {
                chart?.setOption({ ...oldOptions });
                setN(0);
            }
        }
    }, [stemp]);
    return (
        <div
            className="my-chart"
            ref={chartRef}
            style={{
                height: position?.height || "100%",
                width: position?.width || "100%",
            }}
        ></div>
    );
}

export default LineChart;
