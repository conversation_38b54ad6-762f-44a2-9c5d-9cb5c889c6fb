/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-16 17:55:09
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2022-11-30 18:48:37
 * @FilePath: /deckgl/src/components/InfoPanel/Charts/useEchartResizer.ts
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import useComponentSize from "./useComponentSize";
import * as echarts from "echarts";
import React, { useEffect } from "react";

function useEchartResizer(chartRef: React.MutableRefObject<HTMLDivElement>) {
    const size = useComponentSize(chartRef);

    useEffect(() => {
        const chart =
            chartRef.current && echarts.getInstanceByDom(chartRef.current);
        if (chart) {
            chart.resize();
        }
    }, [chartRef, size]);
}
export default useEchartResizer;
