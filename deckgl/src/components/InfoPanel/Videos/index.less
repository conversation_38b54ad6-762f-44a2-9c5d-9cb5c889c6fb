@import "@/theme/mixin.less";
.video-view {
    // width: 300px;
    height: 300px;
    position: relative;
    display: flex;
    border-radius: 10px;

    .ant-empty {
        width: 300px;
        margin-inline: 0;
    }
    .image-item {
        width: 300px;
        height: 100%;
        position: relative;
        img {
            width: 100%;
            height: 100%;
            object-fit: fill;
            // user-drag: none;
        }
        .image-title {
            display: block;
            position: absolute;
            top: 2px;
            left: 50%;
            transform: translateX(-50%);
            color: #fff;
        }
    }
    .menu-btn {
        position: absolute;
        top: 2px;
        right: 5px;
        font-size: 16px;
        color: #fff;
        cursor: pointer;
    }
    .video-select-page {
        width: 200px;
        height: 300px;
        background-color: @panel-bg-color;
        position: relative;
        .select-title {
            width: 100%;
            height: 20px;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            margin: 5px 0;
        }

        .ant-checkbox-group {
            flex-direction: column;
            width: 100%;
            height: calc(100% - 30px);
            overflow-y: auto;
            .ant-checkbox-wrapper {
                margin-inline-start: 0;
                padding-left: 20px;
                width: 100%;
                overflow: hidden;
                span {
                    color: @panel-font-color;
                }
            }
        }
        .icon-close {
            position: absolute;
            right: 5px;
            top: 2px;
            cursor: pointer;
        }
    }
}
