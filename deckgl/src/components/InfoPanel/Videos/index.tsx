/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-28 17:29:10
 * @LastEditors: jack 501177081.com
 * @FilePath: /deckgl/src/components/InfoPanel/Videos/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { RootState } from "@/app/store";
import { IReplayTopic } from "@/types";
import { Checkbox, Empty, Image } from "antd";
import { CheckboxChangeEvent } from "antd/es/checkbox/Checkbox";
import { useCallback, useMemo, useState, useEffect } from "react";
import { useSelector } from "react-redux";

import "./index.less";
import LoadPng from "@/assets/img/load.png";
import { useTranslation } from "react-i18next";
let ros: any;
interface ITopicOption extends IReplayTopic {
    label: string;
    value: string;
}
interface ITopicInfo {
    [key: string]: {
        url: string;
        topic: any;
    };
}
function App() {
    const { t } = useTranslation("operation");
    const config = useSelector((state: RootState) => state.dataReducer.config);
    const currentReplayBagInfo = useSelector(
        (state: RootState) => state.dataReducer.currentReplayBagInfo
    );
    // 所有摄像头topic
    const [bagImageList, setBagImageList] = useState<ITopicOption[]>([]);
    // 多选
    const [selectTopicList, setSelectTopicList] = useState<string[]>([]);
    // topic image地址 topic实例
    const [topicInfo, setTopicInfo] = useState<ITopicInfo>({});

    const [displaySelectPage, setDisplaySelectPage] = useState(false);
    const { ip } = config.connectConfig;
    const onChange = (event: CheckboxChangeEvent) => {
        const { value } = event.target;
        let bool = false;
        const newList = JSON.parse(JSON.stringify(selectTopicList));
        for (let i = 0; i < selectTopicList.length; i++) {
            if (selectTopicList[i] === value) {
                bool = true;
                topicInfo[value]?.topic?.unsubscribe();
                newList.splice(i, 1);
                setSelectTopicList(newList);
                break;
            }
        }
        if (!bool) {
            newList.push(value);
            setSelectTopicList(newList);
            const filterTopic = bagImageList.filter(
                (item) => item.name === value
            );
            const { name, type } = filterTopic[0] || {};
            subTopic(name, type);
        }
    };
    // 订阅topic
    const subTopic = useCallback((name: string, type: string) => {
        if (!name || !type) return;

        const videoTopic = new ROSLIB.Topic({
            ros: ros,
            name: name,
            messageType: type,
        });

        videoTopic.subscribe((message: any) => {
            const { data } = message;
            const url = "data:image/jpeg;base64," + data;
            setTopicInfo((state) => {
                return {
                    ...state,
                    [name]: {
                        url,
                        topic: videoTopic,
                    },
                };
            });
        });
    }, []);

    // 初始化
    const initTopic = useCallback(
        (topics: IReplayTopic[] = []) => {
            let imageTopicArr: ITopicOption[] = [];
            console.log("Video topics initialization:", {
                totalTopics: topics?.length || 0,
                currentBag: currentReplayBagInfo?.bag_name,
            });

            if (topics?.length) {
                // 筛选出包含compressed的主题
                topics.forEach((item: IReplayTopic) => {
                    if (item.name.indexOf("compressed") > -1) {
                        imageTopicArr.push({
                            ...item,
                            label: item.name,
                            value: item.name,
                        });
                    }
                });
            }

            if (imageTopicArr && imageTopicArr.length) {
                const { name, type } = imageTopicArr[0];
                console.log("订阅首个视频主题:", name);
                subTopic(name, type);
                setSelectTopicList([name]);
            } else {
                console.warn(
                    "当前包中没有找到视频主题:",
                    currentReplayBagInfo?.bag_name
                );
                // 可以在这里设置一个状态，显示"当前包没有视频"的提示
            }
            setBagImageList(imageTopicArr);
        },
        [subTopic, currentReplayBagInfo?.bag_name]
    );

    useEffect(() => {
        setSelectTopicList([]);
        setTopicInfo({});
        if (!ip || !currentReplayBagInfo) return;

        console.log(
            "Video component: 检测到包切换，重新建立视频连接",
            currentReplayBagInfo.bag_name
        );

        if (ros) {
            console.log("Video component: 关闭旧的ROS连接");
            ros.close();
            ros = null;
        }

        ros = new ROSLIB.Ros({
            url: `ws://${ip}:${config.connectConfig.rosPort || 9060}`,
        });

        ros.on("connection", () => {
            console.log("Video component: ROS连接成功");
        });

        ros.on("error", (error: Error) => {
            console.error("Video component: ROS连接失败", error);
        });

        ros.on("close", () => {
            console.log("Video component: ROS连接关闭");
        });

        console.log(
            "Video component: 初始化视频主题",
            currentReplayBagInfo.topics?.length || 0
        );
        initTopic(currentReplayBagInfo.topics);

        // 组件卸载时清理连接
        return () => {
            console.log("Video component: 组件卸载，清理ROS连接");
            if (ros) {
                ros.close();
            }
            // 清理所有订阅的主题
            Object.values(topicInfo).forEach((info) => {
                if (
                    info.topic &&
                    typeof info.topic.unsubscribe === "function"
                ) {
                    info.topic.unsubscribe();
                }
            });
        };
    }, [ip, currentReplayBagInfo, initTopic]);

    return (
        <div className="video-view">
            {selectTopicList.length ? (
                <>
                    {selectTopicList.map((item) => {
                        return (
                            <div className="image-item" key={item}>
                                <Image
                                    width={"100%"}
                                    height={"100%"}
                                    src={
                                        topicInfo[item]
                                            ? topicInfo[item].url
                                            : LoadPng
                                    }
                                    alt={item}
                                    draggable={false}
                                />
                                <span className="image-title">{item}</span>
                            </div>
                        );
                    })}
                    {!displaySelectPage && (
                        <i
                            className="menu-btn iconfont icon-setting"
                            onClick={() => setDisplaySelectPage(true)}
                        ></i>
                    )}

                    {displaySelectPage && (
                        <div className="video-select-page">
                            <div className="select-title">Video</div>
                            <Checkbox.Group value={selectTopicList}>
                                {bagImageList.map((item) => {
                                    return (
                                        <Checkbox
                                            value={item.value}
                                            onChange={onChange}
                                            key={item.value}
                                            disabled={
                                                selectTopicList.length == 1 &&
                                                selectTopicList[0] ===
                                                    item.value
                                            }
                                        >
                                            {item.name.split("/")[1]}
                                        </Checkbox>
                                    );
                                })}
                            </Checkbox.Group>
                            <i
                                className="iconfont icon-close"
                                onClick={() => setDisplaySelectPage(false)}
                            ></i>
                        </div>
                    )}
                </>
            ) : (
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={t("noCameraInfo")}
                />
            )}
        </div>
    );
}
export default App;
