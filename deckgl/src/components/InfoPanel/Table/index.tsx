/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON>@trunk.tech
 * @Date: 2023-01-03 10:28:37
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2023-01-12 15:39:34
 * @FilePath: /deckgl/src/components/InfoPanel/Table/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import "./index.less";
import DataDisplay from "../DataDisplay";
import { Theme } from "@/types";
import { Empty } from "antd";
import { useTranslation } from "react-i18next";
function index({ data, theme }: { data: any; theme: Theme }) {
    const { t } = useTranslation("common");
    const CardList = ["vehicleSpeed", "throttle", "steeringAngle", "brake"];
    return (
        <div className={`table-container  ${data ? "" : "empty"}`}>
            {data ? (
                CardList.map((cardItem, index) => {
                    const itemData = data.filter(
                        (item: any) => item.id === cardItem
                    );
                    return (
                        <DataDisplay
                            {...itemData[0]}
                            theme={theme || "light"}
                            position={{ width: "48%", height: "48%" }}
                            key={index}
                        />
                    );
                })
            ) : (
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={t("emptyText")}
                />
            )}
        </div>
    );
}

export default index;
