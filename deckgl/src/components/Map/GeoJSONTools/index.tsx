/*
 * @Author: luofei <EMAIL>
 * @Date: 2024-03-21 17:41:51
 * @LastEditors: luofei <EMAIL>
 * @LastEditTime: 2024-03-26 11:16:32
 * @FilePath: /deckgl/src/components/Map/GeoJSONTools/index.tsx
 * @Description:
 *
 * Copyright (c) 2024 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import {
    forwardRef,
    useEffect,
    useState,
    ReactNode,
    useImperativeHandle,
} from "react";
import { Button, message, Upload, Modal, Flex } from "antd";

import {
    UploadOutlined,
    DownloadOutlined,
    LockOutlined,
} from "@ant-design/icons";

import Deck, { GeoJsonLayer, TextLayer, PolygonLayer } from "deck.gl";
import {
    PathStyleExtension,
    CollisionFilterExtension,
} from "@deck.gl/extensions";
import { center, points } from "@turf/turf";
import { themeStyle } from "@/theme";
import lightingEffect from "@/components/Map/config/LightEffect";
import { toLatLon } from "@/utils/mapTools";

type FileUploadButtonProps = {
    onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
    children: ReactNode;
};

const FileUploadButton = ({
    onFileChange,
    children,
}: FileUploadButtonProps) => {
    return (
        <Button
            className="custom-file-input-container"
            type="primary"
            style={{ position: "relative" }}
        >
            <input
                type="file"
                id="file-input"
                style={{
                    width: "100%",
                    height: "100%",
                    opacity: 0,
                    cursor: "pointer",
                    position: "absolute",
                    left: 0,
                }}
                onChange={(event) => {
                    const file = event.target.files[0];

                    const reader = new FileReader();

                    reader.onload = (e) => {
                        const geojson = JSON.parse(
                            (e.target?.result as string) || "{}"
                        );
                        if (Object.keys(geojson).length > 0) {
                            onFileChange(geojson);
                        }
                    };
                    reader.readAsText(file);
                }}
            />
            {children}
        </Button>
    );
};

const GeoJSONTools = forwardRef(
    (
        { geojsonURL, theme }: { geojsonURL: string; theme: string },
        ref: any
    ) => {
        const [geojson, setGeojson] = useState<any>(null);
        const [viewState, setViewState] = useState<any>({
            latitude: 30,
            longitude: 100,
            zoom: 12,
            pitch: 0,
            bearing: 0,
        });
        const [lock, setLock] = useState<any>(null);

        const creatData = (data: any) => {
            // json 内部如果含有 viewState
            if (data.viewState) {
                setViewState(data.viewState);
            } else {
                const [longitude, latitude] = center(data).geometry.coordinates;
                setViewState({
                    latitude,
                    longitude,
                    zoom: 12,
                    pitch: 0,
                    bearing: 0,
                });
            }
            if (data.lock) {
                setLock(data.lock);
            }
            // 计算 geojson 中心
            setGeojson(data);
        };
        // fetch加载地图
        useEffect(() => {
            fetch(geojsonURL)
                .then((response) => response.json())
                .then((data) => {
                    creatData(data);
                })
                .catch((error) => {
                    console.error(error);
                    message.error("地图无法读取,请上传地图!");
                });
        }, []);

        const layers = [
            new GeoJsonLayer({
                id: "geojson-layer",
                data: geojson,
                stroked: false,
                filled: true,
                extruded: true,
                wireframe: true,
                pickable: true,
                // pointType: "circle+text+icon ",
                getPointRadius: 0.1,
                getLineColor: themeStyle[theme].geoJson[0],
                lineWidthScale: 0.1,
                lineWidthMinPixels: 1,
                getLineWidth: 1,
                capRounded: true,
                getDashArray: (d) => {
                    if (d.properties.type === 1) {
                        return [20, 20];
                    } else {
                        return [0, 0];
                    }
                },
                // parameters: {
                //     depthTest: false,
                // },
                getFillColor: (d) => {
                    if (d?.properties?.type || d?.properties?.type === 0) {
                        return (
                            themeStyle[theme].geoJson[d.properties.type] ||
                            themeStyle[theme].geoJson.default
                        );
                    }
                    return themeStyle[theme].geoJson.default;
                },
                getElevation: (d) => {
                    const type = d.properties?.type;

                    switch (type) {
                        case "Coastline":
                            return 1.5;
                        case "Building":
                            return Math.floor(Math.random() * 11) + 10;
                        case "Checkpoint":
                            return 10;
                        case "Yard":
                            return 3;

                        default:
                            return 0;
                    }
                },
                dashJustified: true,
                extensions: [
                    new PathStyleExtension({
                        dash: true,
                        highPrecisionDash: false,
                    }),
                ],
            }),
            // label
            new TextLayer({
                id: "label-layer",
                data: geojson?.features || [],
                characterSet: "auto",
                getPosition: (d) => {
                    if (d.geometry.type === "Polygon") {
                        const features = points(d.geometry.coordinates[0]);
                        const [x, y] = center(features).geometry.coordinates;
                        return [x, y, 5];
                    }
                    if (
                        d.geometry.type === "Point" &&
                        d.properties.type === 0
                    ) {
                        const [x, y] = d.geometry.coordinates;
                        return [x, y, 2];
                    }
                },
                getText: (d) => {
                    if (
                        d.geometry.type === "Point" ||
                        d.geometry.type === "Polygon"
                    ) {
                        return d.properties.text;
                    }
                },
                getSize: (d) => {
                    if (d.geometry.type === "Point") {
                        return 8;
                    } else {
                        return 13;
                    }
                },
                getColor: themeStyle[theme].markColor,
                extensions: [new CollisionFilterExtension()],
            }),
            // lock
            new PolygonLayer({
                id: "lock-layer",
                data: lock || [],
                // stroked: true,
                filled: true,
                getFillColor: (d) =>
                    d.status === "open" ? [0, 255, 0] : [255, 0, 0],
                getPolygon: (d) => {
                    return d.position;
                },
                getLineWidth: 0,
            }),
        ];
        const uploadGeojson = (data: object) => {
            creatData(data);
        };

        ref &&
            useImperativeHandle(
                ref,
                () => ({
                    getData,
                }),
                [geojson, viewState, lock]
            );
        const getData = () => {
            const newGeojson = {
                ...geojson,
                viewState,
                lock,
            };
            return newGeojson;
        };
        const download = () => {
            const a = document.createElement("a");

            a.href = URL.createObjectURL(
                new Blob([JSON.stringify(getData(), null, 2)], {
                    type: "text/plain",
                })
            );
            a.download = "map.json";
            a.click();
        };

        const [isMobile, setIsMobile] = useState(false);

        const upLoadLock = (data: object) => {
            const lockData = data.map((item) => {
                const firstPositionConverted = toLatLon(item.position[0], 51);
                const positionConverted = item.position.map((point) =>
                    toLatLon(point, 51)
                );

                return {
                    ...item,
                    center: toLatLon(item.center, 51),
                    position: [...positionConverted, firstPositionConverted],
                };
            });
            setLock(lockData);
        };

        return (
            <div onContextMenu={(evt) => evt.preventDefault()}>
                <Deck
                    viewState={viewState}
                    onViewStateChange={(info) => setViewState(info.viewState)}
                    controller={true}
                    layers={layers}
                    onClick={(info) => console.log(info.viewport)}
                    effects={[lightingEffect]}
                    getTooltip={({ object }) =>
                        object &&
                        object.geometry &&
                        object.properties.type + "\n" + object.properties.text
                    }
                    // onHover={({ object }) => console.log(object)}
                ></Deck>
                <Flex
                    gap="small"
                    vertical
                    align="flex-end"
                    style={{
                        width: "120px",
                        padding: "10px",
                        float: "right",
                    }}
                >
                    <FileUploadButton
                        onFileChange={(data) => uploadGeojson(data)}
                    >
                        <UploadOutlined />
                        上传地图
                    </FileUploadButton>

                    <FileUploadButton onFileChange={(data) => upLoadLock(data)}>
                        <LockOutlined /> 上传锁站
                    </FileUploadButton>

                    <Button type="primary" onClick={download}>
                        <DownloadOutlined /> 下载文件
                    </Button>
                </Flex>
                {/* <Deck></Deck> */}
                <Modal
                    title="Basic Modal"
                    open={isMobile}
                    onOk={() => {
                        setIsMobile(false);
                    }}
                    onCancel={() => {
                        setIsMobile(false);
                    }}
                >
                    <p>Some contents...</p>
                    <p>Some contents...</p>
                    <p>Some contents...</p>
                </Modal>
            </div>
        );
    }
);
export default GeoJSONTools;
