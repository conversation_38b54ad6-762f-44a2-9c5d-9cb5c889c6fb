/*
 * @Author: luofei <EMAIL>
 * @Date: 2024-01-26 10:06:03
 * @LastEditors: luofei <EMAIL>
 * @LastEditTime: 2025-04-28 14:22:26
 * @FilePath: /deckgl/src/components/Map/layers/customLayer.tsx
 * @Description:
 *
 * Copyright (c) 2024 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import {
    GeoJsonLayer,
    TextLayer,
    PointCloudLayer,
    ScenegraphLayer,
    PolygonLayer,
    PathLayer,
    IconLayer,
    ScatterplotLayer,
    COORDINATE_SYSTEM,
    CompositeLayer,
} from "deck.gl";
import {
    PathStyleExtension,
    CollisionFilterExtension,
} from "@deck.gl/extensions";
import { themeStyle, getIconfont } from "@/theme";
import { getColorArrayForRgbAndOpacity } from "@/utils/index";
import { returnNumber } from "@/utils/index";

// truf
import { center, points } from "@turf/turf";
function rgbaStringToArray(rgbaString: any) {
    // 使用正则表达式匹配rgba字符串中的数值
    const rgbaValues = rgbaString.match(/[\d.]+/g);

    // 将匹配到的字符串转换为数值
    const red = parseInt(rgbaValues[0], 10);
    const green = parseInt(rgbaValues[1], 10);
    const blue = parseInt(rgbaValues[2], 10);
    const alpha = parseFloat(rgbaValues[3]) * 255;

    // 返回包含RGBA值的数组
    return [red, green, blue, alpha];
}
class CustomLayer extends CompositeLayer {
    // 转换数据
    // updateState({
    //     props,
    //     changeFlags,
    // }: {
    //     props: any;
    //     changeFlags: { dataChanged: boolean };
    // }) {
    //     if (changeFlags.dataChanged) {
    //         // console.log("geoJsonData" + props);
    //     }
    // }

    // filterSubLayer({ layer, viewport }) {
    //     return layer;
    // }

    renderLayers() {
        const {
            data,
            geoJsonData,
            config,
            glbConfig,
            truckDebugInfo,
            modelVisible,
        } = this.props as any;
        const { theme = "light" } = config || {};
        const { zoom, pitch, id } = this.context.viewport;
        const focusPosition = data?.focus?.position || null;
        const Tstyle = themeStyle[theme];
        // baseMap
        const baseMap = [
            // geojson 底图
            new GeoJsonLayer({
                id: "geojson-layer",
                data: geoJsonData,
                pickable: true,
                stroked: false,
                filled: true,
                extruded: true, //pitch == 0 ? false : true,
                wireframe: true,
                // pointType: "circle+text+icon ",
                getPointRadius: 0.3,
                getLineColor: (d) =>
                    Tstyle.geoJson[d?.properties?.type] || Tstyle.geoJson[0],
                lineWidthScale: 0.1,
                lineWidthMinPixels: 1,
                getLineWidth: 0,
                capRounded: true,
                getDashArray: (d) => {
                    return d.properties.type === 1 ? [20, 20] : [0, 0];
                },
                parameters: {
                    depthTest: false,
                },
                getFillColor: (d) => {
                    if (d?.properties?.type || d?.properties?.type === 0) {
                        return (
                            Tstyle.geoJson[d.properties.type] ||
                            Tstyle.geoJson.default
                        );
                    }
                    if (d?.properties?.symbol?.polygonFill) {
                        return rgbaStringToArray(
                            d?.properties?.symbol?.polygonFill
                        );
                    } else {
                        return Tstyle.geoJson.default;
                    }
                },
                getElevation: (d) => {
                    const type = d.properties?.type;
                    return Tstyle.elevation[type];
                },
                dashJustified: true,
                extensions: [
                    new PathStyleExtension({
                        dash: true,
                        highPrecisionDash: false,
                    }),
                ],
                // 在主题变化时 线和几何颜色更新
                updateTriggers: {
                    getFillColor: [theme],
                    getLineColor: [theme],
                },
            }),
            // label
            new TextLayer({
                id: "label-layer",
                data: geoJsonData?.features || [],
                characterSet: "auto",
                fontFamily: "sans-serif",
                sizeMaxPixels: 20,
                sizeMinPixels: 6,
                sizeUnits: "meters",
                getPosition: (d) => {
                    const coordinates = d.geometry.coordinates;
                    const type = d.geometry.type;
                    const properties = d.properties;

                    // 判断点类型和属性类型
                    if (type === "Point" && properties.type === 0) {
                        const [x, y] = coordinates;
                        return [x, y, 2];
                    }
                    // 判断是否存在符号或者为多边形类型
                    if (properties?.symbol || type === "Polygon") {
                        const features = points(coordinates[0]);
                        const [x, y] = center(features).geometry.coordinates;
                        return [x, y, 2];
                    }
                },
                getText: (d) => {
                    const { properties, geometry } = d ?? {};

                    if (properties?.symbol?.textName) {
                        return properties.symbol.textName;
                    }

                    if (
                        geometry.type === "Point" ||
                        geometry.type === "Polygon"
                    ) {
                        return properties.text;
                    }
                },
                getSize: (d) => {
                    // 点的字体小一些
                    const { geometry } = d ?? {};
                    if (geometry.type === "Point") {
                        return Tstyle.smallFontSize;
                    }
                    return Tstyle.middleFontSize;
                },
                getColor: Tstyle.markColor,
                extensions: [new CollisionFilterExtension()],
            }),
        ];
        if (data) {
            // 指定 data 类型
            const {
                glbs = [],
                points = [],
                marks = [],
                lines = [],
                boxs = [],
                dephboxs = [],
                circles = [],
                icons = [],
                customGeojson = [],
            } = data as any;
            let labelData: {
                position: any[];
                name: any;
                id: any;
                fontSize: number;
                fontColor: any;
            }[] = [];
            let vehicleLayers =
                Object.entries(glbs).map(([key, glb]) => {
                    const { scale, translation, orientation, height } =
                        glbConfig?.[key] || {};
                    glb.map((item, index) => {
                        // customFontSize, customFontColor 是针对每个title单独定制，Tstyle.glb[key]是每种模型的title的字体样式
                        const {
                            position,
                            name,
                            customFontSize,
                            customFontColor,
                        } = item;
                        let labelPosition: any[] = position;
                        if (key.indexOf("trailer")) {
                            const z = height * scale?.z + translation?.z || 5;
                            const [x, y] = position;
                            labelPosition = [x, y, z];
                            labelData.push({
                                position: [
                                    labelPosition[0],
                                    labelPosition[1],
                                    id == "top-down" ? z + 2 : height,
                                ],
                                name,
                                id: name,
                                fontSize:
                                    customFontSize ||
                                    Tstyle?.glbTitle?.[key]?.fontSize ||
                                    Tstyle.middleFontSize,
                                // 每种模型title的字体颜色 或者默认颜色
                                fontColor:
                                    customFontColor ||
                                    Tstyle?.glbTitle?.[key]?.fontColor ||
                                    Tstyle.markColor,
                            });
                        }
                    });
                    return [
                        // 模型
                        truckDebugInfo.visualization.trunkModel.value &&
                            new ScenegraphLayer({
                                id: `${key}`,
                                name: "gltf-layer",
                                data: glb,
                                pickable: true,
                                scenegraph: "/glb/optimized/" + key + ".glb",
                                getPosition: (d) => d.position,
                                getOrientation: (d) => [
                                    orientation?.x || 0,
                                    d.heading + orientation.y || 0,
                                    orientation?.z || 0,
                                ],
                                getColor: (d) => d.color || null,
                                getScale: (d) => [
                                    d?.customScale?.x || scale?.x || 1,
                                    d?.customScale?.y || scale?.y || 1,
                                    d?.customScale?.z || scale?.z || 1,
                                ],
                                getTranslation: [
                                    translation?.x || 0,
                                    translation?.y || 0,
                                    translation?.z || 0,
                                ],
                                // sizeScale: 1,
                                // _dataDiff:(newData, oldData) => [{startRow: index, endRow: index + 1}]
                                _lighting: "pbr",
                                // dataComparator: (newData, oldData) => {
                                //     return (
                                //         JSON.stringify(newData).length ===
                                //         JSON.stringify(oldData).length
                                //     );
                                // },
                                // opacity: modelOpacity ? 1 : 0.2,
                                visible:
                                    glb.length > 0 && zoom > 14 && modelVisible
                                        ? true
                                        : false,
                                // 离线加载模型
                                loadOptions: {
                                    draco: {
                                        workerUrl: "/draco/draco_worker.js",
                                    },
                                },
                                // getScene: (gltf) => {
                                //     console.log(gltf);

                                //     if (gltf && gltf.scenes) {
                                //         // gltf post processor replaces `gltf.scene` number with the scene `object`
                                //         return typeof gltf.scene === "object"
                                //             ? gltf.scene
                                //             : gltf.scenes[gltf.scene || 0];
                                //     }
                                //     return gltf;
                                // },
                            }),

                        focusPosition &&
                            glb.map((item, index) => {
                                const { position, points, polygons, distance } =
                                    item;
                                return [
                                    new PointCloudLayer({
                                        id: `${key + index}-pointcloud`,
                                        data: points || [],
                                        getPosition: (d) => d.position,
                                        coordinateSystem:
                                            COORDINATE_SYSTEM.METER_OFFSETS,
                                        coordinateOrigin: position,
                                        getColor: [255, 0, 0],
                                        pointSize: 2,
                                        visible: points && points.length > 0,
                                    }),
                                    new PolygonLayer({
                                        id: `${key + index}-polygonlayer`,
                                        data: polygons || [],
                                        getPolygon: (d) => {
                                            return d.contour;
                                        },
                                        coordinateSystem:
                                            COORDINATE_SYSTEM.METER_OFFSETS,
                                        coordinateOrigin: position,
                                        getFillColor: [255, 0, 0, 100],
                                        // 高程
                                        extruded: true,
                                        elevationScale: 0.001,
                                        visible:
                                            polygons && polygons.length > 0,
                                    }),
                                    // 动态测距
                                    new ScatterplotLayer({
                                        id: `${key + index}-distance`,
                                        data: distance || [],
                                        opacity: 0.8,
                                        stroked: true,
                                        filled: false,
                                        radiusUnits: "meters",
                                        lineWidthUnits: "meters",
                                        getLineWidth: 0.2,
                                        getLineColor: [255, 140, 10, 200],
                                        getRadius: (d) => d.radius,
                                        visible: focusPosition,
                                        coordinateSystem:
                                            COORDINATE_SYSTEM.METER_OFFSETS,
                                        coordinateOrigin: focusPosition,
                                    }),
                                ];
                            }),
                    ];
                }) || [];
            return [
                baseMap,
                new GeoJsonLayer({
                    id: "draw-geojson-layer",
                    data: customGeojson,
                    pickable: true,
                    filled: true,
                    getElevation: 0.01,
                    // 点
                    pointType: "icon+text",
                    pointRadiusUnits: "meters",
                    characterSet: "auto",
                    fontFamily: "sans-serif",
                    getText: (d: any) => d?.properties?.symbol?.textName,
                    getTextSize: (d) =>
                        d?.properties?.symbol?.fontSize || Tstyle.smallFontSize,
                    getTextColor: (d) =>
                        d?.properties?.symbol?.fontColor || Tstyle.markColor,
                    sizeMaxPixels: Tstyle.largeFontSize,
                    sizeMinPixels: 6,
                    // getTextAlignmentBaseline: "top",
                    // getTextPixelOffset: [0, -50],
                    // getIconPixelOffset: [0, -20],
                    getIcon: (d) => {
                        const { marker } = d?.properties?.symbol;
                        return getIconfont(marker);
                    },
                    getIconSize: 30,
                    iconSizeMaxPixels: 30,
                    // 线
                    getLineColor: (d) => {
                        if (d?.properties?.symbol?.polygonFill) {
                            const { polygonFill, polygonOpacity } =
                                d.properties.symbol;
                            return (
                                getColorArrayForRgbAndOpacity(
                                    polygonFill,
                                    polygonOpacity
                                ) || [135, 196, 240, 150]
                            );
                        }
                        return [135, 196, 240, 150];
                    },
                    getLineWidth: (d) => d?.properties?.symbol?.lineWidth || 0,
                    // 面
                    getFillColor: (d) => {
                        if (d?.properties?.symbol?.polygonFill) {
                            const { polygonFill, polygonOpacity } =
                                d.properties.symbol;
                            return (
                                getColorArrayForRgbAndOpacity(
                                    polygonFill,
                                    polygonOpacity
                                ) || [135, 196, 240, 150]
                            );
                        }
                        return [135, 196, 240, 150];
                    },
                    visible: customGeojson.length > 0 ? true : false,
                }),
                new ScatterplotLayer({
                    id: "scatterplot-layer",
                    data: circles,
                    stroked: true,
                    filled: true,
                    getPosition: (d) => d.position,
                    getRadius: (d) => d.radius || 1,
                    sizeUnits: "pixels",
                    getFillColor: (d) => d.color || Tstyle.pointColor,
                    getLineColor: (d) => d.lineColor || Tstyle.pointColor,
                    getLineWidth: (d) => d.lineWidth || 0,
                    opacity: 0.5,
                    visible: circles.length > 0 ? true : false,
                    // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                    // coordinateOrigin: coordinateOriginLatLon,
                }),
                new TextLayer({
                    id: "text-layer",
                    data: marks,
                    // 中文 此选项会产生性能开销，如果数据非常大，可能会导致该层需要更长的时间来加载。
                    characterSet: "auto",
                    getPosition: (d) => d.position,
                    getText: (d) => d.text,
                    getColor: (d) => d.color || Tstyle.markColor,
                    getSize: (d) => d.size || Tstyle.middleFontSize,
                    getAngle: 0,
                    getTextAnchor: (d) => d.textAnchor || "middle",
                    fontWeight: "bold",
                    getAlignmentBaseline: (d) =>
                        d.alignmentBaseline || "center",
                    backgroundPadding: [5, 5],
                    visible: marks.length > 0 ? true : false,
                    // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                    // coordinateOrigin: coordinateOriginLatLon,
                }),

                new ScatterplotLayer({
                    id: `model-center`,
                    data: labelData,
                    stroked: false,
                    filled: true,
                    getPosition: (d) => [d.position[0], d.position[1], 0.01],
                    getRadius: 0.1,
                    sizeUnits: "meters",
                    getFillColor: [255, 140, 0, 200],
                    opacity: 0.5,
                    visible: labelData && labelData.length > 0,
                }),

                new PathLayer({
                    id: "path-layer",
                    data: lines,
                    widthScale: 1,
                    widthMinPixels: 2,
                    sizeUnits: "meters",
                    getPath: (d) => d.path,
                    getColor: (d) => d.color || Tstyle.lineColor,
                    getWidth: (d) => d.width || 0.5,
                    getDashArray: (d) => {
                        if (d?.isDash) {
                            return [10, 10];
                        } else {
                            return [0, 0];
                        }
                    },
                    dashJustified: true,
                    extensions: [
                        new PathStyleExtension({
                            dash: true,
                            highPrecisionDash: false,
                        }),
                    ],
                    opacity: 0.5,
                }),
                new PointCloudLayer({
                    id: "point-cloud-layer",
                    data: points,
                    getPosition: (d) => d.position,
                    getColor: (d) => d.color || Tstyle.pointColor,
                    pointSize: 2,
                    sizeUnits: "pixels",
                    visible: points.length > 0 ? true : false,
                    // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                    // coordinateOrigin: coordinateOriginLatLon,
                }),

                new PolygonLayer({
                    id: "deph-polygon-layer",
                    data: dephboxs,
                    stroked: true,
                    filled: true,
                    wireframe: true,
                    extruded: true,
                    getPolygon: (d) => d.box,
                    getElevation: (d) => returnNumber(d?.height),
                    getFillColor: (d) => d.color || Tstyle.boxFillColor,
                    opacity: 1,
                    getLineColor: (d) => d.lineColor || Tstyle.boxFillColor,
                    getLineWidth: (d) => d.lineWidth || 0,
                    parameters: {
                        depthTest: true,
                    },
                    // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                    // coordinateOrigin: coordinateOriginLatLon,
                    visible: dephboxs.length > 0 ? true : false,
                }),
                vehicleLayers && vehicleLayers,
                new PolygonLayer({
                    id: "polygon-layer",
                    pickable: true,
                    data: boxs,
                    stroked: true,
                    filled: true,
                    wireframe: true,
                    extruded: true,
                    getPolygon: (d) => d.box,
                    getElevation: (d) => returnNumber(d?.height),
                    getFillColor: (d) => d.color || Tstyle.boxFillColor,
                    opacity: 1,
                    getLineColor: (d) => d.lineColor || Tstyle.boxFillColor,
                    getLineWidth: (d) => d.lineWidth || 0,
                    parameters: {
                        depthTest: true,
                    },
                    // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                    // coordinateOrigin: coordinateOriginLatLon,
                    visible: boxs.length > 0 ? true : false,
                }),
                new TextLayer({
                    id: "modelLabel-layer",
                    data: labelData,
                    // 中文 此选项会产生性能开销，如果数据非常大，可能会导致该层需要更长的时间来加载。
                    characterSet: "auto",
                    pickable: true,
                    getPosition: (d) => d.position,
                    getText: (d) => {
                        return d.name || d.id || "";
                    },
                    sizeMaxPixels: Tstyle.largeFontSize,
                    getColor: (d) => d?.fontColor,
                    getSize: (d) => d.fontSize,
                    fontFamily: "sans-serif",
                    sizeUnits: Tstyle.idSizeUnits || "pixels",
                    fontSettings: {
                        sdf: true,
                        fontSize: 256,
                        // buffer: 200,
                        radius: 80,
                        cutoff: 0.2,
                    },
                    outlineWidth: 0,
                    outlineColor: [255, 255, 255, 255],
                    // // getAlignmentBaseline: "bottom",
                    fontWeight: 900,
                    // background: false,
                    // backgroundPadding: [0, 0],
                    // getBackgroundColor: [255, 255, 255, 1],
                    // getBorderWidth: 0,

                    // backgroundPadding: [1, 0],
                    parameters: {
                        depthTest: false,
                    },
                    visible: labelData.length > 0,
                    // extensions: [new CollisionFilterExtension()],
                }),
                new IconLayer({
                    id: "icon-layer",
                    data: icons,
                    getIcon: (d) => d.icon,
                    // icon size is based on data point's contributions, between 2 - 25
                    sizeMaxPixels: Tstyle.largeFontSize,
                    getSize: (d) => d.size || Tstyle.middleFontSize,
                    sizeUnits: Tstyle.idSizeUnits || "pixels",
                    getPosition: (d) => d.position,
                    getPixelOffset: (d) => d.pixelOffset || [0, 0],
                    visible: icons.length > 0 ? true : false,
                    // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                    // coordinateOrigin: coordinateOriginLatLon,
                    parameters: {
                        depthTest: false,
                    },
                }),
            ];
        } else {
            return baseMap;
        }
    }
}
CustomLayer.layerName = "CustomLayer";
CustomLayer.defaultProps = {
    data: [],
    geoJsonData: null as any,
    config: {},
};
export default CustomLayer;
