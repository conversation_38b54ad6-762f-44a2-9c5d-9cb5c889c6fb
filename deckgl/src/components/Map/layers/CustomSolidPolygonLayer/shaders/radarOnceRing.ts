export default {
    inject: {
        "vs:#decl": `
        attribute float pingSpeed;
        attribute vec2 uvs;
        attribute vec4 changefillColors;
        varying vec2 vUvs;
        varying float vPingSpeed;
        varying vec4 vFillColor;
    `,
        "vs:#main-end": `\
        vUvs = uvs.xy;
        vPingSpeed = pingSpeed;
        vFillColor = changefillColors;
    `,
        "fs:#decl": `\
        uniform float currentTime;
        varying vec2 vUvs;
        varying float vPingSpeed;
        varying vec4 vFillColor;

        // radar
        vec4 RadarPing(vec2 uv, vec2 center, float innerTail, 
            float frontierBorder, float timeResetSeconds, 
            float radarPingSpeed, float fadeDistance)
            {
            vec2 diff = center-uv;
            float r = length(diff);
            float time = mod(currentTime, timeResetSeconds) * radarPingSpeed;

            float circle = smoothstep(time - innerTail, time, r) * smoothstep(time + frontierBorder,time, r);
            circle *= smoothstep(fadeDistance, 0.0, r); // fade to 0 after fadeDistance
            float alpha = circle * (1.0 - smoothstep(0.0, fadeDistance, r));

            return vec4(vec3(circle), alpha);
          }
        `,
        "fs:DECKGL_FILTER_COLOR": `\
        // 重置时间 单次放大展示时间 超过重置
        float resetTimeSec = 1.0;
        // 展示范围大小，范围为0-1 0时颜色不展示 1时展示圆形范围包含几何
        float fadeDistance = 0.5;
        // pingSpeed shader放大速度
        // 圆环外边 值越大越模糊 
        float frontierBorder = 0.015;
        // 圆环内边渐变宽度
        float innerBorder = 0.15;
        //  圆心位置
        vec2 center = vec2(0.5, 0.5);
        color.rgba = RadarPing(vUvs, center, innerBorder, frontierBorder, resetTimeSec, vPingSpeed, fadeDistance) * vFillColor ;
        `,
    },
};
