import { SolidPolygonLayerProps, SolidPolygonLayer, Accessor } from "deck.gl";
import { DefaultProps } from "@deck.gl/core";
import GL from "@luma.gl/constants";
import radarOnceRing from "./shaders/radarOnceRing";
import radarMultiRing from "./shaders/radarMultiRing";
// import waterShader from "./injects/water";
import * as turf from "@turf/turf";
type _TimePolygonLayerProps<DataT> = {
    /**
     * time
     */
    currentTime?: number;
    /**
     * radar speed
     */
    getPingSpeed?: Accessor<DataT, number>;
    /**
     * shader type
     */
    radarType?: string;
    /**
     *  uv
     */
    getUvs?: Accessor<DataT, number[]>;
    getFillColors?: Accessor<DataT, [number, number, number, number]>;
};
export type _TimeSimpleMeshLayerProps<DataT = any> =
    _TimePolygonLayerProps<DataT> & SolidPolygonLayerProps<DataT>;

const defaultProps: DefaultProps<_TimeSimpleMeshLayerProps> = {
    currentTime: { type: "number", value: 0 },
    getPingSpeed: { type: "accessor", value: 1 },
};
const ATTRIBUTE_TRANSITION = {
    enter: (value: any, chunk: any) => {
        return chunk.length
            ? chunk.subarray(chunk.length - value.length)
            : value;
    },
};
const DEFAULT_COLOR: [number, number, number, number] = [0, 0, 0, 255];
/**
 * 经纬度计算矩形顶点
 * @param param0
 * @returns
 */
const calculateSquareVerticesGpsWithRotation = ({
    center = [0, 0],
    sideLength = 1,
    angle = 0,
}: {
    center: [number, number];
    sideLength: number;
    angle: number;
}): number[][] => {
    // 四个方向对应于正方形的四个角（未旋转状态下）
    const bearings = [225, 315, 45, 135];

    // 对角线的一半长度
    const halfDiagonal = (sideLength * Math.sqrt(2)) / 2;

    // 给定的中心点
    const centerPoint = turf.point([center[0], center[1]]);

    // 计算旋转前的四个角顶点的经纬度坐标
    const verticesBeforeRotation = bearings.map((bearing) => {
        return turf.destination(centerPoint, halfDiagonal, bearing, {
            units: "meters",
        });
    });

    // 旋转正方形：对每个顶点应用旋转变换
    const verticesAfterRotation = verticesBeforeRotation.map((vertex) => {
        return turf.transformRotate(vertex, angle, {
            pivot: centerPoint,
        });
    });

    // 提取经过旋转后的顶点坐标
    const rotatedVertices = verticesAfterRotation.map((vertex) => {
        return vertex.geometry.coordinates;
    });

    return rotatedVertices;
};
/**
 * 直角坐标系计算矩形顶点
 * @param data
 * @param system
 * @returns
 */
const calculateSquareVerticesAxisWithRotation = ({
    center = [0, 0],
    sideLength = 1,
    angle = 0,
}: {
    center: [number, number];
    sideLength: number;
    angle: number;
}): number[][] => {
    const rotatePoint = (
        center: [number, number],
        point: [number, number],
        angle: number
    ) => {
        const radian = (angle * Math.PI) / 180;
        const rotatedX =
            center[0] +
            (point[0] - center[0]) * Math.cos(radian) -
            (point[1] - center[1]) * Math.sin(radian);
        const rotatedY =
            center[1] +
            (point[1] - center[1]) * Math.cos(radian) +
            (point[0] - center[0]) * Math.sin(radian);
        return [rotatedX, rotatedY];
    };
    // Step 1: 计算未旋转矩形的顶点
    const halfWidth = sideLength / 2;
    const points: [number, number][] = [
        [-halfWidth, halfWidth],
        [halfWidth, halfWidth],
        [halfWidth, -halfWidth],
        [-halfWidth, -halfWidth],
    ].map((point) => [center[0] + point[0], center[1] + point[1]]);

    // Step 2: 旋转每个顶点
    return points.map((point) => rotatePoint(center, point, angle));
};
export const getPolygonInfo = (data: any[], system: string | null) => {
    const deepData = JSON.parse(JSON.stringify(data));
    const UVS = [0, 0, 1, 0, 1, 1, 0, 1, 0, 0];
    let arr = [];
    if (system === "gps") {
        arr = deepData.map((item: any) => {
            const polygon = calculateSquareVerticesGpsWithRotation({
                center: item.position,
                sideLength: item.size || 1,
                angle: item.rotation || 0,
            });
            polygon.push(polygon[0]); // 闭合多边形
            return {
                ...item,
                polygon,
                uvs: UVS,
            };
        });
        // axis
    } else {
        arr = deepData.map((item: any) => {
            const polygon = calculateSquareVerticesAxisWithRotation({
                center: item.position,
                sideLength: item.size || 1,
                angle: item.rotation || 0,
            });
            polygon.push(polygon[0]); // 闭合多边形
            return {
                ...item,
                polygon,
                uvs: UVS,
            };
        });
    }
    return arr;
};
export default class TimeSolidPolygonLayer<
    DataT = any,
    ExtraProps extends {} = {}
> extends SolidPolygonLayer<
    DataT,
    Required<_TimeSimpleMeshLayerProps> & ExtraProps
> {
    static layerName = "custom-solid-polygon-layer";
    static defaultProps = defaultProps;

    getShaders(vsType: string) {
        const shaders = super.getShaders(vsType);
        // radar单环和多环参数难调 直接使用多个shader区分更方便
        const { radarType } = this.props;
        const shader = radarType === "once" ? radarOnceRing : radarMultiRing;
        return {
            ...shaders,
            ...shader,
        };
    }

    initializeState() {
        super.initializeState();
        const attributeManager = this.getAttributeManager();
        // add 和addInstanced 方法都是传入attribute 但是用途不同 uniforms 不会针对每个几何计算
        // add 渲染时每个顶点着色器的值都会改变
        // addInstanced 渲染时只会改变一次
        attributeManager!.add({
            // 自定义uv
            uvs: {
                size: 2,
                type: GL.FLOAT,
                accessor: "getUvs",
                fp64: this.use64bitPositions(),
            },
            pingSpeed: {
                size: 1,
                type: GL.FLOAT,
                accessor: "getPingSpeed",
                fp64: this.use64bitPositions(),
            },
            fillColor: {
                size: 4,
                type: GL.UNSIGNED_BYTE,
                normalized: true,
                transition: ATTRIBUTE_TRANSITION,
                accessor: "getFillColors",
                defaultValue: DEFAULT_COLOR,
                shaderAttributes: {
                    // 每个顶点自己的颜色
                    changefillColors: {
                        divisor: 0,
                    },
                    // 图层统一颜色
                    instanceFillColors: {
                        divisor: 1,
                    },
                },
                fp64: this.use64bitPositions(),
            },
        });
    }
    draw(params: any) {
        const { currentTime, data } = this.props;

        params.uniforms = {
            ...params.uniforms,
            currentTime,
        };

        super.draw(params);
    }
}
/**
 * 支持gps坐标和经纬度坐标
 */
// new TimeSolidPolygonLayer({
//     id: `vehicle-${key + index}-radar`,
//     data: getPolygonInfo(
//         [
//             {
//                 position: [0, 0],
//                 size: 20,
//                 rotation: 0,
//                 fillColor: [255, 0, 0, 255],
//                 pingSpeed: 0.2,
//             },
//         ],
//         "axis"
//     ),
//     filled: true,
//     getPolygon: (d) => d.polygon,
//     // solidPolygonLayer中未内置uv坐标 自行计算坐标传入
//     getUvs: (d) => d.uvs,
//     getFillColors: (d) => d.fillColor,
//     getPingSpeed: (d) => d.pingSpeed,
//     // radarType: type,
//     currentTime: time,
//     extruded: false,
//     coordinateSystem:
//         COORDINATE_SYSTEM.METER_OFFSETS,
//     coordinateOrigin: position,
// }),
