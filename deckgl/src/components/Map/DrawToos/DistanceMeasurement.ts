/*
 * @Author: jack <EMAIL>
 * @Date: 2023-04-12 15:57:30
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2023-05-09 10:28:06
 * @FilePath: /deckgl/src/components/Map/DrawToos/DistanceMeasurement.ts
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
// deckgl 测距模块

import {
    lineString,
    point,
    multiPoint,
    featureCollection,
    distance,
} from "@turf/turf";
import { GeoJsonLayer } from "deck.gl";

export default function DistanceMeasurement(points: number[][]) {
    if (points.length > 1) {
        const lines = lineString(points, { text: "line 1" });
        let cachedistanceVule = 0;
        let pointArr = points.map((item, index) => {
            const pointA = point(item);
            let distanceVule = 0;
            if (points[index - 1] != undefined) {
                const pointB = point(points[index - 1]);
                if (pointA && pointB) {
                    distanceVule = distance(pointA, pointB, {
                        units: "meters",
                    });
                }
            }
            distanceVule = distanceVule + cachedistanceVule;
            cachedistanceVule = distanceVule;
            pointA.properties.text = " " + distanceVule.toFixed(2);

            return pointA;
        });
        const geojson = featureCollection([lines, ...pointArr]);

        return new GeoJsonLayer({
            id: "drawlayer",
            data: geojson,
            pickable: false,
            getLineColor: [255, 165, 1],
            getLineWidth: 2,
            lineWidthUnits: "pixels",
            getText: (f) => (f.properties.text ? f.properties.text + "m" : ""),
            getTextColor: [0, 200, 200],
            pointType: "text",
            getTextSize: 20,
            parameters: {
                depthTest: false,
            },
            getTextAnchor: "start",
            textBackground: true,
            textBackgroundPadding: [5, 1],
            textOutlineColor: [200, 0, 0, 255],
            getTextBackgroundColor: [100, 200, 0, 100],
            getTextBorderColor: [0, 200, 200],
            getTextBorderWidth: 1,
        });
    } else {
        return [];
    }
}
