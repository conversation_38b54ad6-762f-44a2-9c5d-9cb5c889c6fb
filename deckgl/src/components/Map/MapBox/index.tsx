/*
 * @Author: luofei luofei.trunk.tech
 * @Date: 2022-11-04 16:26:46
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2025-01-10 10:47:50
 * @FilePath: /deckgl/src/components/Map/MapBox/index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { useState, useEffect, useMemo } from "react";
import Map, {
    Layer,
    FullscreenControl,
    NavigationControl,
    Source,
    Marker,
} from "react-map-gl/maplibre";
import { BASEMAP } from "@deck.gl/carto";

import { toLatLon as toLatLonForMap } from "@/utils/mapTools";
import "maplibre-gl/dist/maplibre-gl.css";

import "./index.less";

function toLatLon(position, z, UTMZone) {
    return toLatLonForMap(position, UTMZone || 51, "", z || 0);
}
const mapStyles = {
    id: "line",
    type: "line",
    paint: {
        "line-color": "green",
        "line-width": 3,
    },
};
function MapBox({ workData, theme }: { workData: any; theme: string }) {
    const { UTMZone, customData } = workData;

    const route = customData?.route;
    const geojsonData = useMemo(() => {
        if (!route) return {};
        const coordinates = route.map((position) =>
            toLatLon(position, 0, UTMZone || 51)
        );

        return {
            type: "FeatureCollection",
            features: [
                {
                    type: "Feature",
                    properties: {},
                    geometry: {
                        type: "LineString",
                        coordinates,
                    },
                },
            ],
        };
    }, [route, UTMZone]);

    const target = useMemo(() => {
        if (!geojsonData.features) return null;
        const coordinates = geojsonData.features[0].geometry.coordinates;
        return coordinates[coordinates.length - 1];
    }, [geojsonData]);

    const viewState = useMemo(() => {
        return {
            latitude: workData?.position?.[1] || 39.66807142300836,
            longitude: workData?.position?.[0] || 122.44967170553787,
            bearing: workData?.heading ? -workData.heading - 90 : 0,
        };
    }, [workData?.position, workData?.heading]);

    return (
        <Map
            reuseMaps
            mapStyle={theme == "dark" ? BASEMAP.DARK_MATTER : BASEMAP.POSITRON}
            attributionControl={false}
            latitude={viewState?.latitude}
            longitude={viewState?.longitude}
            bearing={viewState?.bearing}
            initialViewState={{
                ...viewState,
                zoom: 12,
            }}
        >
            <Source type="geojson" data={geojsonData}>
                <Layer {...mapStyles} />
            </Source>
            {target && (
                <Marker
                    longitude={target[0]}
                    latitude={target[1]}
                    color="red"
                    scale={1}
                />
            )}
            <FullscreenControl position="top-right" />
            <div id="radar">
                <div className="pointer"></div>
                <div className="shadow"></div>
                <div className="arrow"></div>
            </div>
        </Map>
    );
}

export default MapBox;
