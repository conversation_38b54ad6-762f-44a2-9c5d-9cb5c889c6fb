@size: 50px;
#radar {
    //     no-repeat 50% 50%;;
    width: @size;
    height: @size;
    background-size: @size @size;
    //  居中
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    z-index: 100;
    background: rgba(83, 149, 247, 0.5);
    // opacity: 1;
    border-radius: 100%;

    .pointer {
        position: absolute;
        z-index: 10;
        left: 10.5820106%;
        right: 10.5820106%;
        top: 10.5820106%;
        bottom: 50%;
        will-change: transform;
        transform-origin: 50% 100%;
        border-radius: 50% 50% 0 0 / 100% 100% 0 0;
        background-image: linear-gradient(
            135deg,
            rgba(5, 162, 185, 0.8) 0%,
            rgba(0, 0, 0, 0.02) 70%,
            rgba(0, 0, 0, 0) 100%
        );
        clip-path: polygon(
            100% 0,
            100% 10%,
            //控制扇形角度 100% => 135deg
            50% 100%,
            0 100%,
            0 0
        );

        animation: rotate360 3s infinite linear;
    }

    .radar .pointer:after {
        content: "";
        position: absolute;
        width: 50%;
        bottom: -1px;
        border-top: 2px solid rgba(0, 231, 244, 0.8);
        box-shadow: 0 0 3px rgba(0, 231, 244, 0.6);
        border-radius: 9px;
    }

    .shadow {
        position: absolute;
        left: 15%;
        top: 15%;
        right: 15%;
        bottom: 15%;
        margin: auto;
        border-radius: 9999px;
        box-shadow: 0 0 66px 6px #259bea;
        animation: shadow 1s infinite ease;
    }
    .arrow {
        position: absolute;
        width: (@size / 2);
        height: (@size / 2);
        left: (@size / 4);
        top: (@size / 4);
        background: url("../../../assets/img/up.svg") no-repeat;
        background-size: 100% 100%;
    }
}

@keyframes rotate360 {
    0% {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(-360deg);
    }
}

@keyframes shadow {
    0% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes shadowBorder {
    0% {
        border: 5px solid orange;
    }
    50% {
        border: 1px dashed green;
    }
    to {
        border: 5px solid orange;
    }
}
