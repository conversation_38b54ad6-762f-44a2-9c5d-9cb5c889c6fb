import DeckGL from "@deck.gl/react";
import { COORDINATE_SYSTEM } from "deck.gl";
import { PointCloudLayer } from "@deck.gl/layers";
import type { PickingInfo } from "deck.gl";

type DataType = {
    position: [x: number, y: number, z: number];
    normal: [nx: number, ny: number, nz: number];
    color: [r: number, g: number, b: number];
};

const MapTest = () => {
    const layer = new PointCloudLayer<DataType>();

    return (
        <DeckGL
            initialViewState={{
                longitude: -122.4,
                latitude: 37.74,
                zoom: 11,
            }}
            controller
            getTooltip={({ object }: PickingInfo<DataType>) =>
                object && object.position.join(", ")
            }
            layers={[layer]}
        />
    );
};

export default MapTest;
