/// app.js
import "./index.less";
import DeckGL, { MapViewState } from "deck.gl";
import React, {
    useState,
    useEffect,
    useMemo,
    useCallback,
    forwardRef,
    useImperativeHandle,
} from "react";
import { Map } from "react-map-gl/maplibre";
import { BASEMAP } from "@deck.gl/carto";
import { MAPBOX_ACCESS_TOKEN, getMapStyle } from "./config/MapBox";
// import { renderLayers } from "./config/RenderLayers";
import { center } from "@turf/turf";
import lightingEffect from "./config/LightEffect";
import {
    getViewStateOffset,
    getViewStates,
    getViews,
} from "./config/ViewPorts";
import { themeStyle } from "@/theme";
import { isMobile } from "@/utils/index";
import useEditableGeoJsonLayer from "@/hooks/useEditableGeoJsonLayer";

import CustomLayer from "./layers/customLayer";
import { creat_glbs_config } from "@/consconts";
import { VIEW_MODE } from "@/consconts";
import GUI from "lil-gui";

function calculateTrackedPosition(data: any) {
    if (data?.focus?.position) {
        const [longitude, latitude, altitude] = data.focus.position;
        const bearing = -data.focus.heading || 0;
        return { longitude, latitude, altitude: altitude || 0, bearing };
    } else {
        return null;
    }
}

// package.json 打印版本号
console.log("MapSets:", __APP_VERSION__);

const App = forwardRef(
    (
        {
            data,
            config,
            settings,
            truckDebugInfo = {
                visualization: {
                    // 3D模型
                    trunkModel: {
                        label: "3D模型",
                        value: true,
                    },
                    // 限速标志
                    speedLimit: {
                        label: "限速标志",
                        value: false,
                    },
                },
            },
            _onHoverFunc,
            _onclickFunc,
        }: {
            data: any;
            config: any;
            settings: any;
            truckDebugInfo: any;
            _onHoverFunc: any;
            _onclickFunc: any;
        },
        ref
    ) => {
        const { info } = data?.focus?.properties || {};
        const { autoDriverStatus, severityLevel } = info || {};
        const { mapConfig, theme } = config || {};
        const { osm, mapName, visualAngle, devicePixelRatio } = mapConfig || {};
        const [setting, setSetting] = useState({
            viewMode: visualAngle || "PERSPECTIVE", //2d:TOP_DOWN  3d:PERSPECTIVE
            lock: true, // 锁定视角
            measure: false, // 测量
            satelliteMap: false, // 卫星地图
            reset: false, // 重置
            debug: false, // 调试
            legend: false, // 图例
            console: false, // 控制台
            modelOpacity: true, // 模型透明
            transformModel: false, // 模型变换
            modelVisible: true, //3d模型是否展示
            ...settings,
        });
        const {
            viewMode: viewModeName,
            measure,
            satelliteMap,
            reset,
            lock,
            setMapView,
        } = setting || {};
        const viewMode = viewModeName?.name
            ? viewModeName
            : VIEW_MODE[viewModeName || "PERSPECTIVE"];
        const [viewState, setViewState] = useState<MapViewState>({
            latitude: 39.009329,
            longitude: 117.772736,
            maxPitch: 85,
            maxZoom: 25,
            minPitch: 0,
            minZoom: 0,
            pitch: 0,
            bearing: 0,
            zoom: 20,
            ...mapConfig,
        });

        const [viewOffset, setViewOffset] = useState({
            x: 0,
            y: 200,
            bearing: -90,
        });
        const [lockPosition, setLockPosition] = useState(null) as any;
        const [geoJsonData, setGeoJsonData] = useState(null);
        const [selectMode, setSelectMode] = useState("");
        const changeMapView = (viewState) => {
            // 改变地图 viewsState
            resetMap();
            setViewState((state) => {
                return {
                    ...state,
                    ...viewState,
                };
            });
        };
        // 鼠标样式
        const [cursor, setCursor] = useState("default");
        const getMapViewState = () => {
            return viewState;
        };
        const changeSetting = (s) => {
            setSetting((state) => {
                return {
                    ...state,
                    ...s,
                };
            });
        };
        ref &&
            useImperativeHandle(ref, () => ({
                changeMapView,
                changeSetting,
                getMapViewState,
                // 其他需要暴露给父组件的实例值或方法
            }));

        const [glbConfig, setGlbConfig] = useState(
            localStorage.glbConfig
                ? JSON.parse(localStorage.glbConfig)
                : creat_glbs_config()
        );

        useEffect(() => {
            // 添加双击事件
            const map = document.getElementById("map");
            return () => {};
        }, []);

        useEffect(() => {
            setViewState({
                ...viewState,
                ...viewMode?.initialViewState,
            });
        }, [viewMode]);
        useEffect(() => {
            resetMap();
        }, [reset]);
        useEffect(() => {
            if (trackedPosition && lock) {
                setLockPosition(trackedPosition);
            }
        }, [lock]);
        useEffect(() => {
            const fetchData = async () => {
                try {
                    const json = await fetchGeoJson(mapName);
                    // 修改 Oouter Inner 位置
                    json.features.forEach((item) => {
                        if (
                            item.properties.type === "Outer" ||
                            item.properties.type === "Inner"
                        ) {
                            let coordinates = item.geometry.coordinates;
                            coordinates = coordinates.map((coord) => {
                                return coord.map((c) => {
                                    if (c.length === 2) return [...c, -0.001];
                                    return c;
                                });
                            });
                            item.geometry.coordinates = coordinates; // 将修改后的 coordinates 赋值回原始对象
                        }
                    });
                    const { options } = json;

                    // 如果map 含有viewState 则使用map的viewState 否则使用geojson 中计算的中心点
                    if (options) {
                        setViewState((state: any) => ({
                            ...state,
                            latitude: options.center.y,
                            longitude: options.center.x,
                            bearing: options.bearing,
                            zoom: options.zoom,
                            pitch: options.pitch,
                        }));
                    } else if (setViewState) {
                        let mapCenter = center(json).geometry.coordinates;
                        setViewState((state: any) => ({
                            ...state,
                            ...viewState,
                            latitude: mapCenter[1],
                            longitude: mapCenter[0],
                            ...viewMode?.initialViewState,
                        }));
                    }
                    setGeoJsonData(json);
                } catch (e) {
                } finally {
                }
            };
            fetchData();
        }, [mapName]);
        useEffect(() => {
            if (!selectMode) return;
            const gui = new GUI();
            const gui_name = gui.addFolder(selectMode);
            for (let key in glbConfig[selectMode]) {
                const folder = gui_name.addFolder(key);
                for (let mkey in glbConfig[selectMode][key]) {
                    folder.add(glbConfig[selectMode][key], mkey).step(0.1);
                    folder.onChange((value) => {
                        setGlbConfig({
                            ...glbConfig,
                            [selectMode]: {
                                ...glbConfig[selectMode],
                                [key]: {
                                    ...glbConfig[selectMode][key],
                                    ...value.object,
                                },
                            },
                        });
                    });
                }
            }

            // gui 导出
            gui_name.add(
                {
                    save: () => {
                        const str = JSON.stringify(glbConfig);
                        localStorage.glbConfig = str;
                        const a = document.createElement("a");
                        a.href = URL.createObjectURL(
                            new Blob([str], { type: "text/plain" })
                        );
                        a.download = "glbConfig.json";
                        a.click();
                    },
                },
                "save"
            );

            return () => {
                gui.destroy();
            };
        }, [selectMode]);
        useEffect(() => {
            if (setMapView) {
                setViewState((state: any) => ({
                    ...state,
                    ...setMapView,
                }));
            }
        }, [setMapView]);

        const fetchGeoJson = (mapName: string) => {
            if (mapName.type) {
                return mapName;
            } else {
                let mapUrl = mapName.startsWith("http://")
                    ? mapName
                    : `/geojson/${mapName}.json`;
                return fetch(mapUrl)
                    .then((response) => {
                        if (!response.ok) {
                            throw new Error(
                                `HTTP error! status: ${response.status}`
                            );
                        }
                        return response.json();
                    })
                    .catch((error) => {
                        console.error("地图请求错误");
                        return null;
                    });
            }
        };
        const { drawLayer, features } = useEditableGeoJsonLayer(
            typeof setting.measure === "string" ? setting.measure : null
        );
        const layers = useMemo(() => {
            const result = [
                new CustomLayer({
                    data,
                    geoJsonData,
                    config,
                    truckDebugInfo,
                    glbConfig,
                    modelVisible: setting?.modelVisible,
                }),
            ];

            // 仅在 drawLayer 存在时才添加到图层
            if (drawLayer) {
                result.push(drawLayer);
            }

            return result;
        }, [
            JSON.stringify(data),
            geoJsonData,
            truckDebugInfo,
            config,
            drawLayer,
            setting.modelVisible,
        ]);
        // 在需要 trackedPosition 时调用 calculateTrackedPosition 函数
        const trackedPosition = calculateTrackedPosition(data);

        const viewStates = useMemo(() => {
            return viewState.width
                ? getViewStates({
                      viewState,
                      trackedPosition: lock ? trackedPosition : lockPosition,
                      viewMode,
                      offset: viewOffset,
                  })
                : viewState || {};
        }, [
            viewState,
            lock,
            trackedPosition,
            lockPosition,
            viewMode,
            viewOffset,
        ]);
        const _onLoad = () => {
            resetMap();
        };
        const _onViewStateChange = useCallback(
            ({
                viewState,
                oldViewState,
            }: {
                viewState: MapViewState;
                oldViewState: MapViewState;
            }) => {
                const offset = getViewStateOffset(
                    oldViewState,
                    viewState,
                    viewOffset
                );
                setViewOffset(offset);
                setViewState(viewState);
            },
            [viewOffset]
        );

        const _onClick = (e: { coordinate: never; object: { id: any } }, f) => {
            _onclickFunc && _onclickFunc(e, f);
            if (setting.transformModel && e?.sourceLayer?.props?.name) {
                setSelectMode(e?.sourceLayer?.id);
            } else {
                setSelectMode(null);
            }
        };
        const [hoverInfo, setHoverInfo] = useState();
        const _onHover = (info) => {
            if (info.object?.properties?.type == "Lock" || info.object?.id) {
                setCursor("pointer");
            } else {
                setCursor("default");
            }
            _onHoverFunc && _onHoverFunc(info, features);
        };
        const _getTooltip = ({ object }) => {
            if (object?.properties)
                return JSON.stringify(object.properties)
                    .split(",")
                    .join("\n")
                    .replace("{", "")
                    .replace("}", "")
                    .replace(/"/g, "");
        };

        const resetMap = useCallback(() => {
            setViewOffset({ x: 0, y: 200, bearing: -90 });
        }, []);
        return (
            <>
                <div
                    className={
                        autoDriverStatus == 0 && severityLevel > 2
                            ? "map-alarm"
                            : ""
                    }
                    onContextMenu={(evt) => evt.preventDefault()}
                    id="map"
                    style={{
                        position: "relative",
                        width: "100%",
                        height: "100%",
                        backgroundColor: themeStyle[theme]?.background,
                    }}
                >
                    <DeckGL
                        id={"map"}
                        views={getViews(viewMode)}
                        viewState={viewStates}
                        getCursor={({ isDragging, isHovering }) => {
                            if (setting.measure) {
                                return "crosshair";
                            } else if (isDragging) {
                                return "grabbing";
                            } else {
                                return cursor;
                            }
                        }}
                        _pickable={true}
                        _typedArrayManagerProps={
                            isMobile() ? { overAlloc: 1, poolSize: 0 } : {}
                        }
                        useDevicePixels={
                            devicePixelRatio || window.devicePixelRatio
                        }
                        effects={[lightingEffect]}
                        layers={layers}
                        onLoad={_onLoad}
                        onViewStateChange={_onViewStateChange as any}
                        onResize={() => {
                            resetMap();
                        }}
                        onClick={_onClick as any}
                        onHover={_onHover}
                    >
                        {osm && (
                            <Map
                                reuseMaps
                                mapStyle={
                                    theme == "dark"
                                        ? BASEMAP.DARK_MATTER
                                        : BASEMAP.POSITRON
                                }
                                attributionControl={false}
                                interactive={false}
                                maxZoom={viewState?.maxZoom}
                                maxPitch={viewState?.maxPitch}
                            />
                        )}
                    </DeckGL>
                    {/* {hoverInfo?.object && (
                        <div
                            style={{
                                position: "absolute",
                                zIndex: 1,
                                pointerEvents: "none",
                                left: hoverInfo.x,
                                top: hoverInfo.y,
                            }}
                        >
                            {JSON.stringify(hoverInfo?.object?.properties)}
                        </div>
                    )} */}
                </div>
            </>
        );
    }
);

export default React.memo(App);
