@import "@/theme/mixin.less";

.tool-box {
    // z-index: 9;
    width: 40px;
    position: absolute;
    right: 10px;
    top: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    .ant-btn {
        width: 30px;
        height: 30px;
        padding: 0;
        margin-bottom: 10px;
        background-color: @player-container-bg-color;
        border: 0;
        box-shadow: none;
        animation: none;
        display: flex;
        justify-content: center;
        align-items: center;
        i {
            color: @config-title-font-color;
            font-size: 14px;
        }
        .active {
            color: rgb(54, 104, 255);
        }
        &:not(:disabled):focus-visible {
            outline: none;
        }
        &:hover {
            background-color: @player-container-bg-color;
        }
        span {
            color: @config-title-font-color;
            font-weight: 600;
        }
    }

    // height: 100px;
    // .debug-btn {
    //     // position: fixabsoluted;
    //     // right: 10px;
    //     // top: 26%;

    //     background-color: #333;
    //     z-index: 1000;
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    //     border-radius: 4px;
    //     border: none !important;
    // }
    // .debug-btn i {
    //     font-size: 18px;
    //     line-height: 16px;
    //     font-weight: bolder;
    // }

    // .show-drawer-btn {
    //     width: 50px;
    //     border-radius: 4px;
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    //     border: none !important;
    //     i {
    //         font-size: 15px;
    //         line-height: 16px;
    //     }
    // }

    .ant-select .ant-select-selector {
        border-radius: 4px;
    }
}

.ant-select .ant-select-selection-item {
    font-size: 12px !important;
}
.visual-box {
    width: auto;
    padding: 0;
    margin: 0;
    background: @dropdown-bg-color;
    li {
        list-style: none;
        line-height: 28px;
        font-size: 14px;
        text-align: center;
        color: @dropdown-font-color;
        padding: 0 5px;
        border-radius: 5px;
        &:hover {
            cursor: pointer;
            color: @dropdown-font-color;
            background-color: @dropdown-select-hover-bg-color !important;
        }
    }
    .active {
        font-weight: bold;
        color: @dropdown-font-color !important;
        background-color: @dropdown-select-item-option-selected !important;
    }
}
