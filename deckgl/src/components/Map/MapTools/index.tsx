/*
 * @Author: jack <EMAIL>
 * @Date: 2023-08-08 10:31:03
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-23 16:26:09
 * @FilePath: \deckgl\src\components\Map\MapTools\index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { Button } from "antd";
import "./index.less";
import { memo, useEffect, useRef, forwardRef } from "react";
import Debug from "@/components/Debug";
import Description from "@/components/Description";
import Dotting from "@/components/Dotting";
import { setSettings, setConfig } from "@/features/dataSlice";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
const dynamicStyle = {
    true: {
        color: "green",
        des: "successAndPushing", //"连接成功且数据推送中",
    },
    pending: {
        color: "yellow",
        des: "successButNoData", // `连接成功但超过1ms未收到数据`,
    },
    false: {
        color: "red",
        des: "notConnected", //"暂未连接成功",
    },
};
const MapTools = forwardRef(({ config, state, settings }, ref) => {
    const { t } = useTranslation("map");
    const dispatch = useDispatch();
    const debugref = useRef<any>(null);
    const mapConfig = config?.mapConfig || {};
    const { osm } = mapConfig || {};
    const {
        viewMode,
        measure,
        satelliteMap,
        reset,
        lock,
        debug,
        legend,
        dotting,
    } = settings || {};
    const changeConfig = (key: string, value: any) => {
        let obj = {} as any;
        obj[key] = value;
        dispatch(
            setSettings({
                ...settings,
                ...obj,
            })
        );
        console.log(key, value);
        if (key == "viewMode") {
            dispatch(
                setConfig({
                    ...config,
                    mapConfig: {
                        ...mapConfig,
                        visualAngle: value,
                    },
                })
            );
        }
        // 测量
        if (key == "measure") {
            if (value) {
                dispatch(
                    setSettings({
                        ...settings,
                        measure: "line",
                    })
                );
            } else {
                dispatch(
                    setSettings({
                        ...settings,
                        measure: false,
                    })
                );
            }
        }
    };
    const keyDown = (e: any) => {};
    useEffect(() => {
        document.addEventListener("keydown", keyDown);
        return () => {
            document.removeEventListener("keydown", keyDown);
        };
    }, []);

    return (
        <div className="tool-box">
            {/* "视角模式" */}
            <Button
                type="primary"
                title={t("viewMode")}
                onClick={() => {
                    changeConfig(
                        "viewMode",
                        viewMode == "TOP_DOWN" ? "PERSPECTIVE" : "TOP_DOWN"
                    );
                }}
            >
                <i
                    className={`iconfont ${
                        viewMode == "TOP_DOWN" ? "icon-view_d" : "icon-view_d1"
                    }`}
                ></i>
            </Button>
            <Button
                type="primary"
                title={t("viewLock")}
                onClick={() => {
                    changeConfig("lock", !lock);
                }}
            >
                {/* 视角锁定 */}
                <i
                    className={`iconfont ${lock ? "icon-lock" : "icon-unlock"}`}
                ></i>
            </Button>
            <Button
                type="primary"
                title={t("reset")}
                onClick={() => {
                    changeConfig("reset", !reset);
                }}
            >
                {/* 复位 */}
                <i className={"iconfont icon-Icon_reset"}></i>
            </Button>
            <Button
                type="primary"
                title={t("measure")}
                onClick={() => {
                    const currentMeasure = settings.measure;
                    // 测量;
                    dispatch(
                        setSettings({
                            ...settings,
                            measure: currentMeasure ? null : "line",
                        })
                    );
                }}
            >
                <i
                    className={`iconfont ${
                        measure ? "icon-ranging_fill" : "icon-ranging_line"
                    } ${measure ? "active" : ""}`}
                ></i>
            </Button>
            <Button
                type="primary"
                title={t("mapMarker")}
                onClick={() => {
                    changeConfig("dotting", !dotting);
                }}
            >
                {/* 地图打点 */}
                <i
                    className={`iconfont icon-dadian ${
                        dotting ? "active" : ""
                    }`}
                ></i>
            </Button>
            {osm && (
                <Button
                    type="primary"
                    title={t("satelliteMap")}
                    onClick={() => {
                        if (osm) {
                            changeConfig("satelliteMap", !satelliteMap);
                        } else {
                            alert(t("enableMapFirst"));
                        }
                    }}
                >
                    {/* 卫星地图 */}
                    <i
                        // 点击状态
                        className={`iconfont icon-weixing ${
                            satelliteMap ? "active" : ""
                        }`}
                    ></i>
                </Button>
            )}
            <Button
                ref={debugref}
                className="debug-btn"
                type="primary"
                title={t("debug")}
                onClick={() => {
                    changeConfig("debug", !debug);
                }}
            >
                <i
                    className={`iconfont ${
                        debug ? "icon-debugstepover" : "icon-debug"
                    }`}
                ></i>
            </Button>
            <Button
                type="primary"
                title={t("mapLegend")}
                onClick={() => {
                    changeConfig("legend", !legend);
                }}
            >
                {/* 地图图示 */}
                <i
                    // 点击状态
                    className={`iconfont icon-tuli ${legend ? "active" : ""}`}
                ></i>
            </Button>
            <Button type="primary" title={t("connectionStatus")}>
                {/* 连接状态 */}
                <i
                    // 点击状态
                    className={`fc-${
                        dynamicStyle[state?.wsConnect]?.color
                    } iconfont icon-wifi `}
                    title={t(dynamicStyle[state?.wsConnect]?.des)}
                ></i>
            </Button>
            <Debug debugDisplay={debug} />
            <Description descriptopnChart={legend} />
            <Dotting isShow={dotting} ref={ref} />
        </div>
    );
});

export default memo(MapTools);
