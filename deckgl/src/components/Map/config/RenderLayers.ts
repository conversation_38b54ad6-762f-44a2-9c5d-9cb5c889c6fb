/*
 * @Author: luofei <EMAIL>
 * @Date: 2022-10-20 17:46:40
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2024-06-17 17:42:13
 * @FilePath: /deckgl/src/components/Map/config/RenderLayers.ts
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import {
    GeoJsonLayer,
    TextLayer,
    PointCloudLayer,
    ScenegraphLayer,
    PolygonLayer,
    PathLayer,
    IconLayer,
    TileLayer,
    ScatterplotLayer,
    BitmapLayer,
    COORDINATE_SYSTEM,
    log,
} from "deck.gl";
import { IPlayerData, Theme } from "../../../types";
import { PathStyleExtension } from "@deck.gl/extensions";
import { themeStyle, getIconfont } from "@/theme";
import { getColorArrayForRgbAndOpacity } from "@/utils/index";
import { center } from "@turf/turf";
import { returnNumber } from "@/utils/index";

export function renderLayers({
    data,
    theme,
    modelOpacity,
    egoModel,
    mapName,
    setViewState,
    truckDebugInfo,
    viewState,
    setLoading,
}: {
    data: IPlayerData;
    theme: Theme;
    modelOpacity: boolean;
    egoModel: string;
    mapName: string;
    setViewState: any;
    truckDebugInfo: any;
    viewState: any;
    setLoading: any;
}) {
    let baseLayer: any = [];
    let dataLayers: any = [];
    let mapUrl =
        mapName.indexOf("http://") > -1
            ? mapName
            : "geojson/" + mapName + ".json";
    const raduis = truckDebugInfo.distance.value.value;
    baseLayer = [
        mapName &&
            new GeoJsonLayer({
                id: "geojson-layer",
                data: mapUrl,
                pickable: false,
                stroked: false,
                filled: true,
                extruded: viewState.pitch == 0 ? false : true,
                wireframe: false,
                // pointType: "circle",
                lineWidthScale: 0.1,
                lineWidthMinPixels: 1,
                capRounded: true,
                parameters: {
                    depthTest: false,
                },
                getFillColor: (d) => {
                    if (d?.properties?.type || d?.properties?.type === 0) {
                        return (
                            themeStyle[theme].geoJson[d.properties.type] ||
                            themeStyle[theme].geoJson.default
                        );
                    }
                    return themeStyle[theme].geoJson.default;
                },
                getLineColor: themeStyle[theme].geoJson[0],
                getPointRadius: 0,
                getLineWidth: 1,
                getElevation: (d) => {
                    const type = d.properties?.type;

                    switch (type) {
                        case "Coastline":
                            return 1.5;
                        case "Building":
                            return Math.floor(Math.random() * 11) + 10;
                        case "Checkpoint":
                            return 10;
                        default:
                            return 0.01;
                    }
                },
                getDashArray: (d) => {
                    if (d.properties.type === 1) {
                        return [100, 100];
                    } else {
                        return [0, 0];
                    }
                },
                dashJustified: true,
                extensions: [
                    new PathStyleExtension({
                        dash: true,
                        highPrecisionDash: false,
                    }),
                ],
                onDataLoad: (e) => {
                    const mapCenter = center(e).geometry.coordinates;
                    if (setViewState) {
                        setViewState((state: any) => ({
                            ...state,
                            latitude: mapCenter[1],
                            longitude: mapCenter[0],
                        }));
                        setLoading(false);
                    }
                },
                onDataError: (e) => {
                    console.log(e);
                },
            }),
        new ScatterplotLayer({
            id: "scatterplot-layer11",
            data: [
                {
                    coordinates: [0, 0],
                    exits: 0.01,
                },
                {
                    coordinates: [0, 0],
                    exits: raduis * 1,
                },
                {
                    coordinates: [0, 0],
                    exits: raduis * 2,
                },
                {
                    coordinates: [0, 0],
                    exits: raduis * 3,
                },
            ],
            pickable: false,
            opacity: 0.8,
            stroked: true,
            filled: false,
            radiusScale: raduis ? 1 : 0,

            radiusUnits: "meters",
            lineWidthUnits: "meters",
            getLineWidth: 0.2,
            getPosition: (d) => d.coordinates,
            getRadius: (d) => d.exits,
            getLineColor: (d) => [255, 140, 10, 200],
            coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
            coordinateOrigin: data?.focus?.position || [0, 0],
        }),
    ];
    if (data) {
        const {
            points,
            marks,
            lines,
            boxs,
            glbs,
            circles,
            icons,
            coordinateOriginLatLon = [0, 0],
            customGeojson,
        } = data;
        console.log(data);
        dataLayers = [
            new GeoJsonLayer({
                id: "draw-geojson-layer",
                data: customGeojson,
                filled: true,
                getElevation: 0.01,
                // 点
                pointType: "icon+text",
                pointRadiusUnits: "meters",
                getText: (d: any) => d?.properties?.symbol?.textName,
                getTextSize: 12,
                textSizeMaxPixels: 12,
                getTextColor:
                    theme === "dark" ? [255, 255, 255, 255] : [0, 0, 0, 255],
                getTextAlignmentBaseline: "top",
                getTextPixelOffset: [0, -50],
                getIconPixelOffset: [0, -20],
                getIcon: (d) => {
                    const { marker } = d?.properties?.symbol;
                    return getIconfont(marker);
                },
                getIconSize: 30,
                iconSizeMaxPixels: 30,
                // 线
                getLineColor: (d) => {
                    if (d?.properties?.symbol?.polygonFill) {
                        const { polygonFill, polygonOpacity } =
                            d.properties.symbol;
                        return (
                            getColorArrayForRgbAndOpacity(
                                polygonFill,
                                polygonOpacity
                            ) || [135, 196, 240, 150]
                        );
                    }
                    return [135, 196, 240, 150];
                },
                getLineWidth: (d) => d?.properties?.symbol?.lineWidth || 0,
                // 面
                getFillColor: (d) => {
                    if (d?.properties?.symbol?.polygonFill) {
                        const { polygonFill, polygonOpacity } =
                            d.properties.symbol;
                        return (
                            getColorArrayForRgbAndOpacity(
                                polygonFill,
                                polygonOpacity
                            ) || [135, 196, 240, 150]
                        );
                    }
                    return [135, 196, 240, 150];
                },
            }),
            new ScatterplotLayer({
                id: "scatterplot-layer",
                data: circles,
                pickable: false,
                stroked: false,
                filled: true,
                getPosition: (d) => d.position,
                getRadius: (d) => d.radius || 1,
                sizeUnits: "pixels",
                getFillColor: (d) => d.color || themeStyle[theme].pointColor,
                opacity: 0.5,
                visible: circles.length > 0 ? true : false,
                // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                // coordinateOrigin: coordinateOriginLatLon,
            }),
            new TextLayer({
                id: "text-layer",
                data: marks,
                pickable: false,
                // 中文 此选项会产生性能开销，如果数据非常大，可能会导致该层需要更长的时间来加载。
                characterSet: "auto",
                getPosition: (d) => d.position,
                getText: (d) => d.text,
                getColor: (d) => d.color || themeStyle[theme].markColor,
                getSize: (d) => d.size || 12,
                getAngle: 0,
                getTextAnchor: "middle",
                fontWeight: "bold",
                getAlignmentBaseline: "bottom",
                visible: marks.length > 0 ? true : false,

                // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                // coordinateOrigin: coordinateOriginLatLon,
            }),
            new IconLayer({
                id: "icon-layer",
                data: icons,
                getIcon: (d) => d.icon,
                // icon size is based on data point's contributions, between 2 - 25
                getSize: (d) => d.size || 12,
                getPosition: (d) => d.position,
                sizeUnits: "meters",
                sizeMinPixels: 10, // 50,
                sizeMaxPixels: 60,
                visible: icons.length > 0 ? true : false,
                // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                // coordinateOrigin: coordinateOriginLatLon,
            }),
            new PathLayer({
                id: "path-layer",
                data: lines,
                pickable: false,
                widthScale: 1,
                widthMinPixels: 1,
                sizeUnits: "meters",
                getPath: (d) => d.path,
                getColor: (d) => d.color || themeStyle[theme].lineColor,
                getWidth: (d) => d.width || 0.5,
                getDashArray: (d) => {
                    if (d?.isDash) {
                        return [10, 10];
                    } else {
                        return [0, 0];
                    }
                },
                dashJustified: true,
                extensions: [
                    new PathStyleExtension({
                        dash: true,
                        highPrecisionDash: false,
                    }),
                ],
                opacity: 0.5,
                material: {
                    ambient: 1,
                    diffuse: 1,
                    shininess: 1000,
                    specularColor: [255, 255, 255, 80],
                },
                visible: lines.length > 0 ? true : false,
                // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                // coordinateOrigin: coordinateOriginLatLon,
            }),

            new PointCloudLayer({
                id: "point-cloud-layer",
                data: points,
                pickable: false,
                getPosition: (d) => d.position,
                getColor: (d) => d.color || themeStyle[theme].pointColor,
                pointSize: 2,
                sizeUnits: "pixels",
                material: {
                    ambient: 1,
                    diffuse: 1,
                },
                visible: points.length > 0 ? true : false,
                // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                // coordinateOrigin: coordinateOriginLatLon,
            }),
            // 增加bug展示信息的判断
            (!truckDebugInfo.display || truckDebugInfo.display) &&
                createGlbLayer(
                    glbs,
                    modelOpacity,
                    egoModel,
                    truckDebugInfo.visualization.trunkModel.value,
                    coordinateOriginLatLon
                ),
            new PolygonLayer({
                id: "polygon-layer",
                data: boxs,
                pickable: false,
                stroked: true,
                filled: true,
                wireframe: true,
                extruded: viewState.pitch == 0 ? false : true,
                getPolygon: (d) => d.box,
                getElevation: (d) => returnNumber(d?.height),
                getFillColor: (d) => d.color || themeStyle[theme].boxFillColor,
                opacity: 0.2,
                getLineColor: (d) =>
                    d.lineColor || themeStyle[theme].boxFillColor,
                getLineWidth: (d) => d.lineWidth || 0,
                // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                // coordinateOrigin: coordinateOriginLatLon,
                visible: boxs.length > 0 ? true : false,
            }),
        ];
    }
    // 数组合并
    return baseLayer.concat(dataLayers);
}
function createGlbLayer(
    data: any,
    modelOpacity: boolean,
    egoModel: string,
    visible: boolean = true,
    coordinateOriginLatLon: any
) {
    const layers = [];
    for (let name in data) {
        layers.push(
            new ScatterplotLayer({
                id: name + "scatterplot-layer",
                data: data[name],
                pickable: false,
                stroked: false,
                filled: true,
                getPosition: (d) => [...d.position, 0.01],
                getRadius: 0.08,
                sizeUnits: "pixels",
                getFillColor: [255, 140, 0, 200],
                opacity: 0.5,
                visible: data[name].length > 0 ? true : false,
                // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                // coordinateOrigin: coordinateOriginLatLon,
            }),
            new ScenegraphLayer({
                id: name + "-layer",
                data: data[name],
                pickable: true,
                scenegraph: "glb/" + name + ".glb",
                getPosition: (d) => d.position,
                getOrientation: (d) => [0, d.heading + 0.9, 90],
                getColor: (d) => d.color || [],
                getScale: name == "agv" ? [1, 1, 1] : [1.4, 1.4, 1.4],
                getTranslation: [0, 0, 0],
                sizeScale: 1,
                _lighting: "pbr",
                opacity: modelOpacity ? 1 : 0.2,
                visible: visible && data[name].length > 0 ? true : false,
                // coordinateSystem: COORDINATE_SYSTEM.METER_OFFSETS,
                // coordinateOrigin: coordinateOriginLatLon,
            })
        );
    }
    return layers;
}
