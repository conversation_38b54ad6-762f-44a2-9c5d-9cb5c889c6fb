/*
 * @Author: luofei luofei.trunk.tech
 * @Date: 2022-11-11 13:35:47
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2023-08-17 15:43:59
 * @FilePath: /deckgl/src/components/Map/config/MapBox.ts
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { AnyLayer } from "react-map-gl/dist/esm/types";

const MAPBOX_ACCESS_TOKEN =
    "pk.eyJ1IjoibHVvZmVpIiwiYSI6ImNrY3gwanpldzBkcTIycXF6d2FmYmF0bzYifQ.VecZ41O_HKtxU4JSQUXlyw";

const parkLayers: AnyLayer[] = [
    {
        id: "sky",
        type: "sky",
        paint: {
            "sky-type": "atmosphere",
            "sky-atmosphere-sun": [1, 1],
            "sky-atmosphere-sun-intensity": 1,
        },
    },
    {
        id: "add-3d-buildings",
        source: "composite",
        "source-layer": "building",
        filter: ["==", "extrude", "true"],
        type: "fill-extrusion",
        paint: {
            "fill-extrusion-color": "#ccc",

            // Use an 'interpolate' expression to
            // add a smooth transition effect to
            // the buildings as the user zooms in.
            "fill-extrusion-height": [
                "interpolate",
                ["linear"],
                ["zoom"],
                15,
                0,
                15.05,
                ["get", "height"],
            ],
            "fill-extrusion-base": [
                "interpolate",
                ["linear"],
                ["zoom"],
                15,
                0,
                15.05,
                ["get", "max_height"],
            ],
            "fill-extrusion-opacity": 0.6,
        },
    },
];

function getMapStyle(theme: string, satellite: boolean) {
    const style = {
        default:"mapbox://styles/luofei/cluw3gjjj001f01q14e376cm6",
        dark: "mapbox://styles/luofei/clje1bp74004001pl27xr1kez",
        light: "mapbox://styles/luofei/clje2y8z7003i01qu6ccr8khg",
    };
    // 卫星地图
    if (satellite) {
        return "mapbox://styles/luofei/clje4yf81004a01pa8dtr5y2g";
    } else {
        return style[theme];
    }
}
export { MAPBOX_ACCESS_TOKEN, getMapStyle, parkLayers };
