/*
 * @Author: luofei luofei.trunk.tech
 * @Date: 2022-11-07 14:13:36
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2024-11-25 16:59:11
 * @FilePath: /deckgl/src/components/Map/config/LightEffect.ts
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import {
    AmbientLight,
    PointLight,
    DirectionalLight,
    LightingEffect,
} from "deck.gl";
import {
    _SunLight as SunLight,
    _CameraLight as CameraLight,
} from "@deck.gl/core";

// create ambient light source
const ambientLight = new AmbientLight({
    color: [255, 255, 255],
    intensity: 1,
});
const cameraLight = new CameraLight({
    color: [0, 255, 0],
    intensity: 1,
});

// create directional light source
const directionalLight = new DirectionalLight({
    color: [255, 255, 255],
    intensity: 100,
    direction: [1, -8, -2.5],
    // _shadow: true,
});
const directionalLight1 = new DirectionalLight({
    color: [255, 255, 255],
    intensity: 1.0,
    direction: [-1, 3, -1],
});
const directionalLight2 = new DirectionalLight({
    color: [255, 255, 255],
    intensity: 1.0,
    direction: [1, -8, -2.5],
});
const directionalLight3 = new DirectionalLight({
    color: [255, 255, 255],
    intensity: 2.5,
    direction: [0, 0, 1],
});
const sunLight = new SunLight({
    timestamp: 1554927200000,
    color: [255, 255, 255],
    intensity: 1,
    _shadow: true,
});

// create point light source
const pointLight = new PointLight({
    color: [0, 0, 255],
    intensity: 10.0,
    // use coordinate system as the same as view state
    position: [117.772736, 39.009329, 10000],
});

// create lighting effect with light sources
const lightingEffect = new LightingEffect({
    ambient: new AmbientLight({
        color: [255, 255, 255],
        intensity: 2.0,
    }),
    dir1: new DirectionalLight({
        color: [255, 255, 255],
        intensity: 1.0,
        direction: [-1, -3, -1],
    }),
    dir2: new DirectionalLight({
        color: [255, 255, 255],
        intensity: 0.5,
        direction: [1, 8, -2.5],
    }),
});

export default lightingEffect;
