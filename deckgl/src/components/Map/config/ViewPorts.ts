// Copyright (c) 2019 Uber Technologies, Inc.
//

import { WebMercatorViewport } from "@math.gl/web-mercator";
import { MapView, FirstPersonView } from "@deck.gl/core";

export function getViewStateOffset(oldViewState, viewState, oldOffset) {
    if (!oldViewState) {
        return oldOffset;
    }

    const oldViewport = new WebMercatorViewport(oldViewState);
    const oldPos = [
        oldViewport.width / 2 + oldOffset.x,
        oldViewport.height / 2 + oldOffset.y,
    ];
    const trackedLngLat = oldViewport.unproject(oldPos);

    const newViewport = new WebMercatorViewport(viewState);
    const newPos = newViewport.project(trackedLngLat);

    return {
        x: oldOffset.x + newPos[0] - oldPos[0],
        y: oldOffset.y + newPos[1] - oldPos[1],
        bearing: oldOffset.bearing + viewState.bearing - oldViewState.bearing,
    };
}

// Adjust lng/lat to position the car 1/4 from screen bottom
function offsetViewState(viewState, offset) {
    const shiftedViewState = {
        ...viewState,
        bearing: viewState.bearing + offset.bearing,
    };
    const helperViewport = new WebMercatorViewport(shiftedViewState);

    const pos = [
        viewState.width / 2 + offset.x,
        viewState.height / 2 + offset.y,
    ];
    const lngLat = [viewState.longitude, viewState.latitude];

    const [longitude, latitude] = helperViewport.getMapCenterByLngLatPosition({
        lngLat,
        pos,
    });

    return {
        ...shiftedViewState,
        longitude,
        latitude,
    };
}

export function getViews(viewMode, options = {}) {
    const { name, orthographic, firstPerson, mapInteraction } = viewMode || {};

    const controllerProps = {
        ...mapInteraction,
        keyboard: false,
        touchRotate: true,
        doubleClickZoom: false,
    };
    if (firstPerson) {
        return new FirstPersonView({
            ...options,
            id: name,
            fovy: 75,
            near: 1,
            far: 10000,
            focalDistance: 6,
            controller: controllerProps,
        });
    }
    return new MapView({
        ...options,
        id: name,
        orthographic,
        controller: controllerProps,
    });
}

// Creates viewports that contains information about car position and heading
export function getViewStates({
    viewState,
    trackedPosition,
    viewMode,
    offset,
}) {
    const { name, firstPerson, tracked = {} } = viewMode || {};
    const viewStates = {};
    if (firstPerson) {
        if (trackedPosition) {
            const bearing = trackedPosition.bearing;
            viewState = {
                ...viewState,
                ...firstPerson,
                longitude: trackedPosition.longitude,
                latitude: trackedPosition.latitude,
                bearing: bearing + offset.bearing,
            };

            // Put the tracked object on the ground + 1.3 for vehicle height
            // TODO - support flying vehicle
            viewState.position = [0, 0, trackedPosition.altitude + 1.3];
        }

        viewStates[name] = viewState;
    } else {
        viewState = { ...viewState, transitionDuration: 0 };
        offset = { ...offset };
        // Track car position & heading
        if (tracked.position && trackedPosition) {
            viewState.longitude = trackedPosition.longitude;
            viewState.latitude = trackedPosition.latitude;
        } else {
            offset.x = 0;
            offset.y = 0;
        }
        if (tracked.heading && trackedPosition) {
            viewState.bearing = trackedPosition.bearing;
        } else {
            offset.bearing = 0;
        }
        // Put the tracked object on the ground
        // TODO - support flying vehicle
        if (trackedPosition) {
            viewState.position = [0, 0, trackedPosition.altitude];
        }

        viewStates[name] = offsetViewState(viewState, offset);
    }
    return viewStates;
}
