/*
 * @Author: jack <EMAIL>
 * @Date: 2023-08-10 15:56:08
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2024-11-07 14:48:41
 * @FilePath: /deckgl/src/components/Message/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import MyWorker from "@/worker?worker";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
    updateData,
    setConfig,
    setOperation,
    setCustomData,
} from "@/features/dataSlice";
import WSC from "@/data/wsController";
const worker = new MyWorker();

function MESSAGE() {
    const dispatch = useDispatch();
    const config = useSelector((state: any) => state.dataReducer.config);
    const operation = useSelector((state: any) => state.dataReducer.operation);
    const { connectConfig } = config;
    const truckDebugInfo = useSelector(
        (state: any) => state.dataReducer.truckDebugInfo
    );

    useEffect(() => {
        // 注册worker
        worker.onmessage = (event) => {
            const data = event.data;
            // 全局消息推送
            dispatch(updateData(data));
            // 向父应用发送消息
            if (data.toParent) {
                postMessageTOParent({
                    message: {
                        type: "data",
                        data: data.toParent,
                    },
                });
            }
        };

        // 接收父应用消息
        window.addEventListener("message", (e) => {
            const type: string = e.data.type;
            const data = e.data.message;

            switch (type) {
                case "data":
                    worker.postMessage(e.data);
                    break;
                case "config":
                    //避免父组件重新定义defaultPanel,上层应用直接展示mapsets最新所有组件
                    if (
                        data.layoutConfig &&
                        data.layoutConfig?.length !==
                            config.layoutConfig?.length
                    ) {
                        let layoutConfigNew = data.layoutConfig
                            .concat(config.layoutConfig)
                            .reduce((acc: any, current: any) => {
                                let existing = acc.find(
                                    (item: any) => item.name === current.name
                                );
                                if (!existing) {
                                    acc.push(current);
                                }
                                return acc;
                            }, []);
                        dispatch(
                            setConfig({
                                ...data,
                                layoutConfig: layoutConfigNew,
                            })
                        );
                    } else {
                        dispatch(setConfig(data));
                    }

                    break;
                case "operation":
                    dispatch(setOperation(data));
                    break;
                case "customRender":
                    worker.postMessage({
                        type: "customRender",
                        message: data,
                    });
                    break;
                case "customData":
                    dispatch(setCustomData(data));
                    break;
                default:
                    break;
            }
        });
        return () => {
            worker.terminate();
            window.removeEventListener("message", () => {});
        };
    }, []);
    useEffect(() => {
        worker.postMessage({
            type: "debug",
            message: truckDebugInfo,
        });
    }, [truckDebugInfo]);
    useEffect(() => {
        worker.postMessage({
            type: "config",
            message: config,
        });
        postMessageTOParent({
            message: {
                type: "config",
                data: config,
            },
        });
    }, [config]);
    useEffect(() => {
        worker.postMessage({
            type: "operation",
            message: operation,
        });
        postMessageTOParent({
            message: {
                type: "operation",
                data: operation,
            },
        });
    }, [operation]);

    const postMessageTOParent = ({
        message,
        targetOrigin = "*",
    }: {
        message: any;
        targetOrigin?: string;
    }) => {
        if (window == window.parent) return;
        window.parent.postMessage(message, targetOrigin);
    };

    return (
        <>
            <WSC
                {...{
                    ...connectConfig,
                    worker,
                }}
            ></WSC>
        </>
    );
}

export default MESSAGE;
