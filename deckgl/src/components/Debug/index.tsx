/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2023-02-01 19:25:52
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-23 18:14:36
 * @FilePath: \deckgl\src\components\Debug\index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { useState, useEffect, useRef } from "react";
import { Divider, InputNumber, Switch, Slider, Checkbox } from "antd";
import "./index.less";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { setTruckDebugInfo } from "@/features/dataSlice";
import Draggable from "react-draggable";
import { VehicleParam } from "@/utils/enum";
import { useTranslation } from "react-i18next";
import { useI18n } from "../../i18n/provider";
function Debug({ debugDisplay }: { debugDisplay: boolean }) {
    const dispatch = useDispatch();
    const { t } = useTranslation(["debug", "enum"]);
    const { currentLang } = useI18n();
    const config = useSelector((state: RootState) => state.dataReducer.config);
    const { mapConfig } = config;
    const truckDebugInfo = useSelector(
        (state: RootState) => state.dataReducer.truckDebugInfo
    );
    const [isShowHead, setIsShowHead] = useState(true);
    const [isShowTrailer, setIsShowTrailer] = useState(true);
    const [uploadFile, setUploadFile] = useState({
        name: "upload", //"上传",
        json: null,
    });
    const changeDebugContent = (
        value: number | boolean | string,
        belong: string,
        attribute?: string
    ) => {
        const cloneContent = JSON.parse(JSON.stringify(truckDebugInfo));
        if (attribute) {
            if (typeof value === "number") {
                cloneContent[belong][attribute].value =
                    value <= 0 ? 0.001 : value;
            } else {
                cloneContent[belong][attribute].value = value;
            }
        } else {
            cloneContent[belong] = value;
        }
        if (belong === "egoModel") {
            switch (value) {
                case "art":
                    cloneContent["truck"]["width"].value = 3.1;
                    cloneContent["truck"]["length"].value = 15.8;
                    break;
                case "agv":
                    cloneContent["truck"]["width"].value = 3;
                    cloneContent["truck"]["length"].value = 6;
                    break;
                case "bus":
                    cloneContent["truck"]["width"].value = 2.5;
                    cloneContent["truck"]["length"].value = 10.5;
                    break;
                default:
                    cloneContent["truck"]["width"].value = 2.5;
                    cloneContent["truck"]["length"].value = 6.9;
            }
        }
        dispatch(setTruckDebugInfo(cloneContent));
    };

    /**
     * @description: 是否展示车头/车挂
     * @param {*} type 1车头 2车挂
     * @return {*}
     */
    const handleChaneShowCar = (e: any, type: number) => {
        const cloneContent = JSON.parse(JSON.stringify(truckDebugInfo));
        if (type == 1) {
            setIsShowHead(e.target.checked);
            if (e.target.checked) {
                changeDebugContent(mapConfig.egoModel || "truck", "egoModel");
            } else {
                cloneContent["truck"]["width"].value = 0.001;
                cloneContent["truck"]["length"].value = 0.001;
                dispatch(setTruckDebugInfo(cloneContent));
            }
        } else {
            setIsShowTrailer(e.target.checked);
            if (e.target.checked) {
                cloneContent["trailer"]["width"].value =
                    VehicleParam.trailer_width;
                cloneContent["trailer"]["length"].value =
                    VehicleParam.trailer_length;
            } else {
                cloneContent["trailer"]["width"].value = 0.001;
                cloneContent["trailer"]["length"].value = 0.001;
            }
            dispatch(setTruckDebugInfo(cloneContent));
        }
    };
    const changeFile = (e: any) => {
        const file = e.target.files[0];
        // 获取文件名
        const fileName = file.name;

        // 转json
        if (!file) {
            alert(t("selectFile")); //"请选择文件"
            return;
        }
        const reader = new FileReader();
        reader.onload = function (event) {
            const content = event.target.result;
            try {
                const jsonData = JSON.parse(content);
                setUploadFile({
                    name: fileName,
                    json: jsonData,
                });
                e.target.value = "";
            } catch (error) {
                alert(t("invalidJson")); //"文件内容不是有效的JSON格式"
            }
        };
        reader.readAsText(file);
    };
    const cleanFile = () => {
        setUploadFile({
            name: "upload", //"上传",
            json: [],
        });
    };
    useEffect(() => {
        dispatch(
            setTruckDebugInfo({
                ...truckDebugInfo,
                pathJSON: uploadFile.json,
            })
        );
    }, [uploadFile]);
    useEffect(() => {
        changeDebugContent(debugDisplay, "display");
    }, [debugDisplay]);

    useEffect(() => {
        changeDebugContent(mapConfig.egoModel || "truck", "egoModel");
    }, [mapConfig.egoModel]);

    return (
        <Draggable handle={"strong"}>
            <div
                className={`debug-container ${currentLang}`}
                style={{
                    display: debugDisplay ? "block" : "none",
                }}
            >
                {/* 仅标题区域支持拖拽 */}
                <strong className="strong">
                    <div className="debug-title">
                        <span>{t("debug")}</span>
                        {/* 调试 */}
                    </div>
                </strong>
                <div className="adjust-attributes">
                    <div className="belong-row">
                        <strong className="strong">
                            <div className="belong-des">
                                <span className="title-border"></span>
                                <span> {t("visualization")}</span>
                                {/* 可视化 */}
                            </div>
                        </strong>
                        {Object.keys(truckDebugInfo.visualization).map(
                            (item) => {
                                return (
                                    <div className="attribute-row" key={item}>
                                        <div className="attribute-des">
                                            {t(
                                                `enum:${truckDebugInfo.visualization[item].label}`
                                            )}
                                        </div>
                                        <Switch
                                            checked={
                                                truckDebugInfo.visualization[
                                                    item
                                                ].value
                                            }
                                            onChange={(val) =>
                                                changeDebugContent(
                                                    val,
                                                    "visualization",
                                                    item
                                                )
                                            }
                                        ></Switch>
                                    </div>
                                );
                            }
                        )}
                    </div>
                    <div className="belong-row">
                        <div className="belong-des">
                            <span className="title-border"></span>
                            <span>{t("perception")}</span>
                            {/* 感知 */}
                        </div>
                        {Object.keys(truckDebugInfo.perception).map((item) => {
                            return (
                                <div className="attribute-row" key={item}>
                                    <div className="attribute-des">
                                        {t(
                                            `enum:${truckDebugInfo.perception[item].label}`
                                        )}
                                    </div>
                                    <Switch
                                        checked={
                                            truckDebugInfo.perception[item]
                                                .value
                                        }
                                        onChange={(val) =>
                                            changeDebugContent(
                                                val,
                                                "perception",
                                                item
                                            )
                                        }
                                    ></Switch>
                                </div>
                            );
                        })}
                    </div>
                    <div className="belong-row">
                        <div className="belong-des">
                            <span className="title-border"></span>
                            <span>{t("planning")}</span>
                            {/* 规控 */}
                        </div>
                        {Object.keys(truckDebugInfo.planning).map((item) => {
                            return (
                                <div className="attribute-row" key={item}>
                                    <div className="attribute-des">
                                        {t(
                                            `enum:${truckDebugInfo.planning[item].label}`
                                        )}
                                    </div>
                                    <Switch
                                        checked={
                                            truckDebugInfo.planning[item].value
                                        }
                                        onChange={(val) =>
                                            changeDebugContent(
                                                val,
                                                "planning",
                                                item
                                            )
                                        }
                                    ></Switch>
                                </div>
                            );
                        })}
                    </div>
                    <div className="belong-row">
                        <div className="belong-des">
                            <span className="title-border"></span>
                            <span>{t("simulation")}</span>
                            {/* 仿真 */}
                        </div>
                        {Object.keys(truckDebugInfo.simulation).map((item) => {
                            return (
                                <div className="attribute-row" key={item}>
                                    <div className="attribute-des">
                                        {t(
                                            `enum:${truckDebugInfo.simulation[item].label}`
                                        )}
                                    </div>
                                    <Switch
                                        checked={
                                            truckDebugInfo.simulation[item]
                                                .value
                                        }
                                        onChange={(val) =>
                                            changeDebugContent(
                                                val,
                                                "simulation",
                                                item
                                            )
                                        }
                                    ></Switch>
                                </div>
                            );
                        })}
                    </div>
                    <div className="belong-row">
                        <div className="belong-des">
                            <span className="title-border"></span>
                            <span>{t("generateTrackEnvelope")}</span>
                            {/* 生成轨迹包络 */}
                        </div>
                        <div className="belong-row">
                            <div className="upload-container">
                                <button className="upload-btn">
                                    {uploadFile.json == null
                                        ? t(uploadFile.name)
                                        : uploadFile.name}
                                    <input
                                        type="file"
                                        accept=".json"
                                        onChange={changeFile}
                                    />
                                </button>
                                <button
                                    className="clear-btn"
                                    onClick={cleanFile}
                                >
                                    {t("clear")}
                                </button>
                                {/* 清空 */}
                            </div>
                        </div>
                    </div>
                </div>
                <Divider />
                {/* <div className="debug-title">
                        <span>主车</span>
                    </div> */}
                <div className="adjust-attributes">
                    <div className="belong-row">
                        <div className="belong-des">
                            <span className="title-border"></span>
                            <span>{t("vehicleFront")}</span>
                            {/* 车头 */}
                            <span className="show">
                                <Checkbox
                                    checked={isShowHead}
                                    onChange={(e) => handleChaneShowCar(e, 1)}
                                ></Checkbox>
                            </span>
                        </div>
                        <div className="car-box">
                            {isShowHead ? (
                                <>
                                    {Object.keys(truckDebugInfo?.truck).map(
                                        (attribute, index) => {
                                            return (
                                                <div
                                                    className="attribute-row"
                                                    key={index}
                                                >
                                                    <div className="attribute-des">
                                                        {t(
                                                            `enum:${truckDebugInfo["truck"][attribute].label}`
                                                        )}
                                                        ：
                                                    </div>
                                                    <InputNumber
                                                        value={
                                                            truckDebugInfo[
                                                                "truck"
                                                            ][attribute].value
                                                        }
                                                        type="number"
                                                        min={0}
                                                        max={1000}
                                                        step={0.1}
                                                        onClick={(event) =>
                                                            event.stopPropagation()
                                                        }
                                                        onChange={(value) =>
                                                            changeDebugContent(
                                                                value,
                                                                "truck",
                                                                attribute
                                                            )
                                                        }
                                                    />
                                                </div>
                                            );
                                        }
                                    )}
                                </>
                            ) : (
                                ""
                            )}
                        </div>
                    </div>
                    {truckDebugInfo.egoModel !== "art" && (
                        <div className="belong-row">
                            <div className="belong-des">
                                <span className="title-border"></span>
                                <span>{t("trailer")}</span>
                                {/* 车挂 */}
                                <span className="show">
                                    <Checkbox
                                        checked={isShowTrailer}
                                        onChange={(e) =>
                                            handleChaneShowCar(e, 2)
                                        }
                                    ></Checkbox>
                                </span>
                            </div>
                            <div className="car-box">
                                {isShowTrailer ? (
                                    <>
                                        {Object.keys(
                                            truckDebugInfo?.trailer
                                        ).map((attribute, index) => {
                                            return (
                                                <div
                                                    className="attribute-row"
                                                    key={index}
                                                >
                                                    <div className="attribute-des">
                                                        {t(
                                                            `enum:${truckDebugInfo["trailer"][attribute].label}`
                                                        )}
                                                        ：
                                                    </div>
                                                    <InputNumber
                                                        value={
                                                            truckDebugInfo[
                                                                "trailer"
                                                            ][attribute].value
                                                        }
                                                        type="number"
                                                        min={0}
                                                        max={
                                                            truckDebugInfo[
                                                                "trailer"
                                                            ][attribute].max
                                                        }
                                                        step={0.1}
                                                        onClick={(event) =>
                                                            event.stopPropagation()
                                                        }
                                                        onChange={(value) =>
                                                            changeDebugContent(
                                                                value,
                                                                "trailer",
                                                                attribute
                                                            )
                                                        }
                                                    />
                                                </div>
                                            );
                                        })}{" "}
                                    </>
                                ) : (
                                    ""
                                )}
                            </div>
                        </div>
                    )}
                </div>
                <Divider />
                <div className="adjust-attributes">
                    <div className="belong-row">
                        <div className="belong-des">
                            <span className="title-border"></span>
                            <span>{t("dynamicRanging")}</span>
                            {/* 动态测距 */}
                        </div>

                        <div className="distance-slider">
                            <Slider
                                marks={truckDebugInfo.distance.value.mask}
                                defaultValue={
                                    truckDebugInfo.distance.value.default
                                }
                                onChange={(value) =>
                                    changeDebugContent(
                                        value,
                                        "distance",
                                        "value"
                                    )
                                }
                            />
                        </div>
                    </div>
                </div>
            </div>
        </Draggable>
    );
}

export default Debug;
