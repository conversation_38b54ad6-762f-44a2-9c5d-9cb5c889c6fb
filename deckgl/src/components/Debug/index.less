@import "@/theme/mixin.less";

.debug-container {
    position: absolute;
    // z-index: 1001;
    top: 7%;
    right: 100px;
    transform: translate(-50%, -50%);
    width: 300px;
    box-sizing: border-box;
    background-color: @config-bg-color;
    border-radius: 4px;
    padding: 4px;
    color: @panel-font-color;
    // position: relative;
    .debug-title {
        font-weight: 500;
        // position: relative;
        margin-bottom: 10px;
        padding-left: 5px;
    }
    .strong {
        width: 100%;
        &:hover {
            cursor: pointer;
        }
    }
    .icon-close {
        position: absolute;
        right: 10px;
        top: 5px;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;
    }
    .adjust-attributes {
        width: 100%;
        .belong-row {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            // font-size: 16px;
            .belong-des {
                height: 18px;
                line-height: 18px;
                font-size: 14px;
                font-weight: 500;
                width: 100%;
                margin: 0 auto;
                .title-border {
                    width: 0;
                    height: 0.729167vw;
                    border-right: 0.104167vw solid #0056ff;
                    margin-right: 0.416667vw;
                    padding-left: 5px;
                }
                .show {
                    margin-left: 10px;
                }
            }
            .distance-slider {
                margin: 5px 10px 0 10px;
                width: 100%;
                span {
                    color: @config-title-font-color;
                    font-size: 12px;
                }
            }
            .car-box {
                width: 100%;
                display: flex;
                height: 34px;
            }
            .attribute-row {
                display: flex;
                justify-content: space-around;
                margin: 5px 0;
                font-size: 12px;
                width: 50%;
                align-items: center;

                .attribute-des {
                    width: 45%;
                    text-align: right;
                }
                .ant-input-number {
                    width: 50%;
                    height: 24px;
                    // height: 20px;
                    line-height: 24px;
                    margin-right: 5%;
                    border-radius: 2px;
                    border: 1px solid @config-input-bd-color;
                    background: @config-input-bg-color;
                    font-size: 12px;
                    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
                    font-weight: 400;
                    input {
                        height: 24px;
                        color: @config-title-font-color;
                    }
                    .ant-input-number-handler {
                        background-color: @config-input-bg-color;
                        border-radius: 5px;
                        span {
                            color: @config-title-font-color;
                        }
                    }
                }
            }
            .upload-container {
                display: flex;
                justify-content: space-around;
                font-size: 0.625vw;
                width: 100%;
                padding: 10px 0 10px 10px;
                line-height: 10px;
                .upload-btn {
                    width: 70%;
                    height: 30px;
                    overflow: hidden;
                    position: relative;
                    background-color: #1677ff;
                    color: var(--carInfo-font-color) !important;

                    input {
                        width: 100%;
                        height: 100%;
                        opacity: 0;
                        position: absolute;
                        top: 0;
                        left: 0;
                    }
                }

                .clear-btn {
                    width: 25%;
                    height: 30px;
                    float: right;
                    background-color: #1677ff;
                    color: var(--carInfo-font-color) !important;
                }
            }
        }
    }
    .ant-divider {
        margin: 5px 0;
    }
}
.ant-input-number-outlined .ant-input-number-handler-wrap {
    background: transparent;
}

.debug-container.en-US {
    width: 400px;
    .attribute-des {
        width: 70% !important;
    }
}
