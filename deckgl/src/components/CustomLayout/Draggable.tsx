/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-09 11:02:01
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2022-11-30 18:47:51
 * @FilePath: /deckgl/src/components/CustomLayout/Draggable.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { useCallback } from "react";
import LineChart from "../InfoPanel/Charts/LineChart";

export interface Component {
    id: number;
    name: string;
}

export interface Props {
    component: Component;
    handleDragStart: (component: Component) => void;
}

function Draggable({ component, handleDragStart }: Props) {
    const onDragStart = useCallback(
        () => handleDragStart(component),
        [component, handleDragStart]
    );

    return (
        <div className="card" draggable onDragStart={onDragStart}>
            <LineChart options={component} />
        </div>
    );
}

export default Draggable;
