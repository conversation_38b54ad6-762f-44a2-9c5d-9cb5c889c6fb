.row {
    display: flex;
    justify-content: center;
    flex-direction: row;
    width: 100%;
    height: 100%;
    .left {
        width: 20%;
        margin: 20px;
        .title {
            font-size: 30px;
            line-height: 50px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }
        .col {
            display: flex;
            flex-direction: column;
            width: 80%;
            height: 90%;
            margin: auto;
            .card {
                flex: 1;
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                min-width: 0;
                word-wrap: break-word;
                background-color: #fff;
                background-clip: border-box;
                border: 1px solid rgba(0, 0, 0, 0.125);
                border-radius: 0.25rem;
                margin: 5px;
            }
        }
    }
    .right {
        flex: 1;
        margin: 20px;
        .top {
            position: relative;
            .title {
                font-size: 30px;
                line-height: 50px;
                text-align: center;
                border-bottom: 1px solid #dee2e6;
            }
            .btn {
                position: absolute;
                right: 20px;
                top: 10px;
                span {
                    color: #fff;
                }
            }
        }
        .bot {
            border: 1px solid #dee2e6;
            //  min-height: 90%;
            .card {
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                min-width: 0;
                word-wrap: break-word;
                background-color: #fff;
                background-clip: border-box;
                border: 1px solid rgba(0, 0, 0, 0.125);
                border-radius: 0.25rem;
                height: 200px;
                .remove {
                    position: absolute;
                    top: 0;
                    right: 5px;
                }
            }
        }
    }
}
