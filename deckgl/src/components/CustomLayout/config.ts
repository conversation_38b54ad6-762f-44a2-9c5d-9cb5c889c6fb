export const components = [
    {
        id: 1,
        name: "<PERSON>",
    },
    {
        id: 2,
        name: "<PERSON><PERSON>",
    },
    {
        id: 3,
        name: "<PERSON><PERSON>",
    },
    {
        id: 4,
        name: "<PERSON><PERSON>",
    },
    {
        id: 5,
        name: "<PERSON><PERSON><PERSON>",
    },
    {
        id: 6,
        name: "Nav",
    },
];
export const views = [
    {
        id: "1",
        name: "Example Layout Name",
        componentLayout: [
            {
                w: 24,
                h: 3,
                x: 0,
                y: 0,
                i: "1623972414843",
                component: {
                    id: 3,
                    name: "<PERSON><PERSON>",
                },
            },
            {
                w: 24,
                h: 3,
                x: 0,
                y: 27,
                i: "1623972423051",
                component: {
                    id: 4,
                    name: "<PERSON><PERSON>",
                },
            },
            {
                w: 5,
                h: 3,
                x: 0,
                y: 3,
                i: "1623972442979",
                component: {
                    id: 4,
                    name: "Nav",
                },
            },
            {
                w: 5,
                h: 3,
                x: 5,
                y: 3,
                i: "1623972449971",
                component: {
                    id: 4,
                    name: "Nav",
                },
            },
            {
                w: 24,
                h: 21,
                x: 0,
                y: 6,
                i: "1623972458835",
                component: {
                    id: 1,
                    name: "<PERSON>",
                },
            },
        ],
    },
    {
        id: "2",
        name: "Layout 2",
        componentLayout: [
            {
                i: "d",
                x: 5,
                y: 9,
                w: 19,
                h: 16,
                component: {
                    id: 1,
                    children: [],
                    name: "Card",
                },
            },
            {
                i: "e",
                x: 9,
                y: 5,
                w: 5,
                h: 4,
                component: {
                    id: 2,
                    children: [],
                    name: "Button",
                },
            },
            {
                i: "a",
                x: 5,
                y: 5,
                w: 4,
                h: 4,
                component: {
                    id: 2,
                    children: [],
                    name: "Button",
                },
            },
            {
                i: "b",
                x: 19,
                y: 5,
                w: 5,
                h: 4,
                component: {
                    id: 2,
                    children: [],
                    name: "Button",
                },
            },
            {
                w: 5,
                h: 4,
                x: 14,
                y: 5,
                i: "1623972148155",
                component: {
                    id: 2,
                    name: "Button",
                },
            },
            {
                w: 24,
                h: 5,
                x: 0,
                y: 25,
                i: "1623972149715",
                component: {
                    id: 4,
                    name: "Footer",
                },
            },
            {
                w: 24,
                h: 5,
                x: 0,
                y: 0,
                i: "1623972168867",
                component: {
                    id: 3,
                    name: "Header",
                },
            },
            {
                w: 5,
                h: 20,
                x: 0,
                y: 5,
                i: "1623972213747",
                component: {
                    id: 4,
                    name: "SideBar",
                },
            },
        ],
    },
    {
        id: "3",
        name: "Another Layout Example",
        componentLayout: [
            {
                i: "d",
                x: 5,
                y: 9,
                w: 19,
                h: 16,
                component: {
                    id: 1,
                    children: [],
                    name: "Card",
                },
            },
            {
                w: 24,
                h: 5,
                x: 0,
                y: 25,
                i: "1623972149715",
                component: {
                    id: 4,
                    name: "Footer",
                },
            },
            {
                w: 24,
                h: 5,
                x: 0,
                y: 0,
                i: "1623972168867",
                component: {
                    id: 3,
                    name: "Header",
                },
            },
            {
                w: 5,
                h: 20,
                x: 0,
                y: 5,
                i: "1623972213747",
                component: {
                    id: 4,
                    name: "SideBar",
                },
            },
            {
                w: 19,
                h: 4,
                x: 5,
                y: 5,
                i: "1623973905504",
                component: {
                    id: 4,
                    name: "Nav",
                },
            },
        ],
    },
    {
        id: "4",
        name: "Layout For Shopping Cart Page",
        componentLayout: [
            {
                w: 24,
                h: 3,
                x: 0,
                y: 0,
                i: "1623972414843",
                component: {
                    id: 3,
                    name: "Header",
                },
            },
            {
                w: 5,
                h: 3,
                x: 0,
                y: 3,
                i: "1623972442979",
                component: {
                    id: 4,
                    name: "Nav",
                },
            },
            {
                w: 5,
                h: 3,
                x: 5,
                y: 3,
                i: "1623972449971",
                component: {
                    id: 4,
                    name: "Nav",
                },
            },
            {
                w: 24,
                h: 21,
                x: 0,
                y: 6,
                i: "1623972458835",
                component: {
                    id: 1,
                    name: "Card",
                },
            },
        ],
    },
    {
        id: "5",
        name: "Just Example",

        componentLayout: [
            {
                i: "d",
                x: 5,
                y: 9,
                w: 19,
                h: 16,
                component: {
                    id: 1,
                    children: [],
                    name: "Card",
                },
            },
            {
                w: 24,
                h: 5,
                x: 0,
                y: 25,
                i: "1623972149715",
                component: {
                    id: 4,
                    name: "Footer",
                },
            },
            {
                w: 24,
                h: 5,
                x: 0,
                y: 0,
                i: "1623972168867",
                component: {
                    id: 3,
                    name: "Header",
                },
            },
            {
                w: 5,
                h: 20,
                x: 0,
                y: 5,
                i: "1623972213747",
                component: {
                    id: 4,
                    name: "SideBar",
                },
            },
            {
                w: 19,
                h: 4,
                x: 5,
                y: 5,
                i: "1623973905504",
                component: {
                    id: 4,
                    name: "Button",
                },
            },
        ],
    },
    {
        id: "6",
        name: "Layout 6",
        componentLayout: [
            {
                i: "d",
                x: 5,
                y: 9,
                w: 19,
                h: 16,
                component: {
                    id: 1,
                    children: [],
                    name: "Card",
                },
            },
            {
                w: 24,
                h: 5,
                x: 0,
                y: 25,
                i: "1623972149715",
                component: {
                    id: 4,
                    name: "Footer",
                },
            },
            {
                w: 24,
                h: 5,
                x: 0,
                y: 0,
                i: "1623972168867",
                component: {
                    id: 3,
                    name: "Header",
                },
            },
            {
                w: 5,
                h: 20,
                x: 0,
                y: 5,
                i: "1623972213747",
                component: {
                    id: 4,
                    name: "SideBar",
                },
            },
        ],
    },
];

export const viewInfo = {
    id: "2",
    name: "Layout 2",
    componentLayout: [
        {
            i: "d",
            x: 0,
            y: 0,
            w: 5,
            h: 12,
            component: {
                name: "折线图",
                id: "speed",
                title: "车速",
                unit: "m/s",
                nowList: [],
                planList: [],
                rendering: false,
                num: 0,
                color: "rgba(147, 52, 255, 1)",
            },
        },
    ],
};
