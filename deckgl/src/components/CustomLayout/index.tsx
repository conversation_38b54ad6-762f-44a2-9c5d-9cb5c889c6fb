import { find as _find } from "lodash";
import { useCallback, useState } from "react";
import { Layout, Responsive, WidthProvider } from "react-grid-layout";
import Draggable from "./Draggable";
import LayoutItem from "./Item";
import { views, viewInfo, components } from "./config";
import { Component, ComponentLayout } from "@/utils/types";
import "./index.less";
import { Button } from "antd";
import { chartList } from "../InfoPanel/Charts/config";

const ResponsiveReactGridLayout = WidthProvider(Responsive);

const droppingItem = {
    i: "__dropping-elem__",
    w: 5,
    h: 12,
};

const CustomLayout = () => {
    const view = viewInfo;
    const draggableComponents = components;
    const [layout, setlayout] = useState<ComponentLayout[]>(
        view ? view.componentLayout : []
    );
    const [component, setComponent] = useState<Component>();
    const mapLayout = useCallback(
        (currentLayout: ComponentLayout[], newLayout: Layout[]) => {
            return currentLayout.map((componentLayout) => {
                const found = _find(newLayout, ["i", componentLayout.i]);
                return { ...componentLayout, ...found };
            });
        },
        []
    );
    const handleDragStart = useCallback((dragComponent: Component) => {
        setComponent(dragComponent);
    }, []);
    const onDrop = useCallback(
        (layouts: Layout[], item: Layout, e: any) => {
            setlayout((prev) =>
                mapLayout(prev, layouts).concat({
                    ...item,
                    i: new Date().getTime().toString(),
                    component,
                    isDraggable: undefined,
                })
            );
        },
        [component, mapLayout]
    );
    const onRemoveItem = useCallback((i: string) => {
        setlayout((prev) => prev.filter((l) => l.i !== i));
    }, []);
    const onLayoutChange = useCallback(
        (layouts: Layout[]) => {
            setlayout((prev) => mapLayout(prev, layouts));
        },
        [mapLayout]
    );
    const handleSave = useCallback(() => {
        let newLayout = { ...view, componentLayout: layout };
        view.componentLayout = newLayout;
    }, [view, layout]);

    return (
        <div className="row">
            <div className="left">
                <h4 className="title">组件列表</h4>
                <div className="col">
                    {chartList.map((component) => (
                        <Draggable
                            key={component.id}
                            component={component}
                            handleDragStart={handleDragStart}
                        />
                    ))}
                </div>
            </div>
            <div className="right">
                <div className="top">
                    <h4 className="title">自选布局</h4>
                    <Button className="btn" onClick={handleSave} type="primary">
                        Save
                    </Button>
                </div>
                <div className="bot">
                    <ResponsiveReactGridLayout
                        className="layout border p-0"
                        cols={{ lg: 24 }}
                        layouts={{ lg: layout }}
                        breakpoints={{ lg: 0 }}
                        rowHeight={7}
                        compactType={null}
                        isDroppable
                        onDrop={onDrop}
                        onLayoutChange={onLayoutChange}
                        droppingItem={droppingItem}
                    >
                        {layout.map((componentLayout, index) => (
                            <div key={componentLayout.i} className="card ">
                                <span
                                    role="button"
                                    className="remove"
                                    onClick={() =>
                                        onRemoveItem(componentLayout.i)
                                    }
                                >
                                    x
                                </span>
                                <LayoutItem componentLayout={componentLayout} />
                            </div>
                        ))}
                    </ResponsiveReactGridLayout>
                </div>
            </div>
        </div>
    );
};

export default CustomLayout;
