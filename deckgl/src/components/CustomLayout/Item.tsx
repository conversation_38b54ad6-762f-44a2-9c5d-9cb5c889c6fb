/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-11-09 11:13:18
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2022-11-30 18:47:24
 * @FilePath: /deckgl/src/components/CustomLayout/Item.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { ComponentLayout } from "@/utils/types";
import LineChart from "../InfoPanel/Charts/LineChart";

interface Props {
    componentLayout: ComponentLayout;
}

function LayoutItem({ componentLayout }: Props) {
    return (
        <LineChart
            options={
                componentLayout.component
                    ? componentLayout.component
                    : componentLayout.i
            }
        />
    );
}

export default LayoutItem;
