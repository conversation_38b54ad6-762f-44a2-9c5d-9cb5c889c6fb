/*
 * @Author: jack <EMAIL>
 * @Date: 2023-05-30 14:34:53
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2023-09-01 17:30:10
 * @FilePath: /deckgl/src/components/Loading/index.tsx
 * @Description: 数据console 监听
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import "./index.less";
function Loading() {
    return (
        <div className="loading-box">
            <div className="box">
                <div>K</div>
                <div>N</div>
                <div>U</div>
                <div>R</div>
                <div>T</div>
            </div>
        </div>
    );
}
export default Loading;
