@import "@/theme/mixin.less";
.config-page-main {
    padding: 0 16px;

    .class-item {
        padding: 20px 0 10px;
        border-bottom: 1px solid @player-container-bd-color;
        .class-title {
            width: 100%;
            height: 14px;
            display: flex;
            margin-bottom: 10px;
            .title-border {
                width: 0;
                height: 14px;
                border-right: 3px solid #0056ff;
                margin-right: 8px;
            }
            .title-text {
                height: 14px;
                font-size: 14px;
                font-family: "SourceHanSansCNMedium";
                font-weight: 500;
                color: @config-title-font-color;
                line-height: 1;
            }
            .anticon-download {
                color: #0056ff;
                margin-left: auto;
                padding: 0 5px;
                cursor: pointer;
            }
        }
        .class-content {
            .group-item {
                display: flex;
                justify-content: space-between; //space-between;
                margin-bottom: 10px;
                align-items: center;
                .group-title {
                    width: 25%;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    font-size: 14px;
                    font-family: "SourceHanSansCNMedium";
                    font-weight: 400;
                    color: @config-title-font-color;
                }
                .group-content {
                    width: 75%;
                    flex: 1;
                    margin: 0 5px;
                    .ant-form .ant-form-item {
                        margin-bottom: 0;
                        .ant-form-item-control-input {
                            min-height: 0 !important;
                            .ant-form-item-control-input-content {
                                height: 24px;
                            }
                        }
                        .ant-form-item-explain-error {
                            height: 10px;
                            font-size: 12px;
                        }
                    }
                    .ant-input {
                        height: 24px;
                        border-radius: 2px;
                        border: 1px solid @config-input-bd-color;
                        background: @config-input-bg-color;
                        font-size: 12px;
                        color: @config-title-font-color;
                        font-family: SourceHanSansCN-Normal, SourceHanSansCN;
                        font-weight: 400;
                        display: flex;
                        align-items: center;
                    }
                    .ant-select {
                        width: 100%;
                        height: 24px;

                        .ant-select-selector {
                            width: 100%;
                            height: 24px;
                            // box-sizing: border-box;
                            border-radius: 2px;
                            border: 1px solid @config-input-bd-color;
                            background: @config-input-bg-color;
                            .ant-select-selection-item {
                                height: 22px;
                                line-height: 22px;
                                font-size: 14px;
                                font-family: SourceHanSansCN-Normal,
                                    SourceHanSansCN;
                                font-weight: 400;
                                color: @config-title-font-color;
                            }
                        }
                    }
                }
                // .full {
                //     width: 288px !important;
                //     margin-left: 0 !important;

                //     .topic-title {
                //         text-align: center;
                //         background-color: @topic-title-background-color;
                //         border-radius: 10px 10px 0 0;
                //     }
                //     .topic-title,
                //     .topic-item {
                //         height: 40px;
                //         line-height: 40px;
                //         border-bottom: 1px solid @config-input-bd-color;
                //         padding: 0 10px;
                //         overflow: hidden;
                //     }
                //     .topic-list {
                //         max-height: 250px;
                //         overflow-y: auto;
                //         color: @dropdown-font-color;

                //         .topic-item {
                //             overflow: hidden;
                //             white-space: nowrap;
                //             text-overflow: ellipsis;
                //             background-color: @dropdown-bg-color;
                //         }
                //     }
                // }
                .ant-radio-group {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    .ant-radio-button-wrapper {
                        width: 140px;
                        height: 93px;
                        padding: 0;
                        border: 0;
                        background-color: rgba(0, 0, 0, 0);

                        .ant-radio-button {
                            display: none;
                            border-radius: 4px;
                        }
                        span {
                            width: 140px;
                            height: 93px;
                            display: block;
                            background-color: rgba(0, 0, 0, 0);
                            position: relative;
                        }
                        .radio-png {
                            width: 140px;
                            height: 93px;
                            border-radius: 4px;
                            // border: 1px solid #ccc;
                        }
                        .select-png {
                            position: absolute;
                            right: 10px;
                            top: 10px;
                            height: 20px;
                            width: 20px;
                            display: none;
                        }
                    }
                    .ant-radio-button-wrapper-checked {
                        .select-png {
                            display: block;
                        }
                        .radio-png {
                            border: 1px solid #0056ff;
                        }
                    }
                    .ant-radio-button-wrapper::before {
                        display: none;
                    }
                }
            }
            .group-osm {
                margin-top: 16px;
                padding-top: 12px;
                border-top: 1px solid @config-title-bdb-color; //#cbcfd5;
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: @config-title-font-color;
                .class-title {
                    margin: 0;
                    height: auto;
                    display: flex;
                    align-items: center;
                    .title-border {
                        width: 0;
                        height: 14px;
                        border-right: 2px solid #0056ff;
                        margin-right: 8px;
                    }
                    .group-title {
                        font-size: 14px;
                    }
                }

                .group-content {
                    .ant-switch {
                        font-size: 14px;
                        background-color: #5d656c;
                    }
                    .ant-switch-checked {
                        background-color: #1677ff;
                    }
                }
            }
            .compoennt-type-group {
                width: 288px;
                display: flex;
                margin-top: 33px;
                margin-bottom: 12px;
                border-collapse: collapse;

                .type-item {
                    flex: 1;
                    height: 32px;
                    color: @config-title-font-color;
                    box-shadow: 0 0 0 0.5px @config-tab-bs-color; //#d1cece;
                    box-sizing: border-box;
                    // border: 1px solid #d1cece;
                    border: none !important;
                    text-align: center;
                    line-height: 32px;
                    font-size: 14px;
                    font-family: "SourceHanSansCNMedium";
                    font-weight: 400;
                    cursor: pointer;
                }
                .type-item:first-child {
                    border-radius: 2px 0 0 2px;
                }
                .type-item:last-child {
                    border-radius: 0 2px 2px 0;
                }
                .select-type {
                    border: none !important;
                    color: #0056ff;
                    // z-index: 100;
                    box-shadow: 0 0 0 1px #0056ff;
                    background-color: @config-tab-select-bg-color;
                }
            }
            .panel-list {
                width: 288px;
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;

                .panel-item {
                    width: 140px;
                    height: 93px;
                    border-radius: 4px;
                    box-sizing: border-box;
                    // background-color: #eee;
                    margin: 4px 0;
                    cursor: pointer;
                    position: relative;
                    border: 1px solid @config-panel-bd-color;
                    .select-png {
                        width: 20px;
                        height: 20px;
                        position: absolute;
                        right: 10px;
                        top: 10px;
                        display: none;
                    }
                    .desc-png {
                        width: 100%;
                        height: 100%;
                        border-radius: 4px;
                    }
                    .desc-text {
                        width: 60px;
                        position: absolute;
                        left: 10px;
                        top: 10px;
                        background-color: #dee4e5;
                        text-align: center;
                        color: rgb(0, 0, 0);
                        border-radius: 4px;
                    }
                }
                .select-panel {
                    border: 1px solid #0056ff;
                    .select-png {
                        display: block;
                    }
                }
                .hide-div {
                    display: none;
                }
                .ant-empty {
                    width: 100%;
                }
            }
        }
    }
    .class-item:last-child {
        border: 0;
    }
}
