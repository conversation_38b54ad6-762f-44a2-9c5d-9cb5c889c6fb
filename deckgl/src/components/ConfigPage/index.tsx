/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2023-02-01 19:25:52
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 16:23:27
 * @FilePath: \deckgl\src\components\ConfigPage\index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { useEffect, useState } from "react";
import { Input, Radio, Select, Switch, Form, Empty } from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import themeDarkPng from "@/assets/img/themeDark.png";
import themeLightPng from "@/assets/img/themeLight.png";
import selectIcon from "@/assets/img/select.png";
import {
    customPanelTypeList,
    panelList,
} from "@/components/ConfigPage/dataPanelList";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/app/store";
import { setConfig } from "@/features/dataSlice";
import "./index.less";
import { ipPattern } from "@/utils/index";
import MapConfig from "@/components/MapConfig";
import { copyText } from "@/utils";
import { useTranslation } from "react-i18next";
function ConfigPage() {
    const { t } = useTranslation("configPage");
    const dispatch = useDispatch();
    const config = useSelector((state: RootState) => state.dataReducer.config);
    const [diableConnectState, setDiableConnectState] = useState(false);
    const [customPanelShowType, setCustomPanelShowType] = useState("dashboard");
    const [customPanelList, setCustomPanelList] = useState<string[]>([]);
    const { connectConfig, parentApp, layoutConfig, language } = config;
    const [form] = Form.useForm();

    /**
     * @description: 更新配置项
     * @return {*}
     */
    const handleUpdateConfig = ({
        key,
        type,
        val,
    }: {
        key?: any;
        type: any;
        val: any;
    }) => {
        //校验通过后再进行请求
        if (type == "ip" && !ipPattern.test(val)) {
            console.log("ip校验没通过");
            return false;
        }
        // 获取回放列表
        // if (type == "ip") {
        //     getBagList(val);
        // }
        if (key) {
            const oldConfig = JSON.parse(JSON.stringify(config[key]));
            oldConfig[type] = val;
            dispatch(
                setConfig({
                    ...config,
                    [key]: oldConfig,
                })
            );
        } else {
            dispatch(
                setConfig({
                    ...config,
                    [type]: val,
                })
            );
        }
    };
    /**
     * @description: ip格式校验
     * @param {string} val
     * @return {*}
     */
    const handleValidator = (val: string) => {
        if (val) {
            if (!ipPattern.test(val)) {
                return Promise.reject("IP格式有误");
            }
            return Promise.resolve();
        } else {
            return Promise.reject("请输入ip地址");
        }
    };
    /**
     * @description: 自定义组件点击显示or隐藏
     * @param {string} str
     * @return {*}
     */
    const setShowPanelList = (str: string) => {
        setCustomPanelShowType(str);
    };
    const selectPanel = (str: string) => {
        const newLayout = layoutConfig?.map((item: any) => {
            if (item.name === str) {
                return {
                    ...item,
                    display: !item.display,
                };
            }
            return item;
        });
        handleUpdateConfig({
            type: "layoutConfig",
            val: newLayout,
        });
    };
    const downloadLayoutConfig = () => {
        console.log(config);
        //  将 config  复制到粘贴板
        copyText(config);
    };
    useEffect(() => {
        const initArr: string[] = [];
        layoutConfig?.forEach((item: any) => {
            if (panelList?.[item.name]?.group === customPanelShowType) {
                initArr.push(item.name);
            }
        });
        setCustomPanelList(initArr);
    }, [layoutConfig, customPanelShowType]);

    useEffect(() => {
        if (parentApp && connectConfig && !connectConfig.ip) {
            setDiableConnectState(true);
        }
    }, [connectConfig, parentApp]);

    return (
        <div className={`config-page-main ${language} `}>
            <div className="class-item">
                <div className="class-title">
                    <div className="title-border"></div>
                    <div className="title-text">{t("connection")}</div>
                </div>
                <div className="class-content">
                    <div className="group-item">
                        <div className="group-title">IP:</div>
                        <div className="group-content">
                            <Form
                                form={form}
                                name="basic"
                                initialValues={{
                                    ip: config.connectConfig.ip,
                                }}
                                className="config-form"
                            >
                                <Form.Item
                                    label=""
                                    name="ip"
                                    className="ip-input"
                                    validateTrigger={["onBlur", "onChange"]}
                                    rules={[
                                        {
                                            validator: async (_, value) =>
                                                handleValidator(value),
                                        },
                                    ]}
                                >
                                    {/* <div className="group-content ip"> */}
                                    {/*   value={ipVal} config.connectConfig.ip */}
                                    {/* defaultValue={config.connectConfig.ip} */}
                                    <Input
                                        placeholder={t("inputPlaceholder")}
                                        disabled={diableConnectState}
                                        onChange={(e) =>
                                            handleUpdateConfig({
                                                key: "connectConfig",
                                                type: "ip",
                                                val: e.target.value,
                                            })
                                        }
                                    />
                                    {/* </div> */}
                                </Form.Item>
                            </Form>
                        </div>
                    </div>
                    <div className="group-item">
                        <div className="group-title">PORT:</div>
                        <div className="group-content">
                            <Input
                                value={config.connectConfig.port}
                                placeholder={t("inputPlaceholder")}
                                disabled={diableConnectState}
                                onChange={(e) =>
                                    handleUpdateConfig({
                                        key: "connectConfig",
                                        type: "port",
                                        val: e.target.value,
                                    })
                                }
                            />
                        </div>
                    </div>
                    <div className="group-item">
                        <div className="group-title">{t("dataType")}:</div>
                        <div className="group-content">
                            <Select
                                defaultValue={config.connectConfig.origin}
                                popupClassName={`config-dropdown ${config.theme}`}
                                disabled={diableConnectState}
                                onSelect={(value) =>
                                    handleUpdateConfig({
                                        key: "connectConfig",
                                        type: "origin",
                                        val: value,
                                    })
                                }
                                options={[
                                    {
                                        value: "HMI",
                                        label: "HMI",
                                    },
                                    {
                                        value: "EXTEND",
                                        label: "EXTEND",
                                    },
                                    {
                                        value: "SNAKE",
                                        label: "SNAKE",
                                    },
                                    {
                                        value: "FITONE",
                                        label: "FITONE",
                                    },
                                    {
                                        value: "FITALL",
                                        label: "FITALL",
                                    },
                                ]}
                            />
                        </div>
                    </div>
                </div>
            </div>
            {/* 地图配置 */}
            <MapConfig handleUpdateConfig={handleUpdateConfig} />
            <div className="class-item">
                <div className="class-title">
                    <div className="title-border"></div>
                    <div className="title-text">{t("appearanceTheme")}:</div>
                </div>
                <div className="class-content">
                    <div className="group-item">
                        <Radio.Group
                            defaultValue={config.theme}
                            onChange={(e) =>
                                handleUpdateConfig({
                                    type: "theme",
                                    val: e.target.value,
                                })
                            }
                        >
                            <Radio.Button value="light">
                                <img
                                    className="radio-png"
                                    src={themeLightPng}
                                    alt="light.png"
                                />
                                <img
                                    src={selectIcon}
                                    alt="slect.png"
                                    className="select-png"
                                />
                            </Radio.Button>
                            <Radio.Button value="dark">
                                <img
                                    className="radio-png"
                                    src={themeDarkPng}
                                    alt="dark.png"
                                />
                                <img
                                    src={selectIcon}
                                    alt="select.png"
                                    className="select-png"
                                />
                            </Radio.Button>
                        </Radio.Group>
                    </div>
                    <div className="group-osm">
                        <div className="class-title">
                            <div className="title-border"></div>
                            <div className="group-title">OSM</div>
                        </div>
                        <div className="group-content">
                            <Switch
                                defaultChecked={config.mapConfig.osm}
                                checkedChildren={t("on")}
                                unCheckedChildren={t("off")}
                                onChange={(checked) =>
                                    handleUpdateConfig({
                                        key: "mapConfig",
                                        type: "osm",
                                        val: checked,
                                    })
                                }
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div className="class-item">
                <div className="class-title">
                    <div className="title-border"></div>
                    <div className="title-text">{t("customComponent")}:</div>
                    <DownloadOutlined
                        onClick={downloadLayoutConfig}
                        title={t("saveComponentConfig")}
                    />
                </div>
                <div className="class-content">
                    <div className="compoennt-type-group">
                        {customPanelTypeList.map((item, index) => {
                            return (
                                <div
                                    className={`type-item ${
                                        customPanelShowType === item
                                            ? "select-type"
                                            : ""
                                    } `}
                                    key={index}
                                    onClick={() => setShowPanelList(item)}
                                >
                                    {item}
                                </div>
                            );
                        })}
                    </div>
                    <div className="panel-list">
                        {customPanelList.length ? (
                            config.layoutConfig?.map((item, index) => {
                                if (customPanelList.indexOf(item.name) > -1) {
                                    return (
                                        <div
                                            className={`${
                                                item.display
                                                    ? "select-panel"
                                                    : ""
                                            } panel-item`}
                                            key={item.name}
                                            onClick={() =>
                                                selectPanel(item.name)
                                            }
                                        >
                                            <img
                                                className="desc-png"
                                                src={
                                                    panelList[item.name][
                                                        config.theme || "light"
                                                    ]
                                                }
                                                alt={item.name}
                                            />
                                            <img
                                                src={selectIcon}
                                                alt="select.png"
                                                className="select-png"
                                            />
                                        </div>
                                    );
                                } else {
                                    return (
                                        <div
                                            className="hide-div"
                                            key={index}
                                        ></div>
                                    );
                                }
                            })
                        ) : (
                            <Empty description={t("noSuchComponent")}>
                                {/* <Button></Button> */}
                            </Empty>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default ConfigPage;
