/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2023-01-10 19:15:27
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2024-07-08 16:44:26
 * @FilePath: \deckgl\src\components\ConfigPage\dataPanelList.ts
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import carInfoLight from "@/assets/dataPanelImages/carInfoLight.png";
import carInfoDark from "@/assets/dataPanelImages/carInfoDark.png";
import vehicleInfoLight from "@/assets/dataPanelImages/vehicleInfoLight.png";
import vehicleInfoDark from "@/assets/dataPanelImages/vehicleInfoDark.png";
import runInfoLight from "@/assets/dataPanelImages/runInfoLight.png";
import runInfoDark from "@/assets/dataPanelImages/runInfoDark.png";
import mapBoxLight from "@/assets/dataPanelImages/mapBoxLight.png";
import mapBoxDark from "@/assets/dataPanelImages/mapBoxDark.png";
import faultDark from "@/assets/dataPanelImages/faultDark.png";
import faultLight from "@/assets/dataPanelImages/faultLight.png";
import decisionLight from "@/assets/dataPanelImages/decisionLight.png";
import decisionDark from "@/assets/dataPanelImages/decisionDark.png";
import controlsLight from "@/assets/dataPanelImages/controlsLight.png";
import controlsDark from "@/assets/dataPanelImages/controlsDark.png";
import chartLight from "@/assets/dataPanelImages/chartLight.png";
import chartDark from "@/assets/dataPanelImages/chartDark.png";
import tableLight from "@/assets/dataPanelImages/tableLight.png";
import tableDark from "@/assets/dataPanelImages/tableDark.png";
import videoPng from "@/assets/dataPanelImages/video.png";
import trafficLight from "@/assets/dataPanelImages/trafficLight.png";
import trafficDark from "@/assets/dataPanelImages/trafficDark.png";
import logoLight from "@/assets/dataPanelImages/logoLight.png";
import logoDark from "@/assets/dataPanelImages/logoDark.png";
import constrainsLight from "@/assets/dataPanelImages/constrainsLight.png";
import constrainsDark from "@/assets/dataPanelImages/constrainsDark.png";
export type GeojsonMap = string;
export type PanelType = string;
export interface IPanelView {
    name: string;
    display: boolean;
    position: { left: number; top: number } | null;
}
export interface IPanelViewPos {
    left: number;
    top: number;
}
export const defaultPanel: IPanelView[] = [
    {
        name: "carInfo",
        display: false,
        position: {
            left: 0.004,
            top: 0.24138286893704852,
        },
    },
    {
        name: "vehicleInfo",
        display: false,
        position: {
            left: 0.5445,
            top: 0.37564499484004127,
        },
    },
    {
        name: "runInfo",
        display: false,
        position: {
            left: 0.5445,
            top: 0.37564499484004127,
        },
    },
    {
        name: "fault",
        display: false,
        position: {
            left: 0.167,
            top: 0.6697626418988648,
        },
    },
    {
        name: "mapBox",
        display: false,
        position: {
            left: 0.5445,
            top: 0.6594427244582043,
        },
    },
    {
        name: "decision",
        display: false,
        position: {
            left: 0.004,
            top: 0.803921568627451,
        },
    },
    {
        name: "controls",
        display: false,
        position: {
            left: 0.004,
            top: 0.6130030959752322,
        },
    },
    {
        name: "constrains",
        display: false,
        position: {
            left: 0.004,
            top: 0.6130030959752322,
        },
    },
    {
        name: "videos",
        display: false,
        position: {
            left: 0.004,
            top: 0.613,
        },
    },
    {
        name: "traffic",
        display: false,
        position: {
            left: 0.696458064516129,
            top: 0.030498258977149074,
        },
    },
    {
        name: "logo",
        display: false,
        position: {
            left: 0.696458064516129,
            top: 0.030498258977149074,
        },
    },

    // {
    //     name: "charts",
    //     display: false,
    //     position: {
    //         left: 0.772,
    //         top: 0.42518059855521156,
    //     },
    // },
    // {
    //     name: "table",
    //     display: false,
    //     position: {
    //         left: 0.004,
    //         top: 0.39215686274509803,
    //     },
    // },
];
export const panelList: any = {
    carInfo: {
        group: "dashboard",
        light: carInfoLight,
        dark: carInfoDark,
    },
    // vehicleInfo: {
    //     group: "dashboard",
    //     light: vehicleInfoLight,
    //     dark: vehicleInfoDark,
    // },
    runInfo: {
        group: "dashboard",
        light: runInfoLight,
        dark: runInfoDark,
    },
    fault: {
        group: "module",
        light: faultLight,
        dark: faultDark,
    },
    controls: {
        group: "module",
        light: controlsLight,
        dark: controlsDark,
    },
    decision: {
        group: "module",
        light: decisionLight,
        dark: decisionDark,
    },
    constrains: {
        group: "module",
        light: constrainsLight,
        dark: constrainsDark,
    },

    // charts: {
    //     group: "module",
    //     light: chartLight,
    //     dark: chartDark,
    // },
    // table: {
    //     group: "module",
    //     light: tableLight,
    //     dark: tableDark,
    // },
    mapBox: {
        group: "extend",
        light: mapBoxLight,
        dark: mapBoxDark,
    },
    videos: {
        group: "extend",
        light: videoPng,
        dark: videoPng,
    },
    traffic: {
        group: "extend",
        light: trafficLight,
        dark: trafficDark,
    },
    logo: {
        group: "extend",
        light: logoLight,
        dark: logoDark,
    },
};
export const customPanelTypeList = ["dashboard", "module", "extend"];
