/*
 * @Author: jack <EMAIL>
 * @Date: 2023-02-27 17:26:07
 * @LastEditors: luofei luofei.trunk.tech
 * @LastEditTime: 2023-03-10 17:05:07
 * @FilePath: /deckgl/src/components/VideoPlayer/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { useEffect, useRef, useState, useMemo } from "react";

import "./index.less";

interface VideoPlayerProps {
    name: string;
    url: string;
}

function VideoPlyer({ imageList }: { imageList: {} }) {
    // ref
    // const canvas = useRef<HTMLCanvasElement>(null);

    // uesMemo
    let imageUrlList = useMemo(
        () =>
            imageList.map((item: any) => {
                return {
                    name: item.topic,
                    url: "data:image/jpeg;base64," + item.url,
                };
            }),
        [imageList]
    );

    return (
        <div className="videoPlayer">
            {imageUrlList.length > 0
                ? imageUrlList.map(
                      ({ url, name }: { url: string; name: string }) => {
                          return <img src={url} alt={name} key={name} />;
                      }
                  )
                : null}
        </div>
    );
}

export default VideoPlyer;
