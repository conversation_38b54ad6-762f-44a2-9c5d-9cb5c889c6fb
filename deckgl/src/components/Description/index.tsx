/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2023-08-23 17:24:57
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 10:39:32
 * @FilePath: \deckgl\src\components\Description\index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { themeStyle } from "@/theme";
import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import Draggable from "react-draggable";
import "./index.less";
import { useTranslation } from "react-i18next";
function Description({ descriptopnChart }: { descriptopnChart: boolean }) {
    const config = useSelector((state: RootState) => state.dataReducer.config);
    const { theme } = config;
    const { t } = useTranslation(["description"]);
    return (
        <Draggable handle={"strong"}>
            <div
                className="chart-modal"
                style={{ display: descriptopnChart ? "block" : "none" }}
            >
                <strong>
                    <h2>{t("elementLegend")}</h2>
                    {/* 地图元素图示 */}
                </strong>
                <div className="chart-content">
                    {Object.keys(themeStyle[theme || "light"].geoJson).map(
                        (item) => {
                            const filterArr = [
                                "0",
                                "Arrow",
                                "default",
                                "Yard",
                                "Building",
                            ];
                            if (filterArr.indexOf(item) !== -1) return null;
                            const value = themeStyle.dark.geoJson[item];
                            return (
                                <div className="chart-item" key={item}>
                                    <div
                                        className="color"
                                        style={{
                                            background: `rgba(${value[0]},${
                                                value[1]
                                            },${value[2]},${value[3] / 255})`,
                                        }}
                                    ></div>
                                    <div className="title">
                                        {item == "0" ? "Line" : item}
                                    </div>
                                </div>
                            );
                        }
                    )}
                </div>
                <strong>
                    <h2>{t("shortcutLegend")}</h2>
                    {/* 快捷键图示 */}
                </strong>
                <div className="chart-content">
                    <div className="chart-item">
                        <div className="btn ">C </div>
                        {/* 控制台 */}
                        <div className="title">{t("console")} </div>
                    </div>
                    <div className="chart-item">
                        <div className="btn">D </div>
                        <div className="title">
                            {t("debug")}
                            {/* 调试 */}
                        </div>
                    </div>
                    <div className="chart-item">
                        <div className="btn">2 </div>
                        <div className="title">
                            {t("view2d")}
                            {/* 2D视角 */}
                        </div>
                    </div>
                    <div className="chart-item">
                        <div className="btn">⌃ M </div>
                        <div className="title">
                            {t("menuBar")}
                            {/* 菜单栏 */}
                        </div>
                    </div>
                    <div className="chart-item">
                        <div className="btn">R </div>
                        <div className="title">
                            {t("reset")}
                            {/* 重置 */}
                        </div>
                    </div>
                    <div className="chart-item">
                        <div className="btn">3 </div>
                        <div className="title">
                            {t("view3d")}
                            {/* 3D视角 */}
                        </div>
                    </div>
                    <div className="chart-item">
                        <div className="btn">L </div>
                        <div className="title">
                            {t("viewLock")}
                            {/* 视角锁定 */}
                        </div>
                    </div>
                    <div className="chart-item">
                        <div className="btn">M </div>
                        <div className="title">
                            {t("measure")}
                            {/* 测量 */}
                        </div>
                    </div>
                    <div className="chart-item">
                        <div className="btn">H </div>
                        <div className="title">
                            {t("legend")}
                            {/* 图例 */}
                        </div>
                    </div>
                    <div className="chart-item">
                        <div className="btn">S </div>
                        <div className="title">
                            {t("satelliteMap")}
                            {/* 卫星地图 */}
                        </div>
                    </div>
                    <div className="chart-item">
                        <div className="btn">O </div>
                        <div className="title">
                            {t("modelTransparency")}
                            {/* 模型透明 */}
                        </div>
                    </div>
                </div>
            </div>
        </Draggable>
    );
}
export default Description;
