@import "@/theme/mixin.less";

.chart-modal {
    position: absolute;
    top: 7%;
    right: 420px;
    transform: translate(-50%, -50%);
    width: 400px;
    // height: 300px;
    background-color: @config-bg-color;
    // background-color: @player-container-bg-color;
    border-radius: 4px;
    padding-bottom: 20px;
    position: relative;
    h2 {
        font-size: 16px;
        text-align: center;
        margin: 10px auto;
        color: @config-title-font-color;
        &:hover {
            cursor: pointer;
        }
    }
    .icon-close {
        position: absolute;
        right: 10px;
        font-size: 14px;
        top: 10px;
        cursor: pointer;
        color: @config-title-font-color;
    }
    .chart-content {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        padding: 0 10px;

        .chart-item {
            display: flex;
            color: @config-title-font-color;
            flex: 0 0 32%;
            height: 20px;
            text-align: left;
            line-height: 20px;
            /* 边距懒得算，css函数代替 */
            margin-right: calc(2%);
            margin-bottom: calc(2%);
            .title {
                flex: 1;
                color: @config-title-font-color;
                font-size: 14px;
                padding-right: 5px;
            }
            .btn {
                width: 40px;
                height: 20px;
                font-weight: bold;
                border: 1px solid #c0bebe;
                border-radius: 5px;
                line-height: 20px;
                text-align: center;
                box-shadow: 0 0 0 0.5px @panel-bs-color;
                margin-right: 5px;
            }
            .color {
                width: 40px;
                height: 20px;
                margin-right: 5px;
            }
        }
        /* 去除每行尾的多余边距 */
        .chart-item:nth-child(3n) {
            margin-right: 0;
        }
        /* 使最后一个元素的边距填满剩余空间 */
        .chart-item:last-child {
            margin-right: auto;
        }
    }
}
