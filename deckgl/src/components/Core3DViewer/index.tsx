/*
 * @Author: fanmx <EMAIL>
 * @Date: 2024-02-22 16:51:39
 * @LastEditors: luofei <EMAIL>
 * @LastEditTime: 2025-04-21 14:31:45
 * @FilePath: /deckgl/src/components/Core3DViewer/index.tsx
 * @Description:
 *
 * Copyright (c) 2024 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { useEffect, useRef } from "react";
import MAPGL from "@/components/Map";
import MapTools from "@/components/Map/MapTools";
import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { useDispatch } from "react-redux";
import { setOperation } from "@/features/dataSlice";
let flag = true;

function D3Player() {
    const childRef = useRef() as any; // 声明 useRef

    const { changeMapView, changeSetting } = childRef?.current || {};
    const handleButtonClick = () => {
        flag = !flag;
        changeSetting &&
            changeSetting({
                measure: flag,
            });
    };
    // const [data, setData] = useState({});
    const data = useSelector((state: any) => state.dataReducer.data.data);
    const config = useSelector((state: any) => state.dataReducer.config);
    const settings = useSelector((state: any) => state.dataReducer.settings);
    const state = useSelector((state: RootState) => state.dataReducer.status);
    const truckDebugInfo = useSelector(
        (state: any) => state.dataReducer.truckDebugInfo
    );

    const dispatch = useDispatch();
    const _onclickFunc = (e: any) => {
        if (e?.object?.info) {
            dispatch(setOperation({ selectObj: { ...e?.object?.info } }));
        } else {
            dispatch(setOperation({ selectObj: { id: -1 } }));
        }
    };
    const _onHoverFunc = (e: any) => {
        console.log("onhover", e);
    };
    function parentMethod() {
        console.log("This is the method of the parent component");
    }
    useEffect(() => {
        if (childRef.current) childRef.current.changeSetting(settings);
    }, [settings]);

    return (
        <div
            style={{ width: "100%", height: "100%" }}
            className={`${config?.theme || "dark"}`}
        >
            {/* <button onClick={handleButtonClick}>Call Child Method</button> */}
            <MAPGL
                {...{
                    data,
                    config,
                    truckDebugInfo,
                }}
                _onclickFunc={_onclickFunc}
                ref={childRef}
            ></MAPGL>
            {config.tools && (
                <MapTools
                    {...{
                        config,
                        state,
                        settings,
                    }}
                    ref={childRef}
                ></MapTools>
            )}
        </div>
    );
}
export default D3Player;
