/*
 * @Author: jack <EMAIL>
 * @Date: 2023-05-30 14:34:53
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2023-08-22 16:01:11
 * @FilePath: /deckgl/src/components/Console/index.tsx
 * @Description: 数据console 监听
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import ReactJson from "react-json-view";
import "./index.less";
function Console({ isOpen }: { isOpen: boolean }) {
    const workData = useSelector((state: any) => state.dataReducer.data);
    const config = useSelector((state: any) => state.dataReducer.config);
    const theme = config.theme;

    const consoleTheme = useMemo(() => {
        switch (theme) {
            case "light":
                return "bright:inverted";
            case "dark":
                return "ocean";
            default:
                return "ocean";
        }
    }, [theme]);

    // useEffect(() => {
    //     // 按下ctrl + ~ 打开console
    //     const handleKeyDown = (e: any) => {
    //         if (e.ctrlKey && e.keyCode === 192) {
    //             setIsOpen((state) => !state);
    //         }
    //     };
    //     document.addEventListener("keydown", handleKeyDown);
    //     return () => {
    //         document.removeEventListener("keydown", handleKeyDown);
    //     };
    // }, []);

    const getData = async () => {
        // 格式化json 设置格式
        const data = JSON.stringify(workData, null, 4);
        // 复制到剪切板

        if (navigator.clipboard) {
            return navigator.clipboard.writeText(data);
        } else {
            return new Promise((resolve, reject) => {
                const textarea = document.createElement("textarea");
                textarea.value = data;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand("copy");
                textarea.remove();
                resolve(true);
            });
        }
    };

    if (!isOpen) return null;
    return (
        <div>
            <div className="console">
                <button onClick={getData}>复制</button>
                <ReactJson
                    src={workData.data}
                    theme={consoleTheme}
                    enableClipboard={true}
                    collapsed={0}
                    name={"renderData"}
                    iconStyle="square"
                />
                <ReactJson
                    src={workData.realData}
                    theme={consoleTheme}
                    enableClipboard={true}
                    collapsed={0}
                    name={"bridgeData"}
                    iconStyle="square"
                />
                <ReactJson
                    src={workData.config}
                    theme={consoleTheme}
                    enableClipboard={true}
                    collapsed={1}
                    name={"config"}
                    iconStyle="square"
                />
            </div>
        </div>
    );
}
export default Console;
