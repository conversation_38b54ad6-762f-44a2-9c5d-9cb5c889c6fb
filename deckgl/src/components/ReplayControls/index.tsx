/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2022-12-01 17:40:34
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 16:54:18
 * @FilePath: \deckgl\src\components\ReplayControls\index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { useEffect, useRef, useState } from "react";
import { Dropdown, Button, Slider, message } from "antd";
import type { MenuProps } from "antd";
import { RootState } from "@/app/store";
import { useDispatch, useSelector } from "react-redux";
import { setTime } from "@/utils";
import "./index.less";
import type { SliderMarks } from "antd/es/slider";
import { playbackLoopTypeEnum, playbackRateTypeEnum } from "@/utils/enum";
import {
    setSpeedControl,
    setStartPause,
    jump,
    clearSelectBag,
    getDetail,
    adjustBagInfoByBagDetail,
    filterTopic,
} from "@/request/bag";
import {
    setCurrentReplayBagInfo,
    setPlayingStateHook,
} from "@/features/dataSlice";
import { IReplayBagInfo } from "@/types";
import { useTranslation } from "react-i18next";
// 首次加载不执行工具Hook
function useDidUpdateEffect(fn: Function, inputs: any[]) {
    const disMountRef = useRef(false);
    useEffect(() => {
        if (disMountRef.current) fn();
        else disMountRef.current = true;
    }, inputs);
}

function ReplayControls() {
    const { t } = useTranslation([
        "replayControl",
        "enum",
        "common",
        "message",
    ]);
    const dispatch = useDispatch();
    const [isPlaying, setIsPlayingState] = useState(false);
    const [percent, setPercent] = useState(0);
    const [silderMarks, setSliderMarks] = useState<SliderMarks>({});
    const playButton = useRef<HTMLElement | null>(null);
    // store
    const config = useSelector((state: RootState) => state.dataReducer.config);
    const data = useSelector((state: RootState) => state.dataReducer.data.data);
    const selectBagList = useSelector(
        (state: RootState) => state.dataReducer.selectBagList
    );
    const currentReplayBagInfo = useSelector(
        (state: RootState) => state.dataReducer.currentReplayBagInfo
    );
    const filterTopicList = useSelector(
        (state: RootState) => state.dataReducer.filterTopicList
    );
    const playingStateHook = useSelector(
        (state: RootState) => state.dataReducer.playingStateHook
    );
    // 速率
    const [selectedSpeed, setSelectedSpeed] = useState(["1"]);
    // 循环 TODO闭包问题
    const [selectedPlayLoopType, setSelectedPlayLoopType] = useState([
        "single",
    ]);
    // 区间播放时间段
    const [playRange, setPlayRange] = useState<[number, number]>([0, 0]);
    // 编辑播放区间状态
    const [editPlayRangeStatus, setEditPlayRangeStatus] = useState(false);
    // 展示时间格式 UTC
    const [showTimeType, changeShowTimeType] = useState<string>("");
    const replayInfoRef = useRef<null | IReplayBagInfo>(null);
    // 防止多次进入选包循环
    const stopLoop = useRef<Boolean>(false);
    replayInfoRef.current = currentReplayBagInfo;
    //国际化处理
    const playbackLoopTypeEnumList = playbackLoopTypeEnum?.map((item) => {
        let st = item.label;
        return { key: item?.key, label: t(`enum:${st}`) };
    });

    const formatter = (value: number | undefined = 0) => {
        if (showTimeType) {
            return (currentReplayBagInfo?.bag_start_time || 0) + value;
        }
        return `${setTime(value || 0, "mm:ss")}`;
    };
    const handleSpeed: MenuProps["onClick"] = ({ key }) => {
        setSelectedSpeed([key]);
    };
    // 循环播放
    const handlePlayLoopType: MenuProps["onClick"] = (event) => {
        // 重置时间段
        if (event.key == "single" || event.key == "list") {
            setPlayRange([0, currentReplayBagInfo?.bag_length || 0]);
        } else if (event.key == "frame") {
            setPlayRange([0, currentReplayBagInfo?.bag_length || 0]);
            setStartPause(
                config.connectConfig.ip,
                config.connectConfig.apiPort,
                "pause"
            ).then(() => setIsPlayingState(false));
        }
        // 选择区间播放范围时，暂停播放 开始编辑回放区间
        else {
            setEditPlayRangeStatus(true);
            if (isPlaying) {
                startPlay();
            }
        }
        setSliderMarks({});
        selectedPlayLoopType[0] = event.key;
        setSelectedPlayLoopType(selectedPlayLoopType);
    };
    // 编辑区间
    const handlePlayRange = (range: [number, number]) => {
        setPlayRange(range);
    };
    // 编辑区间完成
    const setPlayRangeDone = () => {
        setEditPlayRangeStatus(false);
        setSliderMarks({
            [playRange[0]]: {
                style: {
                    color: "#fff",
                },
                label: `${t("start")}：${setTime(playRange[0], "mm:ss")}`,
            },
            [playRange[1]]: {
                style: {
                    color: "#fff",
                },
                label: `${t("end")}：${setTime(playRange[1], "mm:ss")}`,
            },
        });
        !isPlaying && startPlay();
    };
    const handlePlayingStatus = () => {
        if (editPlayRangeStatus) {
            message.error(t("message:finishEditFirst"));
        } else {
            startPlay();
        }
    };

    const startPlay = async () => {
        if (selectedPlayLoopType[0] == "frame") {
            setStartPause(
                config?.connectConfig?.ip,
                config?.connectConfig?.apiPort,
                "forward"
            );
        } else {
            setIsPlayingState((value) => {
                let status = !value;
                let param = status ? "run" : "pause";
                setStartPause(
                    config?.connectConfig?.ip,
                    config?.connectConfig?.apiPort,
                    param
                );
                return status;
            });
        }
    };
    const setSpeed = () => {
        setSpeedControl(
            config?.connectConfig?.ip,
            config?.connectConfig?.apiPort,
            Number(selectedSpeed[0])
        )
            .then((res) => {})
            .catch((err) => {
                console.log("error :", err);
            });
    };

    const handleJump = (value: any) => {
        let realTime = value;
        const endTime = replayInfoRef.current?.bag_length || 0;
        if (realTime < 0) {
            realTime = 0.1;
        } else if (realTime > endTime) {
            // 防止最终的结束时间没有数据导致进度卡住
            realTime = endTime ? endTime - 0.1 : 0;
        }
        jump(
            config?.connectConfig?.ip,
            config?.connectConfig?.apiPort,
            realTime
        )
            .then((res) => {
                setPercent(realTime);
            })
            .catch((err) => {
                console.log("error :", err);
            });
    };

    const handleClear = () => {
        clearSelectBag(
            config?.connectConfig?.ip,
            config?.connectConfig?.apiPort
        );
    };

    const setPlayClear = () => {
        setIsPlayingState(false);
        setPercent(0);
    };
    const changeTimeType = () => {
        let result = "";
        if (!showTimeType) {
            result = "UTC";
        }
        changeShowTimeType(result);
    };
    const getShowTimeByType = (type: string) => {
        if (type) {
            const left = currentReplayBagInfo?.bag_start_time
                ? (currentReplayBagInfo?.bag_start_time + percent).toFixed(2)
                : "00:00";
            const right =
                currentReplayBagInfo?.bag_end_time.toFixed(2) || "00:00";
            return left + "/" + right;
        }
        const left = setTime(percent || 0, "mm:ss");
        const right = setTime(currentReplayBagInfo?.bag_length || 0, "mm:ss");
        return left + "/" + right;
    };

    /**
     * @description: 事件绑定
     * @param {any} e
     * @return {*}
     */
    const keyDown = (e: any) => {
        switch (e.code) {
            case "Space":
                startPlay();
                break;
            case "ArrowLeft":
                setPercent((state) => {
                    handleJump(state - 10);
                    return state;
                });
                break;
            case "ArrowRight":
                setPercent((state) => {
                    handleJump(state + 10);
                    return state;
                });
                break;
            default:
                break;
        }
    };
    useEffect(() => {
        // 键盘事件绑定
        document.addEventListener("keydown", keyDown);
        //清空
        return () => {
            // 清理播放状态
            handleClear();
            setPlayClear();
            // 清理键盘事件
            document.removeEventListener("keydown", keyDown);
        };
    }, []);
    useEffect(() => {
        if (currentReplayBagInfo) {
            setSpeed();
        }
    }, [selectedSpeed]);
    useEffect(() => {
        if (currentReplayBagInfo?.bag_length) {
            setPlayRange([0, currentReplayBagInfo.bag_length]);
            setEditPlayRangeStatus(false);
            setSliderMarks({});
            // 清理播放状态
            setPlayClear();
        }
    }, [currentReplayBagInfo]);
    /**
     * @description: 收到点云回调
     * @param {any} message
     * @return {*}
     */
    useEffect(() => {
        if (data?.time && currentReplayBagInfo) {
            if (
                data.time < currentReplayBagInfo.bag_start_time ||
                data.time > currentReplayBagInfo.bag_end_time
            ) {
                console.log(data.time, "当前时间不在bag时间区间停止自动循环");
                return;
            }
            let cur = data.time || 0;
            let ses = cur - currentReplayBagInfo.bag_start_time;
            // 结束时间为bag时间或者区间时间
            const endTime =
                selectedPlayLoopType[0] == "range"
                    ? playRange[1]
                    : currentReplayBagInfo.bag_length;
            const startTime =
                selectedPlayLoopType[0] == "range" ? playRange[0] : 0;
            // 误差范围设定为0.1，数据终帧时间不确定
            if (ses >= startTime && ses < endTime - 0.1) {
                setPercent(ses);
            } else if (stopLoop.current) {
                return;
            } else {
                stopLoop.current = true;
                entryLoop();
            }
        }
    }, [data]);
    // TODO 得到baglist传来的播放状态
    useDidUpdateEffect(() => {
        setIsPlayingState(true);
    }, [playingStateHook]);
    /**
     * @description: 进入循环播包判断
     * @return {*}
     */
    const entryLoop = async () => {
        setPlayClear();
        const loopType = selectedPlayLoopType[0];
        const ip = config.connectConfig.ip;
        try {
            switch (loopType) {
                case "single":
                    await getDetail(
                        ip,
                        config.connectConfig.apiPort,
                        "/" + currentReplayBagInfo?.bag_name || ""
                    );
                    break;
                case "list":
                    //获取选中bag列表中 高亮bagname的下标
                    const currentBagIndex = selectBagList.findIndex(
                        (item: any) => {
                            return item.path == currentReplayBagInfo?.bag_name;
                        }
                    );
                    const nextBag: any =
                        currentBagIndex >= selectBagList.length - 1
                            ? selectBagList[0]
                            : selectBagList[currentBagIndex + 1];

                    console.log("列表播放切换到下一个包:", {
                        currentIndex: currentBagIndex,
                        nextBagPath: nextBag.path,
                        totalBags: selectBagList.length,
                    });

                    // 确保路径格式正确
                    let path =
                        nextBag.path.indexOf("/") == -1
                            ? "/" + nextBag.path
                            : nextBag.path;

                    // 检查是否有matched_group，如果有，使用完整的对象作为参数
                    const detailParams = nextBag.matched_group
                        ? {
                              path:
                                  nextBag.path
                                      .split("/")
                                      .slice(0, -1)
                                      .join("/") || "/",
                              matched_group: nextBag.matched_group,
                          }
                        : path;

                    const res: any = await getDetail(
                        ip,
                        config.connectConfig.apiPort,
                        detailParams
                    );

                    const bagInfo = adjustBagInfoByBagDetail(
                        path,
                        res?.results
                    );

                    await dispatch(setCurrentReplayBagInfo(bagInfo));

                    // 更新播放状态钩子，通知其他组件包已经切换
                    dispatch(setPlayingStateHook(!playingStateHook));

                    break;
                case "range":
                    await getDetail(
                        ip,
                        config.connectConfig.apiPort,
                        currentReplayBagInfo?.bag_name || ""
                    );
                    await jump(
                        config.connectConfig.ip,
                        config.connectConfig.apiPort,
                        playRange[0]
                    );
                    break;
                case "frame":
                    await getDetail(
                        ip,
                        config.connectConfig.apiPort,
                        currentReplayBagInfo?.bag_name || ""
                    );
                    break;
            }
            await startPlay();
            filterTopicList?.map((item) => {
                const { filter, name } = item;
                filterTopic({
                    ip: config?.connectConfig?.ip,
                    apiPort: config?.connectConfig?.apiPort,
                    topic: name,
                    filter: filter || false,
                });
            });

            stopLoop.current = false;
        } catch (error) {
            stopLoop.current = false;
        }
    };
    return (
        <div className="progress-box">
            {editPlayRangeStatus ? (
                <>
                    <Slider
                        range
                        min={0}
                        max={currentReplayBagInfo?.bag_length || 0}
                        tooltip={{ formatter }}
                        value={playRange}
                        onChange={handlePlayRange}
                        className="map-progress"
                    />
                    <Button
                        className="range-btn"
                        type="primary"
                        onClick={setPlayRangeDone}
                    >
                        {t("common:confirm")}
                    </Button>
                </>
            ) : (
                <Slider
                    min={0}
                    max={currentReplayBagInfo?.bag_length || 0}
                    marks={silderMarks}
                    tooltip={{ formatter }}
                    value={percent}
                    step={0.01}
                    onChange={handleJump}
                    className="map-progress"
                />
            )}

            <div className="bot">
                <div className="left">
                    <i
                        ref={playButton}
                        onClick={handlePlayingStatus}
                        className={`iconfont ${
                            isPlaying == false ? "icon-yunhang" : "icon-zanting"
                        }`}
                    ></i>{" "}
                    <span
                        className="time"
                        title={t("switchTypeTip")}
                        onClick={changeTimeType}
                    >
                        {getShowTimeByType(showTimeType)}
                    </span>
                </div>
                <div className="right">
                    <Dropdown
                        menu={{
                            items: playbackLoopTypeEnumList,

                            selectable: true,
                            defaultSelectedKeys: selectedPlayLoopType,
                            onClick: handlePlayLoopType,
                        }}
                        placement="top"
                        overlayClassName={`speed-dropdown ${config.theme}`}
                    >
                        <Button type="text" block>
                            {t("mode")}
                        </Button>
                    </Dropdown>
                    <Dropdown
                        menu={{
                            items: playbackRateTypeEnum,
                            selectable: true,
                            defaultSelectedKeys: selectedSpeed,
                            onClick: handleSpeed,
                        }}
                        placement="top"
                        overlayClassName={`speed-dropdown ${config.theme}`}
                    >
                        <Button type="text" block>
                            {t("speed")}
                        </Button>
                    </Dropdown>
                </div>
            </div>
        </div>
    );
}

export default ReplayControls;
