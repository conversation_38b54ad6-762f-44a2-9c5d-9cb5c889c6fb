@import "@/theme/mixin.less";
.progress-box {
    width: 100%;
    position: absolute;
    left: 0%;
    bottom: 3%;
    align-items: center;
    justify-content: center;
    z-index: 20;

    .map-progress {
        width: 96%;
        margin: 10px 2% !important;
        z-index: 10;
    }
    .range-btn {
        position: absolute;
        display: block;
        right: 100px;
        top: -20px;
    }
    .bot {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 2%;
        .left {
            display: flex;
            justify-content: center;
            align-items: center;
            .iconfont {
                font-size: 30px;
                color: @progress-play-font-color;
                &:hover {
                    cursor: pointer;
                }
            }
            .time {
                font-size: 14px;
                color: @panel-font-color;
                margin-left: 20px;
                cursor: pointer;
            }
        }
        .right {
            display: flex;
            .ant-btn {
                border: none;
                span {
                    color: @panel-font-color;
                }
                &:hover {
                    background: transparent !important;
                }
            }
        }
    }
}

.speed-dropdown {
    .ant-dropdown-menu {
        background: @dropdown-bg-color !important;
        span {
            color: @config-title-font-color;
        }
        .ant-dropdown-menu-item {
            &:hover {
                span {
                    color: @dropdown-font-color;
                }
                background-color: @dropdown-select-hover-bg-color !important;
            }
        }
    }
    .ant-dropdown-menu-item-active,
    .ant-dropdown-menu-item-selected {
        span {
            font-weight: bold;
        }
        color: @dropdown-font-color !important;
        background-color: @dropdown-select-item-option-selected !important;
    }
}

.ant-slider-track {
    height: 5px !important;
    background: #1677ff !important;
}
.ant-slider-rail {
    height: 5px !important;
    background: @progress-bg-color !important;
}
