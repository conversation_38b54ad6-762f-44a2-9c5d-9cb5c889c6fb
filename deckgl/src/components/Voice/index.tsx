/*
 * @Author: jack <EMAIL>
 * @Date: 2023-08-24 10:51:40
 * @LastEditors: fanmixue <EMAIL>
 * @LastEditTime: 2023-09-04 19:00:03
 * @FilePath: /deckgl/src/components/Voice/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { useState } from "react";
import { Modal } from "antd";
import { useSelector } from "react-redux";
import { setSettings, setConfig, setOperation } from "@/features/dataSlice";
import { useDispatch } from "react-redux";
import { isMobile } from "@/utils";
import { play } from "@/utils/speech";

function Voice({ theme }: { theme: any }) {
    const [voiceModal, setVoiceModal] = useState(isMobile());
    const dispatch = useDispatch();
    const ok = () => {
        setVoiceModal(false);
        dispatch(setOperation({ voice: true, fullScreen: true }));
        play("语音播报已开启");
    };
    const cancel = () => {
        setVoiceModal(false);
        dispatch(setOperation({ voice: false }));
    };
    return (
        <>
            <Modal
                width={300}
                title="是否开启语音播报？"
                open={voiceModal}
                closable={false}
                onOk={ok}
                onCancel={cancel}
                maskClosable={false}
                okText="确认"
                cancelText="取消"
                wrapClassName={`speech-modal ${theme}`}
            ></Modal>
        </>
    );
}

export default Voice;
