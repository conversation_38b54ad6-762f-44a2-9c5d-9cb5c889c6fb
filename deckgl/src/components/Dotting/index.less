@import "@/theme/mixin.less";

.chart-modal {
    position: absolute;
    top: 7%;
    right: 420px;
    transform: translate(-50%, -50%);
    width: 620px;
    background-color: @config-bg-color;
    border-radius: 4px;
    padding-bottom: 20px;
    position: relative;
    .row {
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin: 10px;
        .json-btn {
            width: auto;
            height: 30px;
            padding: 0 10px;
            background-color: #1677ff;
            font-size: 14px;

            span {
                font-weight: normal;
                color: var(--carInfo-font-color) !important;
            }
        }
    }
    .text-area {
        width: calc(100% - 20px);
        margin: 0 10px;
        border: 1px solid @config-input-bd-color;
        background: @config-input-bg-color;
        textarea,
        .ant-input-clear-icon {
            font-size: 20px;
            color: var(--dropdown-font-color) !important;
            &::placeholder {
                color: #999;
            }
        }
    }
    .vaild {
        color: red;
        margin: 0 10px;
        font-size: 14px;
    }
    .auto-btn {
        width: auto;
        height: auto;
        padding: 5px;
        background-color: #1677ff;
        font-size: 12px;
        span {
            font-weight: normal;
            color: var(--carInfo-font-color) !important;
        }
    }
    .ul {
        height: 200px;
        border: 1px solid @config-input-bd-color;
        border-radius: 10px;
        overflow-y: auto;
        margin: 10px;
        width: calc(100% - 20px);
        padding: 0 10px;
        overflow-x: hidden;
        li {
            list-style: none;
            color: @panel-font-color;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 5px 0;
            .text {
                width: 90%;
                overflow-y: auto;
                padding: 0 5px;
            }
            .del-btn {
                width: 24px;
                height: 24px;
                background-color: #1677ff;
                font-size: 12px;
                span {
                    font-weight: normal;
                    color: var(--carInfo-font-color) !important;
                }
            }
        }
        .ant-empty {
            margin-top: 32px;
        }
    }
}
