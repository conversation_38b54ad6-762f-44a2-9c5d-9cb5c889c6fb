/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2023-08-23 17:24:57
 * @LastEditors: fanmx <EMAIL>
 * @LastEditTime: 2025-07-24 13:32:28
 * @FilePath: \deckgl\src\components\Dotting\index.tsx
 * @Dotting:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/app/store";
import Draggable from "react-draggable";
import { AimOutlined } from "@ant-design/icons";
import { Input, Button, Table } from "antd";
import { updateData, setSettings } from "@/features/dataSlice";
import { useEffect, useState, forwardRef } from "react";
import "./index.less";
import { toLatLon } from "@/utils/mapTools";
import { useTranslation } from "react-i18next";
const { TextArea } = Input;

const Dotting = forwardRef(({ isShow }: { isShow: boolean }, ref) => {
    const { t } = useTranslation(["dotting", "common"]);
    const dispatch = useDispatch();
    const data = useSelector((state: RootState) => state.dataReducer.data.data);
    const settings = useSelector(
        (state: RootState) => state.dataReducer.settings
    );
    const { changeMapView } = ref?.current || {};
    const [errorMsg, setErrorMsg] = useState("");
    const [jsonValue, setJsonValue] = useState();
    const [jsonArr, setJsonArr] = useState([]);
    const [customData, setCustomData] = useState({
        circles: [],
        marks: [],
        lines: [],
    });
    const columns = [
        {
            title: "X",
            dataIndex: "x",
            key: "x",
            align: "center",
        },
        {
            title: "Y",
            dataIndex: "y",
            key: "y",
            align: "center",
        },
        {
            title: "Yaw",
            dataIndex: "yaw",
            key: "yaw",
            align: "center",
        },
        {
            title: t("common:operate"), //"操作",
            key: "action",
            align: "center",
            className: "action",
            fixed: "right",
            width: 100,
            render: (_, record: any) => (
                <Button
                    shape="circle"
                    type="primary"
                    className="auto-btn"
                    icon={<AimOutlined />}
                    size="small"
                    onClick={() => handleFocus(record)}
                ></Button>
            ),
        },
    ];
    /**
     * @description: 聚焦事件
     * @param {*} data
     * @return {*}
     */
    const handleFocus = (data) => {
        let jsonObj = JSON.parse(jsonValue);
        if (!jsonObj?.UTMZone) return;
        let utmPosition = toLatLon([data.x, data.y], jsonObj?.UTMZone);
        changeMapView &&
            changeMapView({
                longitude: utmPosition[0],
                latitude: utmPosition[1],
                zoom: 20,
            });
    };
    /**
     * @description: 过滤json中所有数组拼接在一起
     * @param {any} jsonObject
     * @return {*}
     */
    const flattenArraysFromJson = (jsonObject: any) => {
        let flatArray = [];
        function traverse(obj) {
            for (let key in obj) {
                if (Array.isArray(obj[key])) {
                    // 如果是数组，将数组元素添加到flatArray中
                    flatArray.push(...obj[key]);

                    // 如果数组中包含其他数组，递归处理
                    obj[key].forEach((item) => {
                        if (Array.isArray(item)) {
                            traverse(item);
                        }
                    });
                } else if (typeof obj[key] === "object" && obj[key] !== null) {
                    // 如果是对象但不是数组，递归处理
                    traverse(obj[key]);
                }
            }
        }
        // 开始遍历传入的jsonObject
        traverse(jsonObject);
        return flatArray;
    };
    /**
     * @description: 校验json格式是否正确
     * @param {*} str
     * @return {*}
     */
    const isJSON = (str) => {
        if (typeof str == "string") {
            try {
                var obj = JSON.parse(str);
                if (typeof obj == "object" && obj) {
                    return true;
                } else {
                    return false;
                }
            } catch (e) {
                return false;
            }
        }
    };

    /**
     * @description: input框change事件
     * @param {any} e
     * @return {*}
     */
    const handleChange = (e: any) => {
        let value = e.target.value;
        setJsonValue(value);
        if (!e.target.value) {
            handleClear();
            setErrorMsg("");
        }
        if (!isJSON(value)) {
            return setErrorMsg(t("invalidFormat")); //"输入内容不是有效的 JSON 格式"
        } else {
            setErrorMsg("");
        }
        let val = JSON.parse(value);
        if (val) {
            let list = flattenArraysFromJson(val);
            if (!list?.length) {
                setJsonArr([]);
                handleClear(true);
            } else {
                list.map((item) => ({ key: item, ...item }));
                setJsonArr(list);
                let oldVal = JSON.stringify(val, null, 2);
                setJsonValue(oldVal);
            }
        } else {
            setJsonArr([]);
        }
    };
    /**
     * @description: utm坐标转换
     * @param {*} val
     * @return {*}
     */
    const handleUtmPosition = (val: any) => {
        if (!val) return;
        let obj = JSON.parse(val);
        if (!obj.hasOwnProperty("UTMZone") || !obj["UTMZone"]) {
            return setErrorMsg(t("missingUTMZone")); //"输入内容缺少 UTMZone 字段"
        }
        if (obj["UTMZone"] >= 1 && obj["UTMZone"] <= 60) {
            //遍历数组 进行utm坐标转换
            let circles = [],
                marks = [],
                lines = [];
            for (let i = 0; i < jsonArr.length; i++) {
                if (
                    !jsonArr[i].hasOwnProperty("x") ||
                    !jsonArr[i].hasOwnProperty("y") ||
                    !jsonArr[i].hasOwnProperty("yaw")
                ) {
                    setErrorMsg(t("missingXYYaw")); //"输入内容缺少 x,y,yaw 字段"
                    break;
                }
                let utmPosition = toLatLon(
                    [jsonArr[i].x, jsonArr[i].y],
                    obj.UTMZone
                );
                circles.push({
                    position: utmPosition,
                    radius: 0.5,
                    color: [255, 0, 0, 255],
                });
                marks.push({
                    position: utmPosition,
                    text: "point_" + i,
                    size: 14,
                });
                lines.push({
                    path: calculateEndpoint(
                        jsonArr[i],
                        jsonArr[i]?.yaw || 0,
                        5
                    ).map((item) => {
                        return toLatLon(item, obj.UTMZone);
                    }),
                    color: [255, 0, 0, 255],
                    width: 0.01,
                });
            }
            setCustomData({
                circles,
                marks,
                lines,
            });
        } else {
            return setErrorMsg(t("utmZoneRange")); //"UTMZone字段值必须在1-60之间"
        }
    };
    const handleReset = () => {
        if (!isValid || !jsonValue) return;
        let val = JSON.stringify(JSON.parse(jsonValue), null, 2);
        setJsonValue(val);
    };
    /**
     * @function
     * @description 根据三角函数计算终点的位置
     */
    const calculateEndpoint = (startPoint, yaw, length) => {
        var x2 = startPoint.x + length * Math.cos(yaw);
        var y2 = startPoint.y + length * Math.sin(yaw);
        return [
            [startPoint.x, startPoint.y, 1],
            [x2, y2, 1],
        ];
    };
    /**
     * @description: 清空所有数据
     * @return {*}
     */
    const handleClear = (flag) => {
        if (!flag) setJsonValue("");
        setJsonArr([]);
        setCustomData({
            circles: [],
            marks: [],
            lines: [],
        });
        dispatch(
            updateData({
                data: {
                    ...data,
                    ...{
                        circles: [],
                        marks: [],
                        lines: [],
                    },
                },
            })
        );
    };
    /**
     * @description: 打点
     * @return {*}
     */
    const handleDot = () => {
        dispatch(
            setSettings({
                ...settings,
                ...{ viewMode: "TOP_DOWN" },
            })
        );
        dispatch(
            updateData({
                data: {
                    ...data,
                    ...customData,
                },
            })
        );
    };
    useEffect(() => {
        if (jsonArr?.length) {
            handleUtmPosition(jsonValue);
        }
    }, [jsonArr]);
    useEffect(() => {
        if (customData?.circles?.length) {
            handleDot();
        }
    }, [customData]);
    return (
        <Draggable handle={"strong"}>
            <div
                className="chart-modal"
                style={{ display: isShow ? "block" : "none" }}
            >
                <strong>
                    <h2>{t("plotPoints")}</h2>
                </strong>
                {/* <div className="row">
                    <Button
                        type="primary"
                        className="json-btn"
                        onClick={handleReset}
                    >
                        一键格式化
                    </Button>
                    <Button
                        type="primary"
                        className="json-btn"
                        onClick={handleDot}
                    >
                        打点
                    </Button>
                    <Button
                        type="primary"
                        className="json-btn"
                        onClick={handleClear}
                    >
                        清空
                    </Button>
                </div> */}
                <TextArea
                    allowClear
                    placeholder={t("requiredFormat")}
                    autoSize={{ minRows: 5, maxRows: 10 }}
                    className="text-area"
                    value={jsonValue}
                    onChange={handleChange}
                />
                <div className="vaild">{errorMsg}</div>
                <Table
                    size="small"
                    columns={columns}
                    pagination={false}
                    dataSource={jsonArr}
                    scroll={{ x: 300 }}
                    locale={{
                        emptyText: t("common:emptyText"),
                    }}
                    className="dot-table"
                />
                {/* <ul className="ul">
                    {jsonArr?.length ? (
                        jsonArr.map((item: any, index: number) => {
                            return (
                                <li key={index}>
                                    <span className="text">
                                        {JSON.stringify(item)}
                                    </span>
                                    <Button
                                        shape="circle"
                                        type="primary"
                                        className="del-btn"
                                        icon={<CloseOutlined />}
                                        size="small"
                                    ></Button>
                                </li>
                            );
                        })
                    ) : (
                        <Empty description="暂无数据"></Empty>
                    )}
                </ul> */}
            </div>
        </Draggable>
    );
});
export default Dotting;
