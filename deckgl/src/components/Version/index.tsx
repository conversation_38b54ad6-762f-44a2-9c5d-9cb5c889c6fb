/*
 * @Author: fanmixue <EMAIL>
 * @Date: 2023-02-01 19:25:52
 * @LastEditors: jack 501177081.com
 * @LastEditTime: 2025-07-23 11:21:15
 * @FilePath: /deckgl/src/components/Version/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { useState, useEffect } from "react";
import { Modal } from "antd";
import "./index.less";
import ReactMarkdown from "react-markdown";
import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { load } from "protobufjs";
import axios from "axios";
import { useTranslation } from "react-i18next";
const articles = {
    "1": "version.md",
};
function Version({
    displayModal,
    changeDisplayVersion,
}: {
    displayModal: boolean;
    changeDisplayVersion: React.Dispatch<React.SetStateAction<boolean>>;
}) {
    const { t } = useTranslation("version");
    const config = useSelector((state: RootState) => state.dataReducer.config);
    const [currentArticle, setCurrentArticle] = useState<any>();
    const handleCancel = () => {
        changeDisplayVersion(false);
    };
    const changeCurrentArticle = async (url: string) => {
        const res = await fetch(url);
        const content = await res.text();
        setCurrentArticle(content);
    };
    const [version, setVersion] = useState("");
    useEffect(() => {
        if (displayModal) {
            const baseUrl =
                "http://" +
                config.connectConfig.ip +
                ":" +
                (config.connectConfig.versionPort || 9080) +
                "/api/version/v2/";
            changeCurrentArticle(articles["1"]);

            axios
                .get(baseUrl, {
                    responseType: "arraybuffer",
                })
                .then((res) => {
                    load("protobuf/version.proto", (err: any, root: any) => {
                        if (err) {
                            console.log(err, "error");
                        } else {
                            // pb文件解析
                            const AwesomeMessage =
                                root.lookupType("trunk.msgs.Ver");
                            const data = AwesomeMessage.decode(
                                new Uint8Array(res.data)
                            );
                            const ver = AwesomeMessage.toObject(data);
                            setVersion(ver.version);
                        }
                    });
                })
                .catch((error) => {
                    console.log(error);
                });
        }
    }, [displayModal]);

    return (
        <Modal
            title={t("title")}
            open={displayModal}
            onCancel={handleCancel}
            footer={null}
            mask={false}
            centered={true}
            className={`version-dialog ${config.theme}`}
        >
            <div className="markdown-body">
                <h1>MapSets: v{__APP_VERSION__}</h1>
                <h2>{config.parentApp}</h2>
                <h2>{version}</h2>

                <ReactMarkdown children={currentArticle} />
            </div>
        </Modal>
    );
}

export default Version;
