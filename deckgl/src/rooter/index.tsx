/*
 * @Author: luofei luofei.trunk.tech
 * @Date: 2023-03-23 13:33:39
 * @LastEditors: luofei <EMAIL>
 * @LastEditTime: 2024-03-12 13:55:48
 * @FilePath: /deckgl/src/rooter/index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */

import { createHashRouter, RouterProvider } from "react-router-dom";

import App from "../App";
import Calibration from "@/view/Calibration";
import Demo from "@/view/Demo/boby";

const router = createHashRouter([
    {
        path: "/",
        element: <App></App>,
    },
    {
        path: "/Calibration",
        element: <Calibration></Calibration>,
    },
    {
        path: "/Demo",
        element: <Demo></Demo>,
    },
]);

function Rooter() {
    return <RouterProvider router={router} />;
}

export default Rooter;
