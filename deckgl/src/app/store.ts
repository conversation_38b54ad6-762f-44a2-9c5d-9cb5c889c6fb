/*
 * @Author: luofei <EMAIL>
 * @Date: 2022-08-30 18:56:05
 * @LastEditors: jack <EMAIL>
 * @LastEditTime: 2024-10-22 16:18:35
 * @FilePath: /deckgl/src/app/store.ts
 * @Description:
 *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import {
    configureStore,
    createSerializableStateInvariantMiddleware,
} from "@reduxjs/toolkit";
import dataReducer from "../features/dataSlice";

const store = configureStore({
    reducer: {
        dataReducer,
    },
    // 关闭序列化检查
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            serializableCheck: false,
            gnoredActions: ["dataReducer/updateData"],
        }),

    // 优化
    // middleware: (getDefaultMiddleware) =>
    //     getDefaultMiddleware({
    //         serializableCheck: {
    //             ignoredActions: ["dataReducer/setData"],
    //         },
    //     }),
});
export default store;

export type RootState = ReturnType<typeof store.getState>;
