# 主线通用地图播放器 - 架构图说明

## 🏗️ 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    mapsets 项目根目录                            │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   deckgl        │   MapTalks      │        Threejs              │
│  (主要模块)      │  (地图引擎)      │      (3D渲染)               │
│                 │                 │                             │
│ React+TypeScript│   Vue 3         │   React+Three.js            │
│ DeckGL+Redux    │   MapTalks      │   React Three Fiber         │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

## 🎯 DeckGL 主模块详细架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        DeckGL 主模块                            │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │    React    │    │   Redux     │    │   DeckGL    │         │
│  │   组件层     │◄──►│   状态管理   │◄──►│   渲染引擎   │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
│         │                   │                   │              │
│         ▼                   ▼                   ▼              │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │ 用户界面组件  │    │  数据流管理  │    │  图层渲染    │         │
│  │             │    │             │    │             │         │
│  │ • Home      │    │ • dataSlice │    │ • GeoJSON   │         │
│  │ • Container │    │ • settings  │    │ • PointCloud│         │
│  │ • InfoPanel │    │ • operation │    │ • Scenegraph│         │
│  │ • MapTools  │    │             │    │ • MapBox    │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│                        数据输入层                                │
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │ ROS 数据源   │    │  地图数据    │    │  3D 模型    │         │
│  │             │    │             │    │             │         │
│  │ • WebSocket │    │ • GeoJSON   │    │ • GLB 文件  │         │
│  │ • Protobuf  │    │ • 瓦片地图   │    │ • 纹理贴图  │         │
│  │ • 点云数据   │    │ • 矢量数据   │    │ • 材质配置  │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
└─────────────────────────────────────────────────────────────────┘
```

## 🔄 数据流架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ ROS 数据源   │───►│  WebSocket  │───►│ Protobuf    │───►│ Redux Store │
│             │    │   连接      │    │   解析      │    │   状态管理   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                                                                  │
                                                                  ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  地图数据    │───►│  GeoJSON    │───►│  地图图层    │◄───│ React 组件  │
│             │    │   格式      │    │   渲染      │    │   更新      │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                                                                  │
                                                                  ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  3D 模型    │───►│  GLB 文件   │───►│ Scenegraph  │◄───│ DeckGL 渲染 │
│             │    │   加载      │    │   图层      │    │   输出      │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

## 🎨 用户界面层次结构

```
Home (主界面)
├── Header (顶部导航)
├── Container (主容器)
│   ├── Core3DViewer (3D地图视图)
│   │   ├── MAPGL (地图组件)
│   │   │   ├── DeckGL (渲染引擎)
│   │   │   ├── Map (底图)
│   │   │   └── Layers (图层)
│   │   └── MapTools (地图工具)
│   │       ├── ViewMode (视角控制)
│   │       ├── Measure (测量工具)
│   │       ├── Debug (调试面板)
│   │       └── Legend (图例)
│   ├── RosPlayer (ROS播放器)
│   │   ├── PointCloud (点云渲染)
│   │   ├── Controls (播放控制)
│   │   └── Settings (设置面板)
│   └── InfoPanel (信息面板)
│       ├── Dashboard (仪表盘)
│       │   ├── CarInfo (车辆信息)
│       │   ├── VehicleInfo (车辆状态)
│       │   └── RunInfo (运行信息)
│       ├── Topic (话题信息)
│       ├── TrafficLight (交通灯)
│       ├── Fault (故障信息)
│       └── Videos (视频显示)
├── MosaicTile (瓦片布局)
├── RightMenu (右侧菜单)
├── Console (控制台)
└── ReplayControls (回放控制)
```

## 🔧 核心组件关系

```
┌─────────────────────────────────────────────────────────────────┐
│                      Core3DViewer                              │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                     MAPGL                               │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │   │
│  │  │   DeckGL    │  │    Map      │  │   Layers    │     │   │
│  │  │   渲染核心   │  │   底图组件   │  │   图层管理   │     │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │   │
│  └─────────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   MapTools                              │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │   │
│  │  │   工具栏     │  │   测量工具   │  │   调试面板   │     │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## 📊 技术栈对比

| 模块 | 框架 | 地图引擎 | 3D渲染 | 状态管理 | 构建工具 |
|------|------|----------|--------|----------|----------|
| **DeckGL** | React 18 | DeckGL + MapLibre | Three.js | Redux Toolkit | Vite |
| **MapTalks** | Vue 3 | MapTalks | Three.js | Vuex | Vite |
| **Threejs** | React 18 | - | React Three Fiber | - | Vite |

## 🚀 性能架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        性能优化层                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │  WebWorker  │    │    LOD      │    │  内存管理    │         │
│  │  数据处理    │    │  细节层次    │    │  垃圾回收    │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
│         │                   │                   │              │
│         ▼                   ▼                   ▼              │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │  异步加载    │    │  视锥剔除    │    │  对象池      │         │
│  │  分块渲染    │    │  距离剔除    │    │  资源复用    │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│                        渲染管线                                  │
│                                                                 │
│  数据输入 → 预处理 → 图层构建 → WebGL渲染 → 屏幕输出              │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 🔗 模块间通信

```
┌─────────────┐    HTTP/WebSocket    ┌─────────────┐
│   DeckGL    │◄────────────────────►│  后端服务    │
│   主模块     │                      │             │
└─────────────┘                      └─────────────┘
       │                                     ▲
       │ 组件通信                             │
       ▼                                     │ 数据同步
┌─────────────┐    消息传递/事件     ┌─────────────┐
│  MapTalks   │◄────────────────────►│  Threejs    │
│   模块      │                      │   模块      │
└─────────────┘                      └─────────────┘
```

---

*本架构图说明文档配合项目分析报告使用*
*生成时间: 2025-06-27*
