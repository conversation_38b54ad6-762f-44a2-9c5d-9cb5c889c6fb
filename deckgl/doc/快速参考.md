# 主线通用地图播放器 - 快速参考

## 🚀 快速启动

### 开发环境启动
```bash
# 进入主模块
cd deckgl

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问地址: http://localhost:8080
```

### 其他模块启动
```bash
# MapTalks 模块
cd MapTalks && npm run dev

# Threejs 模块  
cd Threejs && npm run dev
```

## 📁 关键文件位置

### 核心组件
```
deckgl/src/
├── App.tsx                    # 应用入口
├── view/Home/index.tsx        # 主页面
├── components/
│   ├── Core3DViewer/         # 3D地图视图
│   ├── Map/index.tsx         # 地图组件
│   ├── RosPlayer/            # ROS播放器
│   ├── InfoPanel/            # 信息面板
│   └── MapTools/             # 地图工具
```

### 配置文件
```
deckgl/
├── package.json              # 项目依赖
├── vite.config.ts           # 构建配置
├── tsconfig.json            # TypeScript配置
└── public/
    ├── protobuf/            # Protobuf定义
    └── geojson/             # 地图数据
```

## 🔧 常用命令

### 开发命令
```bash
npm run dev                   # 开发模式
npm run build                # 生产构建
npm run build:mapsets        # 组件库构建
npm run preview              # 预览构建结果
```

### 数据更新命令
```bash
npm run protobuf             # 更新Protobuf定义
npm run downloadMap          # 下载地图数据
npm run fitpb                # 更新协议文件
```

### 部署命令
```bash
npm run scp                  # 构建并部署到服务器
```

## 🎯 核心API

### Redux Store 结构
```typescript
interface RootState {
  dataReducer: {
    data: any;                # 主要数据
    config: MapConfig;        # 地图配置
    settings: Settings;       # 用户设置
    operation: Operation;     # 操作状态
    status: Status;          # 连接状态
    currentReplayBagInfo: any; # 回放信息
  }
}
```

### 主要Actions
```typescript
// 数据管理
setData(data)                # 设置主要数据
setConfig(config)            # 设置地图配置
setSettings(settings)        # 设置用户偏好
setOperation(operation)      # 设置操作状态

// 回放控制
setCurrentReplayBagInfo(info) # 设置回放信息
```

### 地图组件Props
```typescript
interface MapProps {
  data: any;                 # 渲染数据
  config: MapConfig;         # 地图配置
  settings: Settings;        # 用户设置
  truckDebugInfo: any;       # 调试信息
  _onclickFunc: Function;    # 点击回调
  _onHoverFunc: Function;    # 悬停回调
}
```

## 🗺️ 图层类型

### DeckGL 图层
```typescript
// 地图底图
GeoJsonLayer              # GeoJSON矢量图层
TileLayer                 # 瓦片图层

// 3D渲染
ScenegraphLayer           # 3D模型图层
PointCloudLayer           # 点云图层

// 数据可视化
PathLayer                 # 路径图层
IconLayer                 # 图标图层
TextLayer                 # 文本图层
ScatterplotLayer          # 散点图层
```

### 图层配置示例
```typescript
new ScenegraphLayer({
  id: 'vehicles',
  data: vehicleData,
  scenegraph: '/glb/truck.glb',
  getPosition: d => d.position,
  getOrientation: d => d.orientation,
  getScale: [1, 1, 1],
  _lighting: 'pbr'
});
```

## 🔌 ROS 通信

### WebSocket 连接
```typescript
// 连接配置
const rosConfig = {
  url: 'ws://localhost:9090',
  topics: [
    '/points',              # 点云数据
    '/visualization',       # 可视化数据
    '/tf',                  # 坐标变换
  ]
};
```

### 数据订阅
```typescript
// 点云订阅
const pointCloudTopic = new ROSLIB.Topic({
  ros: ros,
  name: '/points',
  messageType: 'sensor_msgs/PointCloud2'
});

pointCloudTopic.subscribe(callback);
```

## 🎨 主题配置

### 主题切换
```typescript
// 支持的主题
type Theme = 'light' | 'dark';

// 主题配置
const themeConfig = {
  light: {
    background: '#ffffff',
    text: '#000000',
    mapStyle: BASEMAP.POSITRON
  },
  dark: {
    background: '#000000', 
    text: '#ffffff',
    mapStyle: BASEMAP.DARK_MATTER
  }
};
```

## 📊 性能监控

### 关键指标
```typescript
// 渲染性能
const metrics = {
  fps: 60,                  # 目标帧率
  drawCalls: '<100',        # 绘制调用次数
  triangles: '<1M',         # 三角形数量
  memory: '<512MB'          # 内存使用
};
```

### 性能优化开关
```typescript
const performanceSettings = {
  useDevicePixels: true,    # 设备像素比
  pickable: false,          # 拾取功能
  updateTriggers: {},       # 更新触发器
  transitions: false        # 过渡动画
};
```

## 🐛 调试技巧

### 开发者工具
```typescript
// 启用调试模式
const debugConfig = {
  debug: true,              # 调试面板
  console: true,            # 控制台输出
  stats: true,              # 性能统计
  wireframe: false          # 线框模式
};
```

### 常用调试命令
```javascript
// 浏览器控制台
window.__REDUX_STORE__     # 访问Redux状态
window.__DECK_GL__         # 访问DeckGL实例
window.__THREE__           # 访问Three.js场景
```

## 🔧 故障排除

### 常见问题

#### 1. 地图不显示
```bash
# 检查网络连接
curl http://localhost:8080/geojson/map.json

# 检查控制台错误
# 确认MapBox token配置
```

#### 2. 点云不渲染
```bash
# 检查ROS连接
# 确认WebSocket状态
# 验证数据格式
```

#### 3. 3D模型不加载
```bash
# 检查GLB文件路径
# 确认Draco解码器
# 验证模型格式
```

### 日志级别
```typescript
const logLevels = {
  ERROR: 0,    # 错误信息
  WARN: 1,     # 警告信息  
  INFO: 2,     # 一般信息
  DEBUG: 3     # 调试信息
};
```

## 📱 移动端适配

### 响应式配置
```typescript
const mobileConfig = {
  isMobile: window.innerWidth < 768,
  touchControls: true,
  simplifiedUI: true,
  reducedQuality: true
};
```

### 性能优化
```typescript
const mobileOptimizations = {
  overAlloc: 1,            # 内存分配
  poolSize: 0,             # 对象池大小
  devicePixelRatio: 1      # 像素比限制
};
```

---

*快速参考文档 - 便于日常开发查阅*
*更新时间: 2025-06-27*
