<!--

 * @Author: luo<PERSON><PERSON> luofei.trunk.tech
 * @Date: 2022-11-07 17:13:52
 * @LastEditors: luofei <EMAIL>
 * @LastEditTime: 2024-03-06 16:07:08
 * @FilePath: /deckgl/README.md
 * @Description:
   *
 * Copyright (c) 2022 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
   -->

# 通用播放器 MapSets v0.1.0

## 使用背景

兼容公司所有项目 HMT 可视化组件，统一管理升级，组件定制。

高性能：cpu 内存 占用低，在 linux macos windows 均有不错的性能表现。采用浏览器多线程 webworker，所有计算逻辑在 worker 内运行，避免 UI 渲染主线程阻塞。

可拓展：支持 pc 平板 移动端程序移植

简易移植：任意 web 工程只需引入 iframe 传入数据即可，不需要大动主项目，破坏力小

## 已适配程序

1. TrunkGuan
2. TrunkSnake
3. TrunkVerse

## 使用场景

1. 主应用适配

    1. 接入 iframe
    2. 用过**postMessage** 将数据传入子应用

    ```json
    {
    	// 消息类型：1.数据 2.配置 3.操作
      type:"data"| "config" | "operation"

      // 数据来源：THMI SNAKE VERSE
      origin："HMI" | "SNAKE" | "EXTEND"

     // 原始数据
      message:{}
    }
    ```

2. 公共播放器

    通过 URL 传参： http://127.0.0.1:8080/?type=GUAN&ip=**********

    参数说明：

    1. type：制定适配程序

    2. ip：后端数据 ip

3. 第三方对接文档

    请参考： https://trunk.feishu.cn/wiki/wikcnHqHJsmVhSjEpz72hDCpDxf?from=from_copylink

## 快捷键

### pc

1. 左键：改变地图位置
2. 右键：改变地图视角
3. 滚轮：地图缩放
4. 按键"O" ：调节模型透明
5. 按键"2" ：调节地图视角为 2D
6. 按键"3" ：调节地图视角为 3D
7. 按键"D" ：打开调试模式 地图工具
8. 按键"R" ：重置视图
9. 按键"L" ：视图锁定
10. 按键"M" ：测量
11. 按键"H" ：打开 | 关闭图例
12. 按键"S" ：打开 | 关闭卫星地图
13. 按键"C" ： 打开 | 关闭控制台
14. 按键"Ctrl" && "M" ：打开 | 关闭菜单

### 移动端

1. 单指：改变地图位置
2. 双指：地图缩放
3. 三指：地图视角

## V0.1.0 版本信息

### 地图可视化

1. map

    1. 自动驾驶高精度地图
    2. osm 导航地图
    3. 3D 街景经纬度地图

2. 可视化元素

    1. 模型（已校准）

        1. 自车模型
            1. 集卡
            2. 集卡车头
            3. 集卡车挂
            4. ART 平板
        2. 行人
        3. 自行车
        4. 机动车

    2. 模型包络（3D）

    3. marker （模型 title）

    4. 静态障碍物点云

    5. 动态障碍物包络（模拟 3D）

    6. 实时行驶路径

    7. 实时行驶路点

    8. 可行驶区域

## 项目目录结构

```
├── public/                      # 静态资源
│   ├── glb/                     # 3D模型文件
│   ├── geojson/                 # 地图数据文件
│   ├── protobuf/                # protobuf定义文件
│   ├── json/                    # 静态JSON数据文件
│   ├── mp3/                     # 音频文件
│   ├── geojsonImg/              # 地图相关图片资源
│   └── draco/                   # 3D模型压缩相关文件
│
├── src/                         # 源代码
│   ├── app/                     # 应用状态管理
│   ├── assets/                  # 静态资源
│   ├── components/              # 组件目录
│   │   ├── Map/                 # 地图相关组件
│   │   │   ├── layers/          # 地图图层
│   │   │   ├── MapTools/        # 地图工具
│   │   │   ├── MapBox/          # MapBox封装
│   │   │   ├── DrawToos/        # 绘图工具
│   │   │   └── GeoJSONTools/    # GeoJSON处理工具
│   │   ├── Core3DViewer/        # 3D视图组件
│   │   ├── PointCloud/          # 点云组件
│   │   ├── ReplayControls/      # 回放控制组件
│   │   ├── VideoPlayer/         # 视频播放组件
│   │   ├── InfoPanel/           # 信息面板组件
│   │   ├── Debug/               # 调试工具组件
│   │   ├── Console/             # 控制台组件
│   │   └── ...                  # 其他组件
│   │
│   ├── view/                    # 视图页面
│   │   ├── Home/                # 主页面
│   │   ├── Demo/                # 演示页面
│   │   └── Calibration/         # 校准页面
│   │
│   ├── hooks/                   # 自定义React Hooks
│   │   ├── useEditableGeoJsonLayer.ts  # GeoJSON编辑Hook
│   │   └── useModels.ts                # 3D模型Hook
│   │
│   ├── utils/                   # 工具函数
│   │   ├── mapTools.js          # 地图工具函数
│   │   ├── ros/                 # ROS通信工具
│   │   ├── utm.js               # UTM坐标转换
│   │   └── ...                  # 其他工具函数
│   │
│   ├── workers/                 # Web Worker线程
│   ├── types/                   # TypeScript类型定义
│   ├── data/                    # 数据处理
│   ├── features/                # 特性模块
│   ├── request/                 # 网络请求
│   ├── rooter/                  # 路由配置
│   ├── theme/                   # 主题配置
│   ├── consconts.ts             # 常量定义
│   ├── main.tsx                 # 应用入口
│   └── App.tsx                  # 主应用组件
│
├── .cursorrules                 # Cursor编辑器规则配置
├── package.json                 # 项目依赖配置
├── vite.config.ts               # Vite配置
├── tsconfig.json                # TypeScript配置
├── tsconfig.node.json           # Node TypeScript配置
└── index.html                   # HTML入口文件
```
