/*
 * @Author: luofei <EMAIL>
 * @Date: 2023-11-15 11:16:23
 * @LastEditors: luofei <EMAIL>
 * @LastEditTime: 2024-09-10 10:43:25
 * @FilePath: \deckgl\vite.config.ts
 * @Description: *
 * Copyright (c) 2024 by 北京主线科技有限公司京ICP备19025226号-1, All Rights Reserved.
 */
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import px2vw from "postcss-px-to-viewport";
import libCss from "vite-plugin-libcss";
import typescript from "@rollup/plugin-typescript";
import path from "path";

function resolve(str: string) {
    return path.resolve(__dirname, str);
}
// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
    return {
        plugins: [
            react(),
            libCss(), //组件自动引入 css
            typescript({
                //vite 的生产构建使用的是 rollup，以目前的配置只会构建出 js 代码,对于 typescript 类型，需要借助 rollup 的 typescript 插件来实现
                target: "es5",
                rootDir: resolve("packages/"),
                declaration: true,
                declarationDir: resolve("dist"),
                exclude: resolve("node_modules/**"),
                allowSyntheticDefaultImports: true,
            }),
        ],
        server: {
            host: "0.0.0.0",
            port: 8080,
        },
        resolve: {
            alias: {
                "@": "/src",
                theme: "@/theme/",
            },
        },
        css: {
            postcss: {
                plugins: [
                    px2vw({
                        unitToConvert: "px",
                        viewportWidth: 1920,
                        viewportHeight: 1080, // 视窗的高度
                        unitPrecision: 6, // 转换后的精度，即小数点位数
                        propList: ["*"],
                        selectorBlackList: ["reset-", "lil-", "hmi-"], // 指定不转换为视窗单位的类名，
                        mediaQuery: false, //是否解析媒体查询
                    }),
                ],
            },
        },
        define: {
            __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
        },
        build:
            mode == "mapsets"
                ? {
                      outDir: "dist", // 输出文件夹
                      copyPublicDir: false, //是否将 publicDir 目录中的所有文件复制到 outDir 目录中
                      lib: {
                          // 组件库源码的入口文件
                          entry: resolve(
                              "src/components/Map/Packages/index.tsx"
                          ),
                          // 组件库名称
                          name: "Mapsets",
                          // 文件名称, 打包结果举例: xxx.cjs
                          fileName: "mapsets",
                          // 打包格式
                          formats: ["es", "cjs"],
                      },
                      rollupOptions: {
                          //排除不相关的依赖 以下模块不会被打包进输出文件中,
                          external: [
                              "react",
                              "react-dom",
                              "react-redux",
                              "antd",
                          ],
                          output: {
                              // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
                              globals: {
                                  react: "react",
                                  "react-dom": "react-dom",
                              },
                          },
                      },
                  }
                : {},
    };
});
