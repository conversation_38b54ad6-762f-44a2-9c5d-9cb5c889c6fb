syntax = "proto3";
package trunk.msgs.v2;

import "common.proto";
import "geometry.proto";

/*****************************************************
 *              通用自动驾驶功能接口                 *
 *****************************************************/

// 途经点 - 用于设置终点
message WayPoints {
  message Point {
    enum Type {
      NOT_SPECIFIED = 0; // 未指定
      UP_DETOUR = 1;     // 上岸桥绕行
      DOWN_DETOUR = 2;   // 下岸桥绕行
    }
    double x = 1;
    double y = 2;
    Type type = 3;       // 途径点类型
  }
  string navi_id = 1;                        // 指令id
  string up_vpb = 2;                         // 上岸桥 vpb: Virtual Parallel buffer虚拟并行缓冲区
  string down_vpb = 3;                       // 下岸桥
  uint32 route_direction = 4;                // 上/下岸桥方向 0: 单向岸桥 1: 顺时针 2: 逆时针
  uint32 dest_type = 5;                      // 目的地类型
  repeated Point2D way_points = 6;
  repeated Point way_points_v2 = 7;
  bool need_crane_lane_change = 8;           // 是否开启岸桥下换道(宁波)
}

// 停车模式 - 用于实现车辆急停/缓停
message ParkingMode {
  uint32 id = 1;
  double x = 2;
  double y = 3;
  double yaw = 4;
  uint32 type = 5;                           // 停车类型 0:不停车 1: 缓停(往前行驶15米后停车) 2: 急停 3: 目标点停车(到达目标点后, 缓停)
}

// 相对位置(仅纵向) - 用于实现相对位置调整功能
message RelativePosition {
  uint32 id = 1;                             // id
  double distance = 2;                       // 纵向移动距离
  double precision = 3;                      // 精度要求
}

// 绝对位置 - 用于实现绝对位置调整功能
message AbsolutePosition {
  uint32 id = 1;
  double x = 2;
  double y = 3;
  double yaw = 4;
}

// 电池控制 - 用于控制集卡上下高压
message BatteryControl {
  string truck_id = 1;                       // 集卡id
  uint32 code = 2;                           // 0: 上高压 1: 下高压
}

// 路段限速 - 用于实现区域限速
message SpeedLimit {
  uint32 id = 1;                             // id
  double current_speed_limit = 2;            // 当前限速
  double next_speed_limit = 3;               // 下一个限速
  double distance_to_next_speed_limit = 4;   // 下一个限速点距离
}

// 安全车距 - 用于实现密停场景下停车间距更改
message SafeDistance {
  uint32 id = 1;
  double distance = 2;                       // 安全车距
}

// 车辆控制信息 - 用于实现被动引导功能
message Control {
  double steering_angle_front = 1;                    // 前轮转角 °
  double steering_angle_rear = 2;                     // 后轮转角 °
  double steering_speed_front = 3;                    // 前轮速度 m/s
  double steering_speed_rear = 4;                     // 后轮速度 m/s
  double distance = 5;                       // 距离 m
  double throttle = 6;                       // 油门踏板开度 0.0 ~ 1.0
  double brake = 7;                          // 制动踏板开度 0.0 ~ 1.0
  uint32 gear = 8;                           // 档位 0:P 1:R 2:N 3:D
  uint32 lat_mode = 9;                       // 转向模式设置
  uint32 long_mode = 10;                     // 纵向模式设置
}

// 局部地图 - 用于为自动驾驶提供局部地图
message LocalMap {
  string navi_id = 1;
  double stop_distances = 2;                 // 剩余距离
  repeated Section sections = 3;
}

// 天气 - 用于调整定位和感知的参数
message Weather {
  enum Mode {
    SUNNY = 0;        // 晴天
    LIGHT_RAIN = 1;   // 小雨/雪
    HEAVY_RAIN = 2;   // 大雨/雪
    FOGGY = 3;        // 雾天
  }
  repeated Mode mode = 1;
}

// 禁行区 - 用于地图规划路径时禁止驶入的路段
message ClearArea {
  repeated string ids = 1;                  // 空数组+通行模式: 清空所有禁行区
  uint32 type = 2;                          // 0. lane 1: section 2: road 3: bridge
  uint32 mode = 3;                          // 0: 通行 1: 禁行
}

// 数据记录
message Record {
  bool flag = 1;                            // true: 记录 false: 忽略
}

// guardian复位开关
message ResetSwitch {
  bool flag = 1;
}

// 灯光开关 - 用于控制实现车辆灯光控制
message Light {
  uint32 mode = 1;                          // bit 1: 左转向灯 2: 右转向灯 3: 双闪 4: 刹车灯 5: 前行车灯 6: 后行车灯 7: 近光灯
                                            //     8: 远光灯 9: 前雾灯 10: 后雾灯 11: 挂车雾灯 12: 示廓灯 13: 前照灯 14: 后照灯
                                            //     15: 危险报警顶灯 16: 对位红顶灯 17: 对位蓝顶灯
}

// 车身附件开关 - 用于实现语音、喇叭和雨刷等车身附件控制
message BcmAppendix {
  uint32 mode = 1;                          // bit 1: 电喇叭 2: 气喇叭 3: 低速雨刷 4: 高速雨刷 5: 玻璃水 6: 倒车提示音 7: 车门1 8: 车门2 9: 车速报警 10: 车辆已到达语音 11: 车辆已锁车语音 12: 雷达下电
}

// 车速级别 - 用于调整pnc的参数
message SpeedLevel {
  uint32 mode = 1;                           // 0: 正常速度 1: 中速 2: 低速
}

// 局部地图路径 - 用于云端路径规划
message Path {
  message Lane {
    string id = 1;
    int32 path_index = 2;
    int32 start_index = 3;
    int32 end_index = 4;
  }
  message Section {
    string id = 1;
    double distance_to_end = 2;
    repeated Lane lanes = 3;
  }
  message Trajectory {
    message CurvePoint {
      double x   = 1;
      double y   = 2;
      double z   = 3;
      double yaw = 4;    // yaw
      double v   = 5;    // 速度
    }
    message BoundaryPoint {
      double x = 1;
      double y = 2;
      double z = 3;
    }
    repeated CurvePoint curve = 1;             // 中心线
    repeated BoundaryPoint left_boundary  = 2; // 左边界
    repeated BoundaryPoint right_boundary = 3; // 右边界
  }
  message Region {
    string name      = 1; // 当前车道语义
    int32 type       = 2; // 当前车道类型
    int32 turn_type  = 3; // 前方N米车道转弯类型
  }
  Header header = 1;
  string navi_id = 2;
  repeated string version = 3;               // 地图文件版本号
  repeated Section sections = 4;
  double remaining_distance = 5;
  repeated Point2D way_points = 6;           // 相关途经点
  Trajectory trajectory = 7;                 // 轨迹点
  Region region = 8;                         // current region
}

message TruckContainer {
  message Container {
    int32 size = 1;                         // 箱子尺寸
    double weight = 2;                      // 箱子重量
  }
  repeated Container containers = 1;
  bool enable = 2;                          // 设置PnC是否开启重载逻辑(False:不考虑箱重 True:考虑箱重)
}

// 开关 - 用于设置车辆开关
message Switches {
  enum State {
    OFF = 0;
    ON = 1;
    UNKNOWN = 2;
  }
  State enter_autonomous_driving = 1;         // ON: 进入自动驾驶 OFF/UNKNOWN: 无效操作
}

// 强行换道 - 用于云端下发强行换道请求
message ForceLaneChange {
  message Target {
    Point2D start_point = 1;                   // 变道起始点
    Point2D end_point = 2;                     // 变道结束点
  }
  string navi_id = 1;                          // 与local_map里的navi_id一致
  repeated Target target = 2;
}

// 吊具避让、防拖拽开关指令
message TaskCraneInfo {
  uint32 crane_cmd = 1;                 // 吊具避让开关指令 default: 0, clear running_state : 1, close crane detection : 2, open crane detection : 3;
  uint32 drag_cmd = 2;                  // 吊具防拖拽开发指令 default: 0, clear running_state : 1, close drag detection : 2, open drag detection : 3;
}

message SensorControl {
  enum DeviceType {
    LIDAR = 0;     // 雷达
    CAMERA = 1;    // 相机
  }
  enum DeviceState {
    OFF = 0;       // 关机
    ON = 1;        // 开机
    SLEEP = 2;     // 休眠
    WAKE = 3;      // 唤醒
  }
  DeviceType device_type = 1;        // 传感器类型
  DeviceState device_state = 2;      // 设备状态
}

// 清除底盘故障指令
message ClearChassisErr {
  bool reset_cmd = 1;
}

// 锁站按钮状态
message LockStationBcm {
  uint32 cmd = 1;                    // 0:获取 1:重置
  repeated uint32 status = 2;        // 按钮状态集合(bit0位表示左前 bit1位表示右前 bit2位表示左后 bit3位表示右后)
}

message Reply {
  int32 code = 1;                            // 状态码
  string msg = 2;                            // 消息描述
}

service AdsCommandService {
  rpc SetWayPoints(WayPoints) returns (Reply);
  rpc Park(ParkingMode) returns (Reply);
  rpc SetRelativePosition(RelativePosition) returns (Reply);
  rpc SetAbsolutePosition(AbsolutePosition) returns (Reply);
  rpc SetBatteryControl(BatteryControl) returns (Reply);
  rpc SetSpeedLimit(SpeedLimit) returns (Reply);
  rpc SetSafeDistance(SafeDistance) returns (Reply);
  rpc ResetLocalization(PoseStamped) returns (Reply);
  rpc PassiveGuide(Control) returns (Reply);
  rpc SetLocalMap(LocalMap) returns (Reply);
  rpc SetWeather(Weather) returns (Reply);
  rpc SetClearArea(ClearArea) returns (Reply);
  rpc TriggerRecord(Record) returns (Reply);
  rpc ResetGuardian(ResetSwitch) returns (Reply);
}
