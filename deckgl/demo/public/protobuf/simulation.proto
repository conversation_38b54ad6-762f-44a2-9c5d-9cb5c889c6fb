syntax = "proto3";

package trunk.msgs.v2;

import "geometry.proto";

// 仿真后端服务器状态
message ServerState {
    string type = 1;
    string msg = 2;
    float ping_value = 3;
}

// 包络信息
message TrunkBounding {
    double yaw = 1;  // 包络的 yaw
    double relevant_yaw = 2;  // 相对于前面车挂或者车头的 yaw
    repeated Point3D points = 3; //  包络边界点
}

message Box {
    repeated Point3D points = 1;
}

// entity 的属性信息
message EntityInfo {
    string id = 1;
    string type = 2;
    string name = 3;
    string model_name = 4;
    Point3D pose = 5;
    Point3D speed = 6;
    double yaw = 7;
    double distance = 8;
    bool is_focus = 9;
    repeated TrunkBounding bounding = 14;
    repeated Box safety_boundary_bounding = 15; //  boundary metric边界点
}

