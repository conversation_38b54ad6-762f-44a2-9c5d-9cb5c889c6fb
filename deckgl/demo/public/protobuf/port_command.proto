syntax = "proto3";
package trunk.msgs.v2;

import "geometry.proto";

/*****************************************************
 *                     港口业务接口                  *
 *****************************************************/


message QuayCranes {
  message QuayCrane {
    string qc_id = 1;                     // 岸桥位置
    uint32 in_position = 2;               // 岸桥是否落位 0: 岸桥未落位 1: 岸桥落位
    Point3D center_point = 3;             // 岸桥中心点坐标
    Point3D left_point = 4;               // 岸桥左腿坐标 b
    Point3D right_point = 5;              // 岸桥右腿坐标 a
  }
  repeated QuayCrane quay_cranes = 1;
}

// 待检测物体方位
message ObjPosition {
  string bridge_id = 1;                 // 物体id
  uint32 task_type = 2;                 // 任务类型 0:default 1:charger 2:quay bridge 3:yard bridge 
                                        //          4:quay_left 5:quay_right 6:yard_left 7:yard_right
}

// 车顶警示灯
message Alert {
  uint32 code = 1;                      // 0:None 1: Aliging
}

// 对位标志物
message AlignmentMark {
  message StopLines {
    uint32 task_type = 1;               // 0: default 1: charger 2: quay_bridge 3: yard_bridge 4: quay_left 5: quay_right 6: yard_left 7: yard_right
    uint32 mode = 2;                    // 0b0000: invalid 0b1xxx: id_valid 0bx1xx: x_valid 0bxx1x: y_valid 0bxxx1: yaw_valid
    repeated uint32 id = 3;
    repeated double x = 4;
    repeated double y = 5;
    repeated double yaw = 6;
  }

  message Container {
    bool empty = 1;
    double position = 2;
  }

  StopLines tag = 1;                    // 二维码
  StopLines beam_column = 2;            // 反光柱/横梁
  Container container = 3;              // 集装箱
}

// 狭窄区域信息，旨在引导车辆通过狭窄空间，例如锁站
message NarrowSpaceInfo {
  string id = 1;                        // 锁站id
  Point4D pose = 2;                     // 目标点位姿
  float distance = 3;                   // 距目标点距离
  bool validity = 4;                    // 数据有效性
}
