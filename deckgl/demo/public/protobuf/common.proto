syntax = "proto3";
package trunk.msgs.v2;

import "geometry.proto";

message Lane {
  int32 id = 1;                                  // lane id
  double speed_limit = 2;                        // 当前限速
  uint32 lane_type = 3;                          // 车道类型
  uint32 turn_type = 4;                          // 车道转弯类型 0: 直行车道 1: 左转车道 2: 右转车道 3: 掉头车道
  repeated uint32 boundary_type = 5;             // 边界线类型[左边界, 右边界]   0: 未知 1: 实线 2: 虚线 3: 路边 4: 波茨点 5: 虚拟线
  repeated uint32 boundary_color = 6;            // 边界线颜色[左边界, 右边界]   0: 未知 1: 白色 2: 黄色
  repeated PointXD center_line = 7;              // 中心线
  repeated Point3D left_line = 8;                // 左边界线
  repeated Point3D right_line = 9;               // 右边界线
  int32 goal_index = 10;                         // 目标车道索引
  int32 length = 11;                             // 车道长度
  repeated uint32 boundary_virtual = 12;         // 边界线虚拟类型[左边界, 右边界] 0: 正常边界 1: 虚拟边界
}

message Section {
  int32 id = 1;                                  // section id
  repeated Lane lanes = 2;
  int32 type = 3;
}
