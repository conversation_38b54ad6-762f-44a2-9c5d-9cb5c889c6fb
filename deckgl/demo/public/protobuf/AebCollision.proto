syntax = "proto3";
package trunk.msgs;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

message AebCollision {
  google.protobuf.Timestamp timestamp = 1;
  bool collision = 2;      // true:触发 false:未触发
  bool aeb_is_on = 3;      // true:使用aeb false:不使用aeb
  uint32 trigger_type = 4;  // 触发类型 0:未触发 1:即将碰撞 2:输入异常
}

service AebCollisionService {
  rpc getAebCollision(google.protobuf.Empty) returns (AebCollision) {}
}
