syntax = "proto3";
package trunk.msgs.v2;

import "geometry.proto";
import "google/protobuf/timestamp.proto";

message AEB {
  message TrajectoryPoint {
    Point3D position = 1;
    double utm_theta = 2;
    double utm_trailer_theta = 3;
  }
  message ControlOffset {
    double dx = 1;   //控制偏差-纵向
    double dy = 2;   //控制偏差-横向
    double dphi = 3; //控制偏差-航向
  }
  // AEB collision ogm point
  Point3D collision_point = 1;
  // AEB trajectory
  repeated TrajectoryPoint trajectories = 2;
  ControlOffset control_offset = 3;
}

message Camera {
  message image {
    string topic = 1;
    bytes url = 2;
  }
  repeated image images = 1;
}

message Lidar {
  google.protobuf.Timestamp timestamp = 1;
  repeated double msg = 2;
  string topic = 3;
  Transform tf = 4;
}

message FusionDetection {
  message OgmPoint {
    uint32 type = 1; // 点云类型 0: 未分类 1: unknown_small 2: unknown_big 3:
                     // 行人 4: 自行车 5: 小汽车 6: 卡车
    Point3D point = 2; // 点云数据
  }
  repeated OgmPoint ogm = 2; // 环境点云
}

/// 防坠海边界线
message RoadBoundary {
  message OgmPoint {
    uint32 type = 1;
    Point3D point = 2;
  }
  repeated OgmPoint ogm = 1;
}

message VisionLane {
  repeated Point3D points = 1;
  uint32 type = 2;
}

message Constrain {
  message SpeedConstrain {
    double speed = 1;
    double distance = 2;
  }
  repeated SpeedConstrain speed_constrains = 1;
}
