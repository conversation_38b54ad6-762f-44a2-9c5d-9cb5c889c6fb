syntax = "proto3";
package trunk.msgs;

// column-major representation of Eigen objects
// use only for fully dynamic matrices! Otherwise, use repeated double.
// You can also use SemiStaticMatrix below if you need a self-contained message,
// but don't use for message members.
// See eigen-proto.h
message MatrixXi {
  uint32 rows = 1;
  uint32 cols = 2;
  repeated int32 data = 3;
}

message MatrixXf {
  uint32 rows = 1;
  uint32 cols = 2;
  repeated float data = 3;
}

message MatrixXd {
  uint32 rows = 1;
  uint32 cols = 2;
  repeated double data = 3;
}

message SemiStaticMatrixi {
  repeated int32 data = 1;
}

message SemiStaticMatrixf {
  repeated float data = 1;
}

message SemiStaticMatrixd {
  repeated double data = 1;
}
