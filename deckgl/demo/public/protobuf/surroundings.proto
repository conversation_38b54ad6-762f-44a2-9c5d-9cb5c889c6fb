syntax = "proto3";
package trunk.msgs.v2;

import "geometry.proto";
import "google/protobuf/timestamp.proto";

message SurroundingVeh {
  int32 id = 1;                             // 车辆id
  double x = 2;                             // 车辆位置信息 utm_x
  double y = 3;                             // 车辆位置信息 utm_y
  double theta = 4;                         // 车辆位置信息 utm_theta
  double steer_angle = 5;                   // 车辆转向信息
  double trailer_angle = 6;
  double speed = 7;                         // 车辆速度(m/s)
  int32 decision_lon_state = 8;             // 纵向决策结果
  int32 decision_lat_state = 9;             // 横向决策结果
  int32 veh_status = 10;                    // 车辆状态，(行驶中、锁车、对位等)
  repeated Point4D path = 11;               // 规划轨迹
  int32 type = 12;                          // 车辆类型
  google.protobuf.Timestamp timestamp = 13; // 时间戳
}

message Surroundings{
  repeated SurroundingVeh surrounding_vehs = 1;
}