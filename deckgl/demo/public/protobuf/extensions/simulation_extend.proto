syntax = "proto3";

package trunk.msgs;

import "visualization.proto";
import "geometry.proto";


// 仿真后端服务器状态
message ServerState {
    string type = 1;
    string msg = 2;
    float ping_value = 3;
}


// entity 的属性信息
message EntityInfo {
    string id = 1;
    string type = 2;
    string name = 3;
    string model_name = 4;
}


// 卡车的包络信息
message TrunkBounding {
    double yaw = 1;  // 包络的 yaw
    double relevant_yaw = 2;  // 相对于前面车挂或者车头的 yaw
    Point3D pose = 3;  // 包络的前边界中心点
    repeated Point3D bounding = 4; //  包络边界点
}


// 前端实时显示的 metric 结果信息
message metric {
    repeated TrunkBounding metric_bounding = 1;
}


// EntityState 用于 Visualization 扩展车辆状态使用
message EntityState {
    float acceleration = 1;
    EntityInfo entity_info = 2;
    repeated TrunkBounding trunk_bounding = 3;
}


// 仿真扩展 Visualization
message Simulation {
    float stamp = 1;
    EntityState entity_state = 2;
}


// 仿真需要支持多 entity
message VisualizationList {
    repeated Visualization entities = 1;
}
