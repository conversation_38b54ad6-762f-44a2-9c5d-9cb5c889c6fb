syntax = "proto3";
package trunk.msgs;

import "google/protobuf/timestamp.proto";

message ARS430_InputHeader{
    uint32 ARS430_FUNC_UNAVAILABLE = 1;
    uint32 ARS430_FUNC_AVAILABLE = 2;

    uint32 ARS430_NO_DAMP = 3;
    uint32 ARS430_INCREASED_DAMP = 4;
    uint32 ARS430_FULL_DAMP = 5;

    uint64 Stamp = 6;
    uint32 ObjNum = 7;
    uint32 FuncStatus = 8;
    uint32 Blockage = 9;
    uint32 BusOff = 10;
}

message ARS430_InputData{
    uint32 ARS430_TYPE_POINT = 1;
    uint32 ARS430_TYPE_CAR = 2;
    uint32 ARS430_TYPE_TRUCK = 3;
    uint32 ARS430_TYPE_PEDESTRIAN = 4;
    uint32 ARS430_TYPE_MOTORCYCLE = 5;
    uint32 ARS430_TYPE_BICYCLE = 6;
    uint32 ARS430_TYPE_WIDE = 7;
    uint32 ARS430_TYPE_UNCLASSIFIED = 8;

    uint32 ARS430_MOVE_UNKNOWN = 9;
    uint32 ARS430_MOVE_MOVING = 10;
    uint32 ARS430_MOVE_STOPPED= 11;
    uint32 ARS430_MOVE_STATIONARY = 12;
    uint32 ARS430_MOVE_CROSSING = 13;
    uint32 ARS430_MOVE_ONCOMING = 14;
    uint32 ARS430_MOVE_ERR = 15;
    uint32 ARS430_MOVE_SNA = 16;

    uint32 ARS430_TUNNEL_NOTDETECTED = 17;
    uint32 ARS430_TUNNEL_DDETECTED = 18;
    uint32 ARS430_TUNNEL_ERR = 19;
    uint32 ARS430_TUNNEL_SNA = 20;

    uint32 ARS430_LOSS_NO_REASON = 21;
    uint32 ARS430_LOSS_TARGET_LOST = 22;
    uint32 ARS430_LOSS_OBJ_DIST_FAR = 23;
    uint32 ARS430_LOSS_OBJ_DIST_LEFT = 24;
    uint32 ARS430_LOSS_OBJ_DIST_RIGHT = 25;
    uint32 ARS430_LOSS_OBJ_LANE_LEFT = 26;
    uint32 ARS430_LOSS_OBJ_LANE_RIGHT = 27;
    uint32 ARS430_LOSS_OBJ_CRV_LEFT = 28;
    uint32 ARS430_LOSS_OBJ_CRV_RIGHT = 29;
    uint32 ARS430_LOSS_ERR = 30;
    uint32 ARS430_LOSS_SNA = 31;

    uint32 ARS430_MAINTAIN_DELETED = 32;
    uint32 ARS430_MAINTAIN_NEW = 33;
    uint32 ARS430_MAINTAIN_MEASURED = 34;
    uint32 ARS430_MAINTAIN_PREDICTED = 35;
    uint32 ARS430_MAINTAIN_INACTIVE = 36;
    uint32 ARS430_MAINTAIN_MAXTYPEDIFF = 37;
    uint32 ARS430_MAINTAIN_ERR = 38;
    uint32 ARS430_MAINTAIN_SNA = 39;

    uint32 ID = 40;
    uint32 Type__ = 41;
    uint32 MoveStatus = 42;
    uint32 TunnelStatus = 43;
    uint32 LossReason = 44;
    uint32 MaintainStatus = 45;
    uint32 DyStd = 46;
    uint32 DxStd = 47;
    uint32 VyStd = 48;
    uint32 VxStd = 49;
    uint32 LifeTime = 50;
    float RCS = 51;
    float MoveStatusConfi = 52;
    float ProbOfExist = 53;
    float ProbOfObs = 54;
    float Dy = 55;
    float Dx = 56;
    float Vx = 57;
    float Vy = 58;
    float Ax = 59;
    float Width = 60;
    float ElevationObjConfi = 61;
}

message ARS430_Input{
    google.protobuf.Timestamp timestamp = 1;
    ARS430_InputHeader Header = 2;
    repeated ARS430_InputData Data = 3;
}
