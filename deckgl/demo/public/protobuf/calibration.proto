syntax = "proto3";
package trunk.msgs.v2;

import "geometry.proto";


message CalibrationRequest {
  uint32 mode = 1;                          // 0: 开始 1: 微调 2: 保存
  repeated Transform tf = 2;                // tf
}

message CalibrationResponse {
  enum Code {
    UNKNOWN = 0;
    SUCCESS = 1000;                        // 成功
    FAILED  = 2001;                        // 失败
    TIMEOUT = 2002;                        // 超时
    PARAM   = 2003;                        // 参数非法
    INVALID = 2004;                        // mode异常
    RUNNING = 2005;                        // 上一次标定未结束
  }
  Code code = 1;                           // 状态码
  string msg = 2;                          // 消息描述
  string path = 3;                         // urdf文件路径
}

message CalibrationPointCloud {
  message PointCloud {
    Header header = 1;
    Transform tf = 2;                       // tf
    repeated double data = 3;               // 点云数据, 每隔三个一组
  }
  repeated PointCloud pcl = 1;              // 点云
}

message CalibrationParamInfo {
  repeated string lidar_config = 1;         // 雷达配置文件列表
  repeated string bag_path = 2;             // bag包文件列表
  repeated string urdf_path = 3;            // urdf文件列表
}

message CalibrationSetParam {
  enum TfFrom {
    TOPIC = 0;     // topic
    BAG   = 1;     // bag包
    URDF  = 2;     // urdf文件
  }
  bool is_online = 1;                       // 在线标定 true: 在线 false: 离线
  string lidar_config = 2;                  // 雷达配置文件
  string bag_path = 3;                      // bag包路径
  string urdf_path = 4;                     // urdf文件路径
  TfFrom tf_from = 5;                       // tf来源
}

