syntax = "proto3";
package trunk.msgs;

import "geometry.proto";

message Reply {
  int32 status = 1; //0: normal, 1: error
}

// ros msg ADSCmdDomain
message TroubleRemove {
  Header header = 1;
  int32 load_start_enable = 2;  // 按照装箱作业任务发送称重开始标识
  // (cdc会根据此信号的上升沿来重新进行称重，若无上升沿则会保留上一次称重结果，车辆正常作业时该指令应当在装卸箱作业过程中发送0，作业完成后发送1)
  uint32 load = 3;              // 参考重量
  uint32 tos_reset_cmd = 4;      // 故障复位
  uint32 tos_estop = 5;         // 紧急停车
  uint32 bypass_enable = 6;	    // 故障忽略使能
  uint32 bypass_code = 7;	      // 故障忽略码
  uint32 task_type = 8;         // 0: 装箱 1:卸箱
}

message SetIntensityThreshold {
  float instensity_threshold = 1;
}

// ros msg ADSCmdSwitchAeb
message SwitchAeb {
  Header header = 1;
  int32 id = 2;
  bool turn_on_aeb = 3;
}

service ADSCmdService {
  rpc trouble_remove(TroubleRemove) returns (Reply) {}
  rpc reset_location(PoseWithCovarianceStamped) returns (Reply) {}
  rpc set_intensity_threshold(SetIntensityThreshold) returns (Reply) {}
  // tc
  rpc switch_aeb(SwitchAeb) returns (Reply) {}
}