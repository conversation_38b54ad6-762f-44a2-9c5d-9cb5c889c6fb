syntax = "proto3";
package trunk.msgs;

import "google/protobuf/timestamp.proto";
import "CustomPoint.proto";

// Livox costom pointcloud msg format.

message CustomMsg {
    google.protobuf.Timestamp timestamp = 1;
    uint64 timebase = 2;                   // The time of first point
    uint32 point_num = 3;                  // Total number of pointclouds
    uint32  lidar_id = 4;                  // Lidar device id number
    repeated uint32 rsvd = 5;              // Reserved use
    repeated CustomPoint points = 6;       // Pointcloud data
}
