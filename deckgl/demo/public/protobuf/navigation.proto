syntax = "proto3";
package trunk.msgs;

import "geometry.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

message Gnss {
  // Position
  double latitude = 1;
  double longitude = 2;
  double altitude = 3;
  // Velocity
  double north_velocity = 4;
  double east_velocity = 5;
  double up_velocity = 6;
  // Dual Antenna
  double heading = 7;
  // Utm
  double utm_east = 8;
  double utm_north = 9;
  // Std
  float latitude_std = 10;
  float longitude_std = 11;
  float altitude_std = 12;
  float heading_std = 13;
  // Gps Status
  int32 position_status = 14;
  int32 orientation_status = 15;
  int32 status = 16;
  int32 satellite_num = 17;
  float hdop = 18;
  // UTC Time 
  uint32 year = 19;
  uint32 month = 20;
  uint32 day = 21;
  uint32 hour = 22;
  uint32 minute = 23;
  float second = 24;
}

message Imu {
  // Gyro
  Vector3 angular_velocity = 1;
  // Accelerometer
  Vector3 linear_acceleration = 2;
  // Orientation
  Quaternion orientation = 3;
} 

message Ins {
  // Position
  double latitude = 1;
  double longitude = 2;
  double altitude = 3;
  // Velocity
  double north_velocity = 4;
  double east_velocity = 5;
  double up_velocity = 6;
  // Attitude
  double roll = 7;
  double pitch = 8;
  double yaw = 9;
  // Position Std
  float latitude_std = 10;
  float longitude_std = 11;
  float altitude_std = 12;
  // Velocity Std
  float north_velocity_std = 13;
  float east_velocity_std = 14;
  float up_velocity_std = 15;
  // Attitude Std
  float roll_std = 16;
  float pitch_std = 17;
  float yaw_std = 18;
  // Utm
  double utm_east = 19;
  double utm_north = 20;
  // Init Status
  uint32 init_position = 21;
  uint32 init_velocity = 22;
  uint32 init_pitchroll = 23;
  uint32 init_yaw = 24;
  // Device
  double temperature = 25;
  int32 wheel_speed_status = 26;
}

message Navigation {
  google.protobuf.Timestamp timestamp = 1;
  Imu imu = 2;
  Gnss gnss = 3;
  Ins ins = 4;
}

service NavigationService {
  rpc getImu(google.protobuf.Empty) returns (Imu) {}
  rpc getGnss(google.protobuf.Empty) returns (Gnss) {}
  rpc getIns(google.protobuf.Empty) returns (Ins) {}
  rpc getNavigation(google.protobuf.Empty) returns (Navigation) {}
}
