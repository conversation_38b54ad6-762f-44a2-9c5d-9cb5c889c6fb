syntax = "proto3";
package trunk.proto.data;

import "trunk/proto/common/geometry.proto";
import "trunk/proto/common/planning.proto";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

/*****************************************************
 *              通用自动驾驶数据接口                 *
 *****************************************************/

message Vehicle {
  message Model {
    string type = 1;
    double length = 2;
    double base2front = 3;
    double base2tail = 4;
    double width = 5;
    double wheelbase = 6;
    double wheeltrack = 7;
    double trailer_length = 8;
    double trailer_base2front = 9;
    double trailer_base2tail = 10;
    double trailer_width = 11;
    double trailer_wheelbase = 12;
  }
  common.Pose position = 1;
  common.Twist velocity = 2;
  double yaw = 3;
  uint32 auto_driver_status = 4;  // 自动驾驶状态 0: 自动模式 1: 人工模式 2:
                                  // 遥控模式 3: 远控模式 4: 维保模式
  float vehicle_speed = 5;  // 速度 m/s
  float brake = 6;     // 制动踏板开度 0.0 ~ 1.0 备注:卡车制动踏板
  float throttle = 7;  // 油门踏板开度 0.0 ~ 1.0 备注:卡车油门踏板
  float long_accel = 8;            // 纵向加速度 m/s^2
  uint32 gear = 9;                 // 档位 0:P 1:R 2:N 3:D
  uint32 dump_energy_status = 10;  // 电量 0 ~ 100 %
  uint32 charge_status =
      11;  // 当前充电状态     0: 无连接 1: 连接未充电 2:
           // 交流充电中 3: 直流充电中 4: 充电完成 5: 充电故障
  uint32 vehicle_start_flag =
      12;  // 整车高压状态反馈 0: 下高压状态 1: 上高压状态
  uint32 travelable_mileage = 13;  // 可行驶里程 km
  uint32 total_time = 14;          // 总运行时间 h
  float total_kilometer = 15;      // 总运行里程 km
  uint32 light_status =
      16;  // 灯光反馈     bit 1: 左转向灯 2: 右转向灯 3: 双闪 4: 刹车灯 5:
           // 前行车灯 6: 后行车灯 7: 近光灯
           //                  8: 远光灯 9: 前雾灯 10: 后雾灯 11: 挂车雾灯 12:
           //                  示廓灯 13: 前照灯 14: 后照灯 15: 危险报警顶灯
  uint32 emergency_stop_status =
      17;  // 紧急刹车状态 bit 0: 无急停 1: estop 2:
           // 急停按钮 3: 维保开关 4: 驻车 5: 远控急停
  uint32 position_status = 18;  // 位置模式到位标志 0: 无效 1: 到位 2: 未到位
  uint32 vehicle_error_status =
      19;  // 线控底盘系统故障等级 0: 无故障 1: 警告故障 2: 严重故障
  repeated uint32 vehicle_fault_code = 20;  // 厂商故障码
  repeated uint32 cdc_fault_code = 21;  // cdc对车辆和自动驾驶系统诊断出的故障
  repeated sint32 motor_temperature = 22;          // 电机温度 ℃
  repeated sint32 motor_control_temperature = 23;  // 电机控制器温度 ℃
  repeated sint32 cell_temperature = 24;           // 电池温度 ℃
  repeated uint32 wheel_odometer = 25;             // 轮速计脉冲数
  repeated float wheel_speed =
      26;  // 轮速 m/s  z字形:
           // 依次为#1轴左、#1轴右、#2轴左、#2轴右、#3轴左、#4轴左、#4轴右
  repeated float wheel_angle = 27;    // 轮角 ° 同轮速对应
  repeated float tire_pressure = 28;  // 胎压 bar 同轮速对应
  int32 UTMZone = 29;                 // utm带号
  float trailer_angle = 30;           // 挂车角度 °
  float steering_angle = 31;  // 方向盘角度°，平板车默认填充0
  uint32 long_status = 32;  // 纵向模式 0: 速度模式 1: 扭矩模式 2: 位置模式
  uint32 bcm_appendix =
      33;  // 车身附件状态反馈 bit 1: 电喇叭 2: 气喇叭 3: 低速雨刷 4: 高速雨刷
           // 5: 玻璃水 6: 倒车提示音 7: 车门1 8: 车门2
  repeated float brake_pressure =
      34;  // 制动压力 kpa （卡车: 压力1、压力2；平板车:
           // 主压力、前行车制动压力、后行车制动压力、驻车制动压力）
  int32 localization_status = 35;  // 定位跟踪状态, 详情见VehicleState.msg
  string plate_number = 36;        // 车牌号
  Model model = 37;                // 车辆的类型和尺寸
}

message Map {
  message Destination {
    string name = 1;  // 目的地名称
    uint32 type = 2;  // 目的地类型 0: 未指定 1: 岸桥 2: 堆场
    common.Point3D point = 3;  // 目的地坐标点
  }

  message Lane {
    int32 id = 1;            // lane id
    double speed_limit = 2;  // 当前限速
    uint32 lane_type = 3;    // 车道类型
    uint32 turn_type =
        4;  // 车道转弯类型 0: 直行车道 1: 左转车道 2: 右转车道 3: 掉头车道
    repeated uint32 boundary_type =
        5;  // 边界线类型[左边界, 右边界]   0: 未知 1: 实线 2: 虚线 3: 路边 4:
            // 波茨点 5: 虚拟线
    repeated uint32 boundary_color =
        6;  // 边界线颜色[左边界, 右边界]   0: 未知 1: 白色 2: 黄色
    repeated common.PointXD center_line = 7;  // 中心线
    repeated common.Point3D left_line = 8;    // 左边界线
    repeated common.Point3D right_line = 9;   // 右边界线
    int32 goal_index = 10;                    // 目标车道索引
    int32 length = 11;                        // 车道长度
  }
  string navi_id = 1;         // tpa 下发终点指令id
  double stop_distances = 2;  // 距终点距离
  repeated Lane lanes = 3;
  uint32 hdmap_status = 4;  // 规划状态
  uint32 lane_type = 5;     // 当前车道类型
  uint32 turn_type = 6;  // 前方N米的车道转弯类型 0: 直行车道 1: 左转车道 2:
                         // 右转车道 3: 掉头车道
  string lane_name = 7;                      // 当前车道名
  double current_speed_limit = 8;            // 当前路段限速
  double next_speed_limit = 9;               // 下一路段限速
  double distance_to_next_speed_limit = 10;  // 距离下一限速路段距离
  repeated Destination destinations = 11;    // 目的地
  uint32 lane_virtual = 12;                  // 虚拟车道 0: 没有虚拟边线, 1: 仅左边线为虚拟边线 2: 仅右边线为虚拟边线 3: 左右均为虚拟边线
}

message Environment {
  message Object {
    repeated common.Point3D vertex = 1;  // 障碍物轮廓点
    common.Point3D center = 2;           // 障碍物中心点
    common.Point3D size = 3;             // 障碍物尺寸
    common.Vector3 speed = 4;            // 障碍物速度
    int32 id = 5;                        // 障碍物id
    uint32 classification = 6;           // 障碍物分类
    float heading = 7;                   // 障碍物yaw角
  }

  message OgmPoint {
    uint32 type = 1;  // 点云类型 0: 未分类 1: unknown_small 2: unknown_big 3:
                      // 行人 4: 自行车 5: 小汽车 6: 卡车
    common.Point3D point = 2;  // 点云数据
  }

  message TrafficLight {
    uint32 color = 1;  // 信号灯颜色 0: unknown 1: 红 2: 黄 3: 绿 4: 黑(未亮起)
    uint32 shape_type = 2;  // 信号灯形状 0: unknown 1: 水平 2: 垂直 3: 其它
    uint32 blink = 3;  // 闪烁状态 0: 闪烁中 1: 未闪烁 3: unknown
    uint32 turn_type =
        4;  // 转向类型 0: unknown 1: 左转 2: 右转 3: 直行 4: 掉头
            // 5: 左/直行 6: 右/直行 7: 左/掉头 8: 左/右/直行
  }

  repeated Object objects = 1;               // 障碍物包络
  repeated OgmPoint ogm = 2;                 // 环境点云
  repeated Object hanging_objects = 3;       // 悬挂物
  repeated TrafficLight traffic_lights = 4;  // 交通信号灯
  repeated int32 weather = 5;                // 天气
}

message ADS {
  message PredictionObject {
    message PredictionTrajectory {
      repeated common.Point3D points = 1;
    }
    repeated PredictionTrajectory trajectories = 1;
  }

  bool aeb_collision = 1;  // aeb触发状态 true: 触发 false: 未触发
  bool aeb_is_on = 2;  // aeb开关状态 true: 使用aeb false: 不适用aeb
  uint32 aeb_trigger_type = 3;  // aeb触发类型 0: 未触发 1: 即将碰撞 2: 输入异常
  string decision_scenario_name = 4;  // 决策-场景
  string decision_stage_name = 5;     // 决策-阶段
  uint32 decision_lat_state =
      6;  // 决策-横向 0: 跟随 1: 左侧变道 2: 右侧变道 3: 取消变道 4: left nudge
          // 5: right nudge 6: 未知
  uint32 decision_lon_state =
      7;  // 决策-纵向 0: 保持 1: 超车 2: yield 3: stop 4: e-stop
  double rt_speed = 8;                             // 期望车速 m/s
  double rt_brake = 9;                             // 期望制动踏板开度
  double rt_throttle = 10;                         // 期望油门开度
  uint32 rt_gear = 11;                             // 期望档位
  double final_distance = 12;                      // 距终点距离
  repeated PredictionObject prediction_objs = 13;  // 障碍物预测轨迹
  repeated common.Point4D planning_traj = 14;      // 规划轨迹
  repeated common.Point2D decision_left_boundary = 15;   // 决策左边界
  repeated common.Point2D decision_right_boundary = 16;  // 决策右边界
  repeated uint32 decision_err_codes = 17;  // 决策错误码 [已弃用]
  uint32 stop_reason =
      18;  // 停车原因 0: 行驶中 1: 缓停点停车 2: 急停点停车 3: 决策障碍物停车
           //          4: 非自动驾驶 5: 手拨ESTOP 6: 地图下发路径偏离过大 7:
           //          空闲 10: 决策限速停车 11: 规划障碍物停车 12: 终点停车 13:
           //          轨迹长度不足导致停车 14: 曲率过大停车 15:
           //          控制偏离路径过远停车 16: 地图限速停车 20:
           //          油门不足导致停车 21: 对位完成 30: AEB导致停车 31:
           //          定位跳变导致停车 32: 防坠海触发 33:画龙 34:
           //          监测到底盘延迟 35: 监测到急打转向 100:
           //          故障停车，需查看error_code
  double obstacle_distance = 19;  // 最近障碍物距离
  repeated common.Point3D decision_reference_line = 20;  // 决策参考线
  double rt_steering_angle_front = 21;  // 前轮转角控制量 -1.0 ~ 1.0
  double rt_steering_angle_rear = 22;   // 后轮转角控制量 -1.0 ~ 1.0
  double preset_cruise_speed =
      23;  // 司机通过方向盘按钮设定的自动驾驶巡航车速km/h
  uint32 lane_change_status =
      24;  // pnc输出的的换道状态, 0: 直行 1: 左转 2: 右转
  uint32 severity_level = 25;  // 车辆故障诊断等级，NO_PAIN = 0, MILD = 1,
                               // MODERATE = 2, SEVERE = 3, WORST = 4
  uint32 fcw_level = 26;  // FCW预警等级, 0: 不做处理 1: 一级报警(ttc<4.5s) 2:
                          // 二级报警(ttc<3.0s) 3: 三级报警(ttc<1.5s)
  uint32 planning_nudge = 27;  // planning的nudge状态, 0: 无 1: left 2: right
  repeated common.TrajectoryPoint planning_traj_pts = 28; // 规划轨迹2
}

message Sensor {
  message LidarTimestamp {
    string lidar_name = 1;
    string install_position = 2;
    google.protobuf.Timestamp timestamp = 3;
  }

  message LidarDiagnose {
    repeated string lidar_name = 1;
    repeated bool need_calibrate_flag = 2;
  }

  repeated LidarTimestamp lidar_timestamps = 1;  // 激光雷达时间戳
  google.protobuf.Timestamp gnss_timestamp = 2;  // gnss时间戳
  uint32 gnss_status = 3;                        // gnss状态
  LidarDiagnose lidar_diagnose = 4;              // 激光雷达诊断信息
}

message Frame {
  Vehicle vehicle = 1;                      // 车辆状态
  Map map = 2;                              // 道路情况
  Environment environment = 3;              // 交通环境
  ADS ads = 4;                              // 自动驾驶系统
  Sensor sensor = 5;                        // 传感器相关
  repeated int32 error_codes = 6;           // 故障码列表
  google.protobuf.Timestamp timestamp = 7;  // 时间戳
}

service FrameService {
  rpc GetFrame(google.protobuf.Empty) returns (Frame);
}
