syntax = "proto3";
package trunk.proto.fit;

import "trunk/proto/common/geometry.proto";
import "trunk/proto/fit/enums.proto";

// 机械技术状态
enum TechnicalStatus {
  TECH_STATUS_DEFAULT = 0;
  GREEN = 1;   // 正常
  YELLOW = 2;  // 可以执行任何任务，但有轻微故障
  ORANGE = 3;  // 可以完成当前任务
               // 但之后不可以按受与集装箱相关的任务。接受停车或充电任务
  RED = 4;     // 不能正常工作，需要维修
}

// 运动状态,当前用作岸桥是静止还是运动的
enum MotionStatus {
  MOTION_STATUS_DEFAULT = 0;
  MOTION = 1;  // 运动
  STATIC = 2;  // 静止
}

// 船舶靠泊方向
enum VesselDirection {
  VESSEL_DIRECTION_DEFAULT = 0;
  RIGHT = 1;  // 正，右弦靠泊
  LEFT = 2;   // 反，左弦靠泊
}

enum CraneType {
  QC = 0;
  YC = 1;
  DBYC = 2;  // double cantilever yard crane
}

message Crane {
  string crane_id = 1;                   // QC ID
  TechnicalStatus technical_status = 2;  // 技术状态
  string technical_details = 3;    // 技术状态描述，GREEN时不描述
  int32 gantry_offset = 4;         // 大车相对位置偏移量，单位mm
  int32 trolley_offset = 5;        // 小车相对位置偏移量，单位mm
  int32 spreader_height = 6;       // 吊具高度,mm
  common.Point4D gantry_pos = 7;   // 大车坐标，utm
  common.Point4D trolley_pos = 8;  // 小车坐标，utm
  common.Point4D land_left_leg_pos =
      9;  // 面向海侧站，岸桥陆侧左桥腿的坐标，utm
  common.Point4D land_right_leg_pos =
      10;  // 面向海侧站，岸桥陆侧右桥腿的坐标，utm
  string lane_id = 11;        // 当前作业车道号
  MotionStatus move = 12;  // 运动状态  大车运动状态：0未知
                              // 在有些异常情况下发送 1是运动 2是静止
  bool bay_arrived = 13;                 // 是否落贝
  string up_vpb = 14;                       // 上桥位
  string down_vpb = 15;                     // 下桥位
  string berth_name = 16;                   // 泊位号
  WorkCycleDirection route_direction = 17;  //  车辆进入方向
  VesselDirection vessel_direction = 18;    // 船舶靠泊方向
  int32 lock_status = 19;  // 开闭锁状态（0是未知1是闭锁2是开锁）
  int32 trolley_model = 20;  // 小车运动模式：0未知 在有些异常情况下发送
  // 1是正常模式状态（集装箱吊装模式）
  // 2是慢速模式状态（舱盖板吊装模式）
  CraneType crane_type = 21;  // 桥类型，用于前端区分模型
  double crane_yaw = 22;      // 航向角, 用于前端模型角度调整
  string is_top = 23;         // 是否偏上, 用于前端位置调整
  int32 bay = 24;   // 场桥时: 场贝  岸桥时: 桥吊对应船的贝
  repeated string che_ids = 25;   // 分配给岸桥的工作车辆
  BusinessType business_type = 26;  // 业务场景
  repeated string occupancy_che_ids = 27;  // 岸桥当前占用车辆列表
  int32 crane_capacity = 28; // 岸桥容量
  int32 spreader_size = 29; // 岸桥吊具尺寸
  string truck_id = 30; // 岸桥当前正在交互的车辆
  bool is_online = 31; // 岸桥是否在线
}
