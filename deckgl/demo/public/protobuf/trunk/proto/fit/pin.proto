syntax = "proto3";
package trunk.proto.fit;

import "google/protobuf/timestamp.proto";
import "trunk/proto/fit/fault_info.proto";
import "trunk/proto/fit/enums.proto";

enum PinsStatus {
  PINS_STATUS_DEFAULT = 0;
  PINS_STATUS_READY = 1;    // 准备
  PINS_STATUS_AT_TZ = 2;    // 即将驶入
  PINS_STATUS_AT_TP = 3;    // 到达
  PINS_STATUS_LOCK = 4;     // 锁车
  PINS_STATUS_UNLOCK = 5;   // 已解锁
  PINS_STATUS_LEAVING = 6;  // 驶离中
  PINS_STATUS_IDLE = 7;     // 驶离（无车辆状态）
}

message Pin {
  google.protobuf.Timestamp time = 1;  // 更新日期
  string pstp_id = 2;                  // 锁站id
  string che_id = 3;                   // 车辆id
  PinsStatus status = 4;               // 锁站状态
  repeated string task_che_ids = 5;    // 要作业的车辆
  repeated SwitchInfo switch_info = 6; // 锁站按钮状态
  string bridge_no = 7;  // 引桥编号
  string enable = 8;  // Y/N Y-有捆扎工,锁站启用 N-无捆扎工,锁站未启用
  string work_type = 9;  // L/U L-装锁,驶向岸桥 U-卸锁,驶向堆场 作用指向行驶方向
  repeated string vessel_bind = 10;  // 可绑定1个或多个航次
  google.protobuf.Timestamp config_updated = 11;  // 表示配置更新的时间
}

message SwitchInfo{
  enum SwitchColor{
    DEFAULT_SWITCH_COLOR = 0;
    GREEN = 1;
    RED = 2;
  }

  string switch_id            = 1;  // 按钮id
  PinsSwitchType switch_type  = 2;  // 开关类型
  SwitchColor switch_color    = 3;  // 开关颜色 r:红色，g:绿色
  ButtonStatus button_status  = 4;  // 按钮开关
  FaultInfo fault_info = 5;  // 故障信息
}
