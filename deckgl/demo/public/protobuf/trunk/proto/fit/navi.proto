syntax = "proto3";
package trunk.proto.fit;

import "trunk/proto/common/geometry.proto";
import "trunk/proto/common/header.proto";
import "trunk/proto/fit/enums.proto";

import "google/protobuf/timestamp.proto";

message AdvisorySpeeds {
  float vmax = 1;      // 建议车速
  float vmax_dev = 2;  // 建议车速可正负偏离范围
}

message TimeWindow {
  google.protobuf.Timestamp time = 1;  // 关键点要求到达时间
  int32 time_dev = 2;  // 关键点可正负偏离范围,单位秒
}

message WayPoint {
  common.Point4D pos = 1;
  double heading = 2;
  AdvisorySpeeds speeds = 3;
  TimeWindow time_window = 4;
  PointType type = 5;
  DriveDirection driving_direction = 6;
  string id = 7;
  int32 lane_type = 8;
}

message Path {
  message Lane {
    string id = 1;
    int32 path_index = 2;
    int32 start_index = 3;
    int32 end_index = 4;
  }

  message Section {
    string id = 1;
    double distance_to_end = 2;
    repeated Lane lanes = 3;
  }

  message Header {
    uint32 seq = 1;
    google.protobuf.Timestamp timestamp = 2;
    string frame_id = 3;
  }

  message Point2D {
    double x = 1;
    double y = 2;
  }

  Header header = 1;
  string navi_id = 2;
  repeated string version = 3;               // 地图文件版本号
  repeated Section sections = 4;
  double remaining_distance = 5;
  repeated Point2D way_points = 6;
}

message Navi {
  string che_id = 1;  // 车辆id
  string task_id = 2;  // 任务id
  string navi_id = 3;
  uint32 seq_num = 4;  // 顺序号，从1开始，当前task路径每更新一次加1
  WorkCycleDirection route_direction = 5;  //车辆进入方向
  DestType dest_type = 6;                  // 本次navi终点类型
  WayPointsMode mode = 7;                  // 规划模式
  string up_vpb = 8;                       // 上岸桥经过的引桥号
  string down_vpb = 9;                     // 下岸桥经过的引桥号
  bool route_update = 10;            // True：更新路径，False：新任务
  repeated WayPoint waypoints = 11;  // 途径点或轨迹
  SafeDistanceLevel safe_distance_level = 12;  // 途径点或轨迹
  bool is_final_navi = 13;                       // 是否是最后路径
  Path path = 14;                              // 路径
  int32 navi_status = 15;      // navi接收状态
}
