{"version": 3, "sources": ["../../worker-utils/src/lib/env-utils/version.ts", "../../worker-utils/src/lib/env-utils/assert.ts", "../../worker-utils/src/lib/env-utils/globals.ts", "../../worker-utils/src/lib/worker-utils/get-transfer-list.ts", "../../worker-utils/src/lib/worker-farm/worker-body.ts", "../../worker-utils/src/lib/node/require-utils.browser.ts", "../../worker-utils/src/lib/library-utils/library-utils.ts", "../../loader-utils/src/lib/worker-loader-utils/create-loader-worker.ts", "../src/lib/utils/version.ts", "../src/draco-loader.ts", "../../schema/src/category/mesh/mesh-utils.ts", "../../schema/src/lib/utils/assert.ts", "../../schema/src/lib/schema/impl/schema.ts", "../../schema/src/lib/schema/impl/field.ts", "../../schema/src/lib/schema/impl/enum.ts", "../../schema/src/lib/schema/impl/type.ts", "../../schema/src/lib/arrow/arrow-like-type-utils.ts", "../../schema/src/category/mesh/deduce-mesh-schema.ts", "../src/lib/utils/get-draco-schema.ts", "../src/lib/draco-parser.ts", "../src/lib/draco-module-loader.ts", "../src/index.ts", "../src/workers/draco-worker.ts"], "sourcesContent": ["// Version constant cannot be imported, it needs to correspond to the build version of **this** module.\n// __VERSION__ is injected by babel-plugin-version-inline\n\n// Change to `latest` on production branches\nconst DEFAULT_VERSION = 'latest';\ndeclare let __VERSION__: string;\nexport const VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : DEFAULT_VERSION;\nif (typeof __VERSION__ === 'undefined') {\n  // eslint-disable-next-line\n  console.error(\n    'loaders.gl: The __VERSION__ variable is not injected using babel plugin. Latest unstable workers would be fetched from the CDN.'\n  );\n}\n", "// Replacement for the external assert method to reduce bundle size\n// Note: We don't use the second \"message\" argument in calling code,\n// so no need to support it here\n\n/** Throws an `Error` with the optional `message` if `condition` is falsy */\nexport function assert(condition: any, message?: string): void {\n  if (!condition) {\n    throw new Error(message || 'loaders.gl assertion failed.');\n  }\n}\n", "// Purpose: include this in your module to avoids adding dependencies on\n// micro modules like 'global' and 'is-browser';\n\n/* eslint-disable no-restricted-globals */\nconst globals = {\n  self: typeof self !== 'undefined' && self,\n  window: typeof window !== 'undefined' && window,\n  global: typeof global !== 'undefined' && global,\n  document: typeof document !== 'undefined' && document\n};\n\nconst self_: {[key: string]: any} = globals.self || globals.window || globals.global || {};\nconst window_: {[key: string]: any} = globals.window || globals.self || globals.global || {};\nconst global_: {[key: string]: any} = globals.global || globals.self || globals.window || {};\nconst document_: {[key: string]: any} = globals.document || {};\n\nexport {self_ as self, window_ as window, global_ as global, document_ as document};\n\n/** true if running in the browser, false if running in Node.js */\nexport const isBrowser: boolean =\n  // @ts-ignore process.browser\n  typeof process !== 'object' || String(process) !== '[object process]' || process.browser;\n\n/** true if running on a worker thread */\nexport const isWorker: boolean = typeof importScripts === 'function';\n\n/** true if running on a mobile device */\nexport const isMobile: boolean =\n  typeof window !== 'undefined' && typeof window.orientation !== 'undefined';\n\n// Extract node major version\nconst matches =\n  typeof process !== 'undefined' && process.version && /v([0-9]*)/.exec(process.version);\n\n/** Version of Node.js if running under Node, otherwise 0 */\nexport const nodeVersion: number = (matches && parseFloat(matches[1])) || 0;\n", "// NOTE - there is a copy of this function is both in core and loader-utils\n// core does not need all the utils in loader-utils, just this one.\n\n/**\n * Returns an array of Transferrable objects that can be used with postMessage\n * https://developer.mozilla.org/en-US/docs/Web/API/Worker/postMessage\n * @param object data to be sent via postMessage\n * @param recursive - not for application use\n * @param transfers - not for application use\n * @returns a transfer list that can be passed to postMessage\n */\nexport function getTransferList(\n  object: any,\n  recursive: boolean = true,\n  transfers?: Set<any>\n): Transferable[] {\n  // Make sure that items in the transfer list is unique\n  const transfersSet = transfers || new Set();\n\n  if (!object) {\n    // ignore\n  } else if (isTransferable(object)) {\n    transfersSet.add(object);\n  } else if (isTransferable(object.buffer)) {\n    // Typed array\n    transfersSet.add(object.buffer);\n  } else if (ArrayBuffer.isView(object)) {\n    // object is a TypeArray viewing into a SharedArrayBuffer (not transferable)\n    // Do not iterate through the content in this case\n  } else if (recursive && typeof object === 'object') {\n    for (const key in object) {\n      // Avoid perf hit - only go one level deep\n      getTransferList(object[key], recursive, transfersSet);\n    }\n  }\n\n  // If transfers is defined, is internal recursive call\n  // Otherwise it's called by the user\n  return transfers === undefined ? Array.from(transfersSet) : [];\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Transferable\nfunction isTransferable(object: unknown) {\n  if (!object) {\n    return false;\n  }\n  if (object instanceof ArrayBuffer) {\n    return true;\n  }\n  if (typeof MessagePort !== 'undefined' && object instanceof MessagePort) {\n    return true;\n  }\n  if (typeof ImageBitmap !== 'undefined' && object instanceof ImageBitmap) {\n    return true;\n  }\n  // @ts-ignore\n  if (typeof OffscreenCanvas !== 'undefined' && object instanceof OffscreenCanvas) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Recursively drop non serializable values like functions and regexps.\n * @param object\n */\nexport function getTransferListForWriter(object: object | null): object {\n  if (object === null) {\n    return {};\n  }\n  const clone = Object.assign({}, object);\n\n  Object.keys(clone).forEach((key) => {\n    // Typed Arrays and Arrays are passed with no change\n    if (\n      typeof object[key] === 'object' &&\n      !ArrayBuffer.isView(object[key]) &&\n      !(object[key] instanceof Array)\n    ) {\n      clone[key] = getTransferListForWriter(object[key]);\n    } else if (typeof clone[key] === 'function' || clone[key] instanceof RegExp) {\n      clone[key] = {};\n    } else {\n      clone[key] = object[key];\n    }\n  });\n\n  return clone;\n}\n", "import type {WorkerMessageData, WorkerMessageType, WorkerMessagePayload} from '../../types';\nimport {getTransferList} from '../worker-utils/get-transfer-list';\n\n/** Vile hack to defeat over-zealous bundlers from stripping out the require */\nfunction getParentPort() {\n  // const isNode = globalThis.process;\n  let parentPort;\n  try {\n    // prettier-ignore\n    eval('globalThis.parentPort = require(\\'worker_threads\\').parentPort'); // eslint-disable-line no-eval\n    parentPort = globalThis.parentPort;\n    // eslint-disable-next-line no-empty\n  } catch {}\n  return parentPort;\n}\n\nconst onMessageWrapperMap = new Map();\n\n/**\n * Type safe wrapper for worker code\n */\nexport default class WorkerBody {\n  /** Check that we are actually in a worker thread */\n  static inWorkerThread(): boolean {\n    return typeof self !== 'undefined' || Boolean(getParentPort());\n  }\n\n  /*\n   * (type: WorkerMessageType, payload: WorkerMessagePayload) => any\n   */\n  static set onmessage(onMessage: (type: WorkerMessageType, payload: WorkerMessagePayload) => any) {\n    function handleMessage(message) {\n      // Confusingly the message itself also has a 'type' field which is always set to 'message'\n      const parentPort = getParentPort();\n      const {type, payload} = parentPort ? message : message.data;\n      // if (!isKnownMessage(message)) {\n      //   return;\n      // }\n      onMessage(type, payload);\n    }\n\n    const parentPort = getParentPort();\n    if (parentPort) {\n      parentPort.on('message', handleMessage);\n      // if (message == 'exit') { parentPort.unref(); }\n      // eslint-disable-next-line\n      parentPort.on('exit', () => console.debug('Node worker closing'));\n    } else {\n      // eslint-disable-next-line no-restricted-globals\n      globalThis.onmessage = handleMessage;\n    }\n  }\n\n  static addEventListener(\n    onMessage: (type: WorkerMessageType, payload: WorkerMessagePayload) => any\n  ) {\n    let onMessageWrapper = onMessageWrapperMap.get(onMessage);\n\n    if (!onMessageWrapper) {\n      onMessageWrapper = (message: MessageEvent<any>) => {\n        if (!isKnownMessage(message)) {\n          return;\n        }\n\n        // Confusingly in the browser, the message itself also has a 'type' field which is always set to 'message'\n        const parentPort = getParentPort();\n        const {type, payload} = parentPort ? message : message.data;\n        onMessage(type, payload);\n      };\n    }\n\n    const parentPort = getParentPort();\n    if (parentPort) {\n      console.error('not implemented'); // eslint-disable-line\n    } else {\n      globalThis.addEventListener('message', onMessageWrapper);\n    }\n  }\n\n  static removeEventListener(\n    onMessage: (type: WorkerMessageType, payload: WorkerMessagePayload) => any\n  ) {\n    const onMessageWrapper = onMessageWrapperMap.get(onMessage);\n    onMessageWrapperMap.delete(onMessage);\n    const parentPort = getParentPort();\n    if (parentPort) {\n      console.error('not implemented'); // eslint-disable-line\n    } else {\n      globalThis.removeEventListener('message', onMessageWrapper);\n    }\n  }\n\n  /**\n   * Send a message from a worker to creating thread (main thread)\n   * @param type\n   * @param payload\n   */\n  static postMessage(type: WorkerMessageType, payload: WorkerMessagePayload): void {\n    const data: WorkerMessageData = {source: 'loaders.gl', type, payload};\n    // console.log('posting message', data);\n    const transferList = getTransferList(payload);\n\n    const parentPort = getParentPort();\n    if (parentPort) {\n      parentPort.postMessage(data, transferList);\n      // console.log('posted message', data);\n    } else {\n      // @ts-ignore\n      globalThis.postMessage(data, transferList);\n    }\n  }\n}\n\n// Filter out noise messages sent to workers\nfunction isKnownMessage(message: MessageEvent<any>) {\n  const {type, data} = message;\n  return (\n    type === 'message' &&\n    data &&\n    typeof data.source === 'string' &&\n    data.source.startsWith('loaders.gl')\n  );\n}\n", "export const readFileAsArrayBuffer = null;\nexport const readFileAsText = null;\nexport const requireFromFile = null;\nexport const requireFromString = null;\n", "/* global importScripts */\nimport {global, isBrowser, isWorker} from '../env-utils/globals';\nimport * as node from '../node/require-utils.node';\nimport {assert} from '../env-utils/assert';\nimport {VERSION as __VERSION__} from '../env-utils/version';\n\n/**\n * TODO - unpkg.com doesn't seem to have a `latest` specifier for alpha releases...\n * 'beta' on beta branch, 'latest' on prod branch\n */\nconst LATEST = 'latest';\nconst VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : LATEST;\n\nconst loadLibraryPromises: Record<string, Promise<any>> = {}; // promises\n\n/**\n * Dynamically loads a library (\"module\")\n *\n * - wasm library: Array buffer is returned\n * - js library: Parse JS is returned\n *\n * Method depends on environment\n * - browser - script element is created and installed on document\n * - worker - eval is called on global context\n * - node - file is required\n *\n * @param libraryUrl\n * @param moduleName\n * @param options\n */\nexport async function loadLibrary(\n  libraryUrl: string,\n  moduleName: string | null = null,\n  options: object = {}\n): Promise<any> {\n  if (moduleName) {\n    libraryUrl = getLibraryUrl(libraryUrl, moduleName, options);\n  }\n\n  // Ensure libraries are only loaded once\n\n  loadLibraryPromises[libraryUrl] =\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\n    loadLibraryPromises[libraryUrl] || loadLibraryFromFile(libraryUrl);\n  return await loadLibraryPromises[libraryUrl];\n}\n\n// TODO - sort out how to resolve paths for main/worker and dev/prod\nexport function getLibraryUrl(library: string, moduleName?: string, options?: any): string {\n  // Check if already a URL\n  if (library.startsWith('http')) {\n    return library;\n  }\n\n  // Allow application to import and supply libraries through `options.modules`\n  const modules = options.modules || {};\n  if (modules[library]) {\n    return modules[library];\n  }\n\n  // Load from local files, not from CDN scripts in Node.js\n  // TODO - needs to locate the modules directory when installed!\n  if (!isBrowser) {\n    return `modules/${moduleName}/dist/libs/${library}`;\n  }\n\n  // In browser, load from external scripts\n  if (options.CDN) {\n    assert(options.CDN.startsWith('http'));\n    return `${options.CDN}/${moduleName}@${VERSION}/dist/libs/${library}`;\n  }\n\n  // TODO - loading inside workers requires paths relative to worker script location...\n  if (isWorker) {\n    return `../src/libs/${library}`;\n  }\n\n  return `modules/${moduleName}/src/libs/${library}`;\n}\n\nasync function loadLibraryFromFile(libraryUrl: string): Promise<any> {\n  if (libraryUrl.endsWith('wasm')) {\n    const response = await fetch(libraryUrl);\n    return await response.arrayBuffer();\n  }\n\n  if (!isBrowser) {\n    try {\n      return node && node.requireFromFile && (await node.requireFromFile(libraryUrl));\n    } catch {\n      return null;\n    }\n  }\n  if (isWorker) {\n    return importScripts(libraryUrl);\n  }\n  // TODO - fix - should be more secure than string parsing since observes CORS\n  // if (isBrowser) {\n  //   return await loadScriptFromFile(libraryUrl);\n  // }\n\n  const response = await fetch(libraryUrl);\n  const scriptSource = await response.text();\n  return loadLibraryFromString(scriptSource, libraryUrl);\n}\n\n/*\nasync function loadScriptFromFile(libraryUrl) {\n  const script = document.createElement('script');\n  script.src = libraryUrl;\n  return await new Promise((resolve, reject) => {\n    script.onload = data => {\n      resolve(data);\n    };\n    script.onerror = reject;\n  });\n}\n*/\n\n// TODO - Needs security audit...\n//  - Raw eval call\n//  - Potentially bypasses CORS\n// Upside is that this separates fetching and parsing\n// we could create a`LibraryLoader` or`ModuleLoader`\nfunction loadLibraryFromString(scriptSource: string, id: string): null | any {\n  if (!isBrowser) {\n    return node.requireFromString && node.requireFromString(scriptSource, id);\n  }\n\n  if (isWorker) {\n    // Use lvalue trick to make eval run in global scope\n    eval.call(global, scriptSource); // eslint-disable-line no-eval\n    // https://stackoverflow.com/questions/9107240/1-evalthis-vs-evalthis-in-javascript\n    // http://perfectionkills.com/global-eval-what-are-the-options/\n    return null;\n  }\n\n  const script = document.createElement('script');\n  script.id = id;\n  // most browsers like a separate text node but some throw an error. The second method covers those.\n  try {\n    script.appendChild(document.createTextNode(scriptSource));\n  } catch (e) {\n    script.text = scriptSource;\n  }\n  document.body.appendChild(script);\n  return null;\n}\n\n// TODO - technique for module injection into worker, from THREE.DracoLoader...\n/*\nfunction combineWorkerWithLibrary(worker, jsContent) {\n  var fn = wWorker.toString();\n  var body = [\n    '// injected',\n    jsContent,\n    '',\n    '// worker',\n    fn.substring(fn.indexOf('{') + 1, fn.lastIndexOf('}'))\n  ].join('\\n');\n  this.workerSourceURL = URL.createObjectURL(new Blob([body]));\n}\n*/\n", "/* eslint-disable no-restricted-globals */\nimport type {LoaderWithParser} from '../../types';\nimport {WorkerBody} from '@loaders.gl/worker-utils';\n// import {validateLoaderVersion} from './validate-loader-version';\n\nlet requestId = 0;\n\n/**\n * Set up a WebWorkerGlobalScope to talk with the main thread\n * @param loader\n */\nexport function createLoaderWorker(loader: LoaderWithParser) {\n  // Check that we are actually in a worker thread\n  if (!WorkerBody.inWorkerThread()) {\n    return;\n  }\n\n  WorkerBody.onmessage = async (type, payload) => {\n    switch (type) {\n      case 'process':\n        try {\n          // validateLoaderVersion(loader, data.source.split('@')[1]);\n\n          const {input, options = {}, context = {}} = payload;\n\n          const result = await parseData({\n            loader,\n            arrayBuffer: input,\n            options,\n            context: {\n              ...context,\n              parse: parseOnMainThread\n            }\n          });\n          WorkerBody.postMessage('done', {result});\n        } catch (error) {\n          const message = error instanceof Error ? error.message : '';\n          WorkerBody.postMessage('error', {error: message});\n        }\n        break;\n      default:\n    }\n  };\n}\n\nfunction parseOnMainThread(arrayBuffer: ArrayBuffer, options: {[key: string]: any}): Promise<void> {\n  return new Promise((resolve, reject) => {\n    const id = requestId++;\n\n    /**\n     */\n    const onMessage = (type, payload) => {\n      if (payload.id !== id) {\n        // not ours\n        return;\n      }\n\n      switch (type) {\n        case 'done':\n          WorkerBody.removeEventListener(onMessage);\n          resolve(payload.result);\n          break;\n\n        case 'error':\n          WorkerBody.removeEventListener(onMessage);\n          reject(payload.error);\n          break;\n\n        default:\n        // ignore\n      }\n    };\n\n    WorkerBody.addEventListener(onMessage);\n\n    // Ask the main thread to decode data\n    const payload = {id, input: arrayBuffer, options};\n    WorkerBody.postMessage('process', payload);\n  });\n}\n\n// TODO - Support byteOffset and byteLength (enabling parsing of embedded binaries without copies)\n// TODO - Why not support async loader.parse* funcs here?\n// TODO - Why not reuse a common function instead of reimplementing loader.parse* selection logic? Keeping loader small?\n// TODO - Lack of appropriate parser functions can be detected when we create worker, no need to wait until parse\nasync function parseData({loader, arrayBuffer, options, context}) {\n  let data;\n  let parser;\n  if (loader.parseSync || loader.parse) {\n    data = arrayBuffer;\n    parser = loader.parseSync || loader.parse;\n  } else if (loader.parseTextSync) {\n    const textDecoder = new TextDecoder();\n    data = textDecoder.decode(arrayBuffer);\n    parser = loader.parseTextSync;\n  } else {\n    throw new Error(`Could not load data with ${loader.name} loader`);\n  }\n\n  // TODO - proper merge in of loader options...\n  options = {\n    ...options,\n    modules: (loader && loader.options && loader.options.modules) || {},\n    worker: false\n  };\n\n  return await parser(data, {...options}, context, loader);\n}\n", "// Version constant cannot be imported, it needs to correspond to the build version of **this** module.\n// __VERSION__ is injected by babel-plugin-version-inline\n// @ts-ignore TS2304: Cannot find name '__VERSION__'.\nexport const VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : 'latest';\n", "import type {Loader, LoaderOptions} from '@loaders.gl/loader-utils';\nimport {isBrowser} from '@loaders.gl/worker-utils';\nimport type {DracoParseOptions} from './lib/draco-parser';\n// import type {DracoMeshData} from './types';\nimport {VERSION} from './lib/utils/version';\n\nexport type DracoLoaderOptions = LoaderOptions & {\n  draco?: DracoParseOptions & {\n    decoderType?: 'wasm' | 'js';\n    libraryPath?: string;\n    extraAttributes?;\n    attributeNameEntry?: string;\n    workerUrl?: string;\n  };\n};\n\nconst DEFAULT_DRACO_OPTIONS: DracoLoaderOptions = {\n  draco: {\n    decoderType: typeof WebAssembly === 'object' ? 'wasm' : 'js', // 'js' for IE11\n    libraryPath: 'libs/',\n    extraAttributes: {},\n    attributeNameEntry: undefined\n  }\n};\n\n/**\n * Worker loader for Draco3D compressed geometries\n */\nexport const DracoLoader = {\n  name: 'Draco',\n  id: isBrowser ? 'draco' : 'draco-nodejs',\n  module: 'draco',\n  shapes: ['mesh'],\n  version: VERSION,\n  worker: true,\n  extensions: ['drc'],\n  mimeTypes: ['application/octet-stream'],\n  binary: true,\n  tests: ['DRACO'],\n  options: DEFAULT_DRACO_OPTIONS\n};\n\nexport const _TypecheckDracoLoader: Loader = DracoLoader;\n", "// Mesh category utilities\n// TODO - move to mesh category module, or to math.gl/geometry module\nimport {TypedArray} from '../../types';\nimport {MeshAttributes} from './mesh-types';\n\ntype TypedArrays = {[key: string]: TypedArray};\n\n/**\n * Holds an axis aligned bounding box\n * TODO - make sure AxisAlignedBoundingBox in math.gl/culling understands this format (or change this format)\n */\ntype BoundingBox = [[number, number, number], [number, number, number]];\n\n/**\n * Get number of vertices in mesh\n * @param attributes\n */\nexport function getMeshSize(attributes: TypedArrays): number {\n  let size = 0;\n  for (const attributeName in attributes) {\n    const attribute = attributes[attributeName];\n    if (ArrayBuffer.isView(attribute)) {\n      // @ts-ignore DataView doesn't have BYTES_PER_ELEMENT\n      size += attribute.byteLength * attribute.BYTES_PER_ELEMENT;\n    }\n  }\n  return size;\n}\n\n/**\n * Get the (axis aligned) bounding box of a mesh\n * @param attributes\n * @returns array of two vectors representing the axis aligned bounding box\n */\n// eslint-disable-next-line complexity\nexport function getMeshBoundingBox(attributes: MeshAttributes): BoundingBox {\n  let minX = Infinity;\n  let minY = Infinity;\n  let minZ = Infinity;\n  let maxX = -Infinity;\n  let maxY = -Infinity;\n  let maxZ = -Infinity;\n\n  const positions = attributes.POSITION ? attributes.POSITION.value : [];\n  const len = positions && positions.length;\n\n  for (let i = 0; i < len; i += 3) {\n    const x = positions[i];\n    const y = positions[i + 1];\n    const z = positions[i + 2];\n\n    minX = x < minX ? x : minX;\n    minY = y < minY ? y : minY;\n    minZ = z < minZ ? z : minZ;\n\n    maxX = x > maxX ? x : maxX;\n    maxY = y > maxY ? y : maxY;\n    maxZ = z > maxZ ? z : maxZ;\n  }\n  return [\n    [minX, minY, minZ],\n    [maxX, maxY, maxZ]\n  ];\n}\n", "// Replacement for the external assert method to reduce bundle size\n// Note: We don't use the second \"message\" argument in calling code,\n// so no need to support it here\nexport function assert(condition: boolean, message?: string) {\n  if (!condition) {\n    throw new Error(message || 'loader assertion failed.');\n  }\n}\n", "import {assert} from '../../utils/assert';\nimport Field from './field';\n\nexport type SchemaMetadata = Map<string, any>;\n\n/**\n * ArrowJS `Schema` API-compatible class for row-based tables (returned from `DataTable`)\n * https://loaders.gl/arrowjs/docs/api-reference/schema\n */\nexport default class Schema {\n  fields: Field[];\n  // TODO - Arrow just allows Map<string, string>\n  metadata: SchemaMetadata;\n\n  constructor(fields: Field[], metadata?: SchemaMetadata) {\n    assert(Array.isArray(fields));\n    checkNames(fields);\n    // For kepler fields, create arrow compatible `Fields` that have kepler fields as `metadata`\n    this.fields = fields;\n    this.metadata = metadata || new Map();\n  }\n\n  // TODO - arrow only seems to compare fields, not metadata\n  compareTo(other: Schema): boolean {\n    if (this.metadata !== other.metadata) {\n      return false;\n    }\n    if (this.fields.length !== other.fields.length) {\n      return false;\n    }\n    for (let i = 0; i < this.fields.length; ++i) {\n      if (!this.fields[i].compareTo(other.fields[i])) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  select(...columnNames: string[]): Schema {\n    // Ensure column names reference valid fields\n    const nameMap = Object.create(null);\n    for (const name of columnNames) {\n      nameMap[name] = true;\n    }\n    const selectedFields = this.fields.filter((field) => nameMap[field.name]);\n    return new Schema(selectedFields, this.metadata);\n  }\n\n  selectAt(...columnIndices: number[]): Schema {\n    // Ensure column indices reference valid fields\n    const selectedFields = columnIndices.map((index) => this.fields[index]).filter(Boolean);\n    return new Schema(selectedFields, this.metadata);\n  }\n\n  assign(schemaOrFields: Schema | Field[]): Schema {\n    let fields: Field[];\n    let metadata: SchemaMetadata = this.metadata;\n\n    if (schemaOrFields instanceof Schema) {\n      const otherSchema = schemaOrFields;\n      fields = otherSchema.fields;\n      metadata = mergeMaps(mergeMaps(new Map(), this.metadata), otherSchema.metadata);\n    } else {\n      fields = schemaOrFields;\n    }\n\n    // Create a merged list of fields, overwrite fields in place, new fields at end\n    const fieldMap: {[key: string]: Field} = Object.create(null);\n\n    for (const field of this.fields) {\n      fieldMap[field.name] = field;\n    }\n\n    for (const field of fields) {\n      fieldMap[field.name] = field;\n    }\n\n    const mergedFields = Object.values(fieldMap);\n\n    return new Schema(mergedFields, metadata);\n  }\n}\n\n// Warn if any duplicated field names\nfunction checkNames(fields: Field[]): void {\n  const usedNames: Record<string, boolean> = {};\n  for (const field of fields) {\n    if (usedNames[field.name]) {\n      // eslint-disable-next-line\n      console.warn('Schema: duplicated field name', field.name, field);\n    }\n    usedNames[field.name] = true;\n  }\n}\n\nfunction mergeMaps<T>(m1: T, m2: T): T {\n  // @ts-ignore\n  return new Map([...(m1 || new Map()), ...(m2 || new Map())]);\n}\n", "import {DataType} from './type';\n\n/**\n * ArrowJS `Field` API-compatible class for row-based tables\n * https://loaders.gl/arrowjs/docs/api-reference/field\n * A field holds name, nullable, and metadata information about a table \"column\"\n * A Schema is essentially a list of fields\n */\nexport default class Field {\n  name: string;\n  type: DataType;\n  nullable: boolean;\n  metadata: Map<string, string>;\n\n  constructor(\n    name: string,\n    type: DataType,\n    nullable = false,\n    metadata: Map<string, string> = new Map()\n  ) {\n    this.name = name;\n    this.type = type;\n    this.nullable = nullable;\n    this.metadata = metadata;\n  }\n\n  get typeId(): number {\n    return this.type && this.type.typeId;\n  }\n\n  clone(): Field {\n    return new Field(this.name, this.type, this.nullable, this.metadata);\n  }\n\n  compareTo(other: this): boolean {\n    return (\n      this.name === other.name &&\n      this.type === other.type &&\n      this.nullable === other.nullable &&\n      this.metadata === other.metadata\n    );\n  }\n\n  toString(): string {\n    return `${this.type}${this.nullable ? ', nullable' : ''}${\n      this.metadata ? `, metadata: ${this.metadata}` : ''\n    }`;\n  }\n}\n", "// This code is adapted from ArrowJS https://github.com/apache/arrow\n// under Apache license http://www.apache.org/licenses/LICENSE-2.0\n\n/**\n * Main data type enumeration.\n *\n * Data types in this library are all *logical*. They can be expressed as\n * either a primitive physical type (bytes or bits of some fixed size), a\n * nested type consisting of other data types, or another data type (e.g. a\n * timestamp encoded as an int64).\n *\n * **Note**: Only enum values 0-17 (NONE through Map) are written to an Arrow\n * IPC payload.\n *\n * The rest of the values are specified here so TypeScript can narrow the type\n * signatures further beyond the base Arrow Types. The Arrow DataTypes include\n * metadata like `bitWidth` that impact the type signatures of the values we\n * accept and return.\n *\n * For example, the `Int8Vector` reads 1-byte numbers from an `Int8Array`, an\n * `Int32Vector` reads a 4-byte number from an `Int32Array`, and an `Int64Vector`\n * reads a pair of 4-byte lo, hi 32-bit integers as a zero-copy slice from the\n * underlying `Int32Array`.\n *\n * Library consumers benefit by knowing the narrowest type, since we can ensure\n * the types across all public methods are propagated, and never bail to `any`.\n * These values are _never_ used at runtime, and they will _never_ be written\n * to the flatbuffers metadata of serialized Arrow IPC payloads.\n */\nexport enum Type {\n  /** The default placeholder type */\n  NONE = 0,\n  /** A NULL type having no physical storage */\n  Null = 1,\n  /** Signed or unsigned 8, 16, 32, or 64-bit little-endian integer */\n  Int = 2,\n  /** 2, 4, or 8-byte floating point value */\n  Float = 3,\n  /** Variable-length bytes (no guarantee of UTF8-ness) */\n  Binary = 4,\n  /** UTF8 variable-length string as List<Char> */\n  Utf8 = 5,\n  /** Boolean as 1 bit, LSB bit-packed ordering */\n  Bool = 6,\n  /** Precision-and-scale-based decimal type. Storage type depends on the parameters. */\n  Decimal = 7,\n  /** int32_t days or int64_t milliseconds since the UNIX epoch */\n  Date = 8,\n  /** Time as signed 32 or 64-bit integer, representing either seconds, milliseconds, microseconds, or nanoseconds since midnight since midnight */\n  Time = 9,\n  /** Exact timestamp encoded with int64 since UNIX epoch (Default unit millisecond) */\n  Timestamp = 10,\n  /** YEAR_MONTH or DAY_TIME interval in SQL style */\n  Interval = 11,\n  /** A list of some logical data type */\n  List = 12,\n  /** Struct of logical types */\n  Struct = 13,\n  /** Union of logical types */\n  Union = 14,\n  /** Fixed-size binary. Each value occupies the same number of bytes */\n  FixedSizeBinary = 15,\n  /** Fixed-size list. Each value occupies the same number of bytes */\n  FixedSizeList = 16,\n  /** Map of named logical types */\n  Map = 17,\n\n  /** Dictionary aka Category type */\n  Dictionary = -1,\n  Int8 = -2,\n  Int16 = -3,\n  Int32 = -4,\n  Int64 = -5,\n  Uint8 = -6,\n  Uint16 = -7,\n  Uint32 = -8,\n  Uint64 = -9,\n  Float16 = -10,\n  Float32 = -11,\n  Float64 = -12,\n  DateDay = -13,\n  DateMillisecond = -14,\n  TimestampSecond = -15,\n  TimestampMillisecond = -16,\n  TimestampMicrosecond = -17,\n  TimestampNanosecond = -18,\n  TimeSecond = -19,\n  TimeMillisecond = -20,\n  TimeMicrosecond = -21,\n  TimeNanosecond = -22,\n  DenseUnion = -23,\n  SparseUnion = -24,\n  IntervalDayTime = -25,\n  IntervalYearMonth = -26\n}\n", "// This code is adapted from ArrowJS https://github.com/apache/arrow\n// under Apache license http://www.apache.org/licenses/LICENSE-2.0\n\nimport {Type} from './enum';\n\nimport Field from './field';\n\nexport {Type} from './enum';\n\nexport type TypedIntArray =\n  | Int8Array\n  | Uint8Array\n  | Int16Array\n  | Uint16Array\n  | Int32Array\n  | Uint32Array\n  | Int32Array\n  | Uint32Array\n  | Uint8ClampedArray;\n\nexport type TypedFloatArray = Float32Array | Float64Array;\n\nexport type TypedArray = TypedIntArray | TypedFloatArray;\n\nexport type AnyArrayType = Array<any> | TypedIntArray | TypedFloatArray;\n\nexport class DataType {\n  static isNull(x: any): boolean {\n    return x && x.typeId === Type.Null;\n  }\n  static isInt(x: any): boolean {\n    return x && x.typeId === Type.Int;\n  }\n  static isFloat(x: any): boolean {\n    return x && x.typeId === Type.Float;\n  }\n  static isBinary(x: any): boolean {\n    return x && x.typeId === Type.Binary;\n  }\n  static isUtf8(x: any): boolean {\n    return x && x.typeId === Type.Utf8;\n  }\n  static isBool(x: any): boolean {\n    return x && x.typeId === Type.Bool;\n  }\n  static isDecimal(x: any): boolean {\n    return x && x.typeId === Type.Decimal;\n  }\n  static isDate(x: any): boolean {\n    return x && x.typeId === Type.Date;\n  }\n  static isTime(x: any): boolean {\n    return x && x.typeId === Type.Time;\n  }\n  static isTimestamp(x: any): boolean {\n    return x && x.typeId === Type.Timestamp;\n  }\n  static isInterval(x: any): boolean {\n    return x && x.typeId === Type.Interval;\n  }\n  static isList(x: any): boolean {\n    return x && x.typeId === Type.List;\n  }\n  static isStruct(x: any): boolean {\n    return x && x.typeId === Type.Struct;\n  }\n  static isUnion(x: any): boolean {\n    return x && x.typeId === Type.Union;\n  }\n  static isFixedSizeBinary(x: any): boolean {\n    return x && x.typeId === Type.FixedSizeBinary;\n  }\n  static isFixedSizeList(x: any): boolean {\n    return x && x.typeId === Type.FixedSizeList;\n  }\n  static isMap(x: any): boolean {\n    return x && x.typeId === Type.Map;\n  }\n  static isDictionary(x: any): boolean {\n    return x && x.typeId === Type.Dictionary;\n  }\n\n  get typeId(): Type {\n    return Type.NONE;\n  }\n\n  // get ArrayType(): AnyArrayType {\n  //   return Int8Array;\n  // }\n\n  // get ArrayType() { return Array; }\n  compareTo(other: DataType): boolean {\n    // TODO\n    return this === other; // comparer.visit(this, other);\n  }\n}\n\n// NULL\n\nexport class Null extends DataType {\n  get typeId(): Type {\n    return Type.Null;\n  }\n  get [Symbol.toStringTag](): string {\n    return 'Null';\n  }\n  toString(): string {\n    return 'Null';\n  }\n}\n\n// BOOLEANS\n\nexport class Bool extends DataType {\n  get typeId(): Type {\n    return Type.Bool;\n  }\n  // get ArrayType() {\n  //   return Uint8Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Bool';\n  }\n  toString(): string {\n    return 'Bool';\n  }\n}\n\n// INTS\n\nexport class Int extends DataType {\n  readonly isSigned: boolean;\n  readonly bitWidth: number;\n  constructor(isSigned, bitWidth) {\n    super();\n    this.isSigned = isSigned;\n    this.bitWidth = bitWidth;\n  }\n  get typeId(): Type {\n    return Type.Int;\n  }\n  // get ArrayType() {\n  //   switch (this.bitWidth) {\n  //     case 8:\n  //       return this.isSigned ? Int8Array : Uint8Array;\n  //     case 16:\n  //       return this.isSigned ? Int16Array : Uint16Array;\n  //     case 32:\n  //       return this.isSigned ? Int32Array : Uint32Array;\n  //     case 64:\n  //       return this.isSigned ? Int32Array : Uint32Array;\n  //     default:\n  //       throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`);\n  //   }\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Int';\n  }\n  toString(): string {\n    return `${this.isSigned ? 'I' : 'Ui'}nt${this.bitWidth}`;\n  }\n}\n\nexport class Int8 extends Int {\n  constructor() {\n    super(true, 8);\n  }\n}\nexport class Int16 extends Int {\n  constructor() {\n    super(true, 16);\n  }\n}\nexport class Int32 extends Int {\n  constructor() {\n    super(true, 32);\n  }\n}\nexport class Int64 extends Int {\n  constructor() {\n    super(true, 64);\n  }\n}\nexport class Uint8 extends Int {\n  constructor() {\n    super(false, 8);\n  }\n}\nexport class Uint16 extends Int {\n  constructor() {\n    super(false, 16);\n  }\n}\nexport class Uint32 extends Int {\n  constructor() {\n    super(false, 32);\n  }\n}\nexport class Uint64 extends Int {\n  constructor() {\n    super(false, 64);\n  }\n}\n\n// FLOATS\n\nconst Precision = {\n  HALF: 16,\n  SINGLE: 32,\n  DOUBLE: 64\n};\n\nexport class Float extends DataType {\n  readonly precision: number;\n  constructor(precision) {\n    super();\n    this.precision = precision;\n  }\n  get typeId(): Type {\n    return Type.Float;\n  }\n  // get ArrayType() {\n  //   switch (this.precision) {\n  //     case Precision.HALF:\n  //       return Uint16Array;\n  //     case Precision.SINGLE:\n  //       return Float32Array;\n  //     case Precision.DOUBLE:\n  //       return Float64Array;\n  //     default:\n  //       throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`);\n  //   }\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Float';\n  }\n  toString(): string {\n    return `Float${this.precision}`;\n  }\n}\n\nexport class Float16 extends Float {\n  constructor() {\n    super(Precision.HALF);\n  }\n}\nexport class Float32 extends Float {\n  constructor() {\n    super(Precision.SINGLE);\n  }\n}\nexport class Float64 extends Float {\n  constructor() {\n    super(Precision.DOUBLE);\n  }\n}\n\nexport class Binary extends DataType {\n  constructor() {\n    super();\n  }\n  get typeId() {\n    return Type.Binary;\n  }\n  toString() {\n    return 'Binary';\n  }\n  get [Symbol.toStringTag]() {\n    return 'Binary';\n  }\n}\n\n// STRINGS\n\nexport class Utf8 extends DataType {\n  get typeId(): Type {\n    return Type.Utf8;\n  }\n  // get ArrayType() {\n  //   return Uint8Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Utf8';\n  }\n  toString(): string {\n    return 'Utf8';\n  }\n}\n\n// DATES, TIMES AND INTERVALS\n\nconst DateUnit = {\n  DAY: 0,\n  MILLISECOND: 1\n};\n\nexport class Date extends DataType {\n  readonly unit: number;\n  constructor(unit) {\n    super();\n    this.unit = unit;\n  }\n  get typeId(): Type {\n    return Type.Date;\n  }\n  // get ArrayType() {\n  //   return Int32Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Date';\n  }\n  toString(): string {\n    return `Date${(this.unit + 1) * 32}<${DateUnit[this.unit]}>`;\n  }\n}\n\nexport class DateDay extends Date {\n  constructor() {\n    super(DateUnit.DAY);\n  }\n}\nexport class DateMillisecond extends Date {\n  constructor() {\n    super(DateUnit.MILLISECOND);\n  }\n}\n\nconst TimeUnit = {\n  SECOND: 1,\n  MILLISECOND: 1e3,\n  MICROSECOND: 1e6,\n  NANOSECOND: 1e9\n};\n\nexport class Time extends DataType {\n  readonly unit: any;\n  readonly bitWidth: number;\n\n  constructor(unit: any, bitWidth: number) {\n    super();\n    this.unit = unit;\n    this.bitWidth = bitWidth;\n  }\n  get typeId(): Type {\n    return Type.Time;\n  }\n  toString(): string {\n    return `Time${this.bitWidth}<${TimeUnit[this.unit]}>`;\n  }\n  get [Symbol.toStringTag](): string {\n    return 'Time';\n  }\n  // get ArrayType() {\n  //   return Int32Array;\n  // }\n}\n\nexport class TimeSecond extends Time {\n  constructor() {\n    super(TimeUnit.SECOND, 32);\n  }\n}\nexport class TimeMillisecond extends Time {\n  constructor() {\n    super(TimeUnit.MILLISECOND, 32);\n  }\n}\n// export class TimeMicrosecond extends Time { constructor() { super(TimeUnit.MICROSECOND, 64); } }\n// export class TimeNanosecond extends Time { constructor() { super(TimeUnit.NANOSECOND, 64); } }\n\nexport class Timestamp extends DataType {\n  readonly unit: any;\n  readonly timezone: any;\n\n  constructor(unit: any, timezone = null) {\n    super();\n    this.unit = unit;\n    this.timezone = timezone;\n  }\n  get typeId(): Type {\n    return Type.Timestamp;\n  }\n  // get ArrayType() {\n  //   return Int32Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Timestamp';\n  }\n  toString(): string {\n    return `Timestamp<${TimeUnit[this.unit]}${this.timezone ? `, ${this.timezone}` : ''}>`;\n  }\n}\n\nexport class TimestampSecond extends Timestamp {\n  constructor(timezone = null) {\n    super(TimeUnit.SECOND, timezone);\n  }\n}\nexport class TimestampMillisecond extends Timestamp {\n  constructor(timezone = null) {\n    super(TimeUnit.MILLISECOND, timezone);\n  }\n}\nexport class TimestampMicrosecond extends Timestamp {\n  constructor(timezone = null) {\n    super(TimeUnit.MICROSECOND, timezone);\n  }\n}\nexport class TimestampNanosecond extends Timestamp {\n  constructor(timezone = null) {\n    super(TimeUnit.NANOSECOND, timezone);\n  }\n}\n\nconst IntervalUnit = {\n  DAY_TIME: 0,\n  YEAR_MONTH: 1\n};\n\nexport class Interval extends DataType {\n  readonly unit: number;\n  constructor(unit: number) {\n    super();\n    this.unit = unit;\n  }\n  get typeId(): Type {\n    return Type.Interval;\n  }\n  // get ArrayType() {\n  //   return Int32Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Interval';\n  }\n  toString(): string {\n    return `Interval<${IntervalUnit[this.unit]}>`;\n  }\n}\n\nexport class IntervalDayTime extends Interval {\n  constructor() {\n    super(IntervalUnit.DAY_TIME);\n  }\n}\nexport class IntervalYearMonth extends Interval {\n  constructor() {\n    super(IntervalUnit.YEAR_MONTH);\n  }\n}\n\nexport class FixedSizeList extends DataType {\n  readonly listSize: number;\n  readonly children: Field[];\n\n  constructor(listSize: number, child: Field) {\n    super();\n    this.listSize = listSize;\n    this.children = [child];\n  }\n  get typeId(): Type {\n    return Type.FixedSizeList;\n  }\n  get valueType() {\n    return this.children[0].type;\n  }\n  get valueField() {\n    return this.children[0];\n  }\n  // get ArrayType() {\n  //   return this.valueType.ArrayType;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'FixedSizeList';\n  }\n  toString(): string {\n    return `FixedSizeList[${this.listSize}]<${this.valueType}>`;\n  }\n}\n\nexport class Struct extends DataType {\n  public readonly children: Field[];\n\n  constructor(children: Field[]) {\n    super();\n    this.children = children;\n  }\n\n  public get typeId() {\n    return Type.Struct;\n  }\n  public toString() {\n    return `Struct<{${this.children.map((f) => `${f.name}:${f.type}`).join(', ')}}>`;\n  }\n  get [Symbol.toStringTag](): string {\n    return 'Struct';\n  }\n}\n", "import type {TypedArray} from '../../types';\nimport {\n  DataType,\n  Float32,\n  Float64,\n  Int16,\n  Int32,\n  Int8,\n  Uint16,\n  Uint32,\n  Uint8\n} from '../schema/schema';\n\nexport function getArrowTypeFromTypedArray(array: TypedArray): DataType {\n  switch (array.constructor) {\n    case Int8Array:\n      return new Int8();\n    case Uint8Array:\n      return new Uint8();\n    case Int16Array:\n      return new Int16();\n    case Uint16Array:\n      return new Uint16();\n    case Int32Array:\n      return new Int32();\n    case Uint32Array:\n      return new Uint32();\n    case Float32Array:\n      return new Float32();\n    case Float64Array:\n      return new Float64();\n    default:\n      throw new Error('array type not supported');\n  }\n}\n", "import {MeshAttribute, MeshAttributes} from './mesh-types';\nimport {Schem<PERSON>, Field, FixedSizeList} from '../../lib/schema/schema';\nimport {getArrowTypeFromTypedArray} from '../../lib/arrow/arrow-like-type-utils';\n\n/**\n * Create a schema for mesh attributes data\n * @param attributes\n * @param metadata\n * @returns\n */\nexport function deduceMeshSchema(\n  attributes: MeshAttributes,\n  metadata?: Map<string, string>\n): Schema {\n  const fields = deduceMeshFields(attributes);\n  return new Schema(fields, metadata);\n}\n\n/**\n * Create arrow-like schema field for mesh attribute\n * @param attributeName\n * @param attribute\n * @param optionalMetadata\n * @returns\n */\nexport function deduceMeshField(\n  attributeName: string,\n  attribute: MeshAttribute,\n  optionalMetadata?: Map<string, string>\n): Field {\n  const type = getArrowTypeFromTypedArray(attribute.value);\n  const metadata = optionalMetadata ? optionalMetadata : makeMeshAttributeMetadata(attribute);\n  const field = new Field(\n    attributeName,\n    new FixedSizeList(attribute.size, new Field('value', type)),\n    false,\n    metadata\n  );\n  return field;\n}\n\n/**\n * Create fields array for mesh attributes\n * @param attributes\n * @returns\n */\nfunction deduceMeshFields(attributes: MeshAttributes): Field[] {\n  const fields: Field[] = [];\n  for (const attributeName in attributes) {\n    const attribute: MeshAttribute = attributes[attributeName];\n    fields.push(deduceMeshField(attributeName, attribute));\n  }\n  return fields;\n}\n\n/**\n * Make metadata by mesh attribute properties\n * @param attribute\n * @returns\n */\nexport function makeMeshAttributeMetadata(attribute: MeshAttribute): Map<string, string> {\n  const result = new Map();\n  if ('byteOffset' in attribute) {\n    result.set('byteOffset', attribute.byteOffset!.toString(10));\n  }\n  if ('byteStride' in attribute) {\n    result.set('byteStride', attribute.byteStride!.toString(10));\n  }\n  if ('normalized' in attribute) {\n    result.set('normalized', attribute.normalized!.toString());\n  }\n  return result;\n}\n", "import {deduce<PERSON><PERSON><PERSON><PERSON>, MeshAttribute} from '@loaders.gl/schema';\nimport {Schema, Field} from '@loaders.gl/schema';\nimport type {DracoAttribute, DracoLoaderData, DracoMetadataEntry} from '../draco-types';\n\n/** Extract an arrow-like schema from a Draco mesh */\nexport function getDracoSchema(\n  attributes: {[attributeName: string]: MeshAttribute},\n  loaderData: DracoLoaderData,\n  indices?: MeshAttribute\n): Schema {\n  const metadataMap = makeMetadata(loaderData.metadata);\n  const fields: Field[] = [];\n  const namedLoaderDataAttributes = transformAttributesLoaderData(loaderData.attributes);\n  for (const attributeName in attributes) {\n    const attribute = attributes[attributeName];\n    const field = getArrowFieldFromAttribute(\n      attributeName,\n      attribute,\n      namedLoaderDataAttributes[attributeName]\n    );\n    fields.push(field);\n  }\n  if (indices) {\n    const indicesField = getArrowFieldFromAttribute('indices', indices);\n    fields.push(indicesField);\n  }\n  return new Schema(fields, metadataMap);\n}\n\nfunction transformAttributesLoaderData(loaderData: {[key: number]: DracoAttribute}): {\n  [attributeName: string]: DracoAttribute;\n} {\n  const result: {[attributeName: string]: DracoAttribute} = {};\n  for (const key in loaderData) {\n    const dracoAttribute = loaderData[key];\n    result[dracoAttribute.name || 'undefined'] = dracoAttribute;\n  }\n  return result;\n}\n\nfunction getArrowFieldFromAttribute(\n  attributeName: string,\n  attribute: MeshAttribute,\n  loaderData?: DracoAttribute\n): Field {\n  const metadataMap = loaderData ? makeMetadata(loaderData.metadata) : undefined;\n  const field = deduceMeshField(attributeName, attribute, metadataMap);\n  return field;\n}\n\nfunction makeMetadata(metadata: {[key: string]: DracoMetadataEntry}): Map<string, string> {\n  const metadataMap = new Map();\n  for (const key in metadata) {\n    metadataMap.set(`${key}.string`, JSON.stringify(metadata[key]));\n  }\n  return metadataMap;\n}\n", "/* eslint-disable camelcase */\n\nimport type {TypedArray, MeshAttribute, MeshGeometry} from '@loaders.gl/schema';\n\n// Draco types (input)\nimport type {\n  Draco3D,\n  Decoder,\n  Mesh,\n  PointCloud,\n  PointAttribute,\n  Metadata,\n  MetadataQuerier,\n  DracoInt32Array,\n  draco_DataType\n} from '../draco3d/draco3d-types';\n\n// Parsed data types (output)\nimport type {\n  DracoMesh,\n  DracoLoaderData,\n  DracoAttribute,\n  DracoMetadataEntry,\n  DracoQuantizationTransform,\n  DracoOctahedronTransform\n} from './draco-types';\n\nimport {getMeshBoundingBox} from '@loaders.gl/schema';\nimport {getDracoSchema} from './utils/get-draco-schema';\n\n/**\n * @param topology - How triangle indices should be generated (mesh only)\n * @param attributeNameEntry\n * @param extraAttributes\n * @param quantizedAttributes\n * @param octahedronAttributes\n */\nexport type DracoParseOptions = {\n  topology?: 'triangle-list' | 'triangle-strip';\n  attributeNameEntry?: string;\n  extraAttributes?: {[uniqueId: string]: number};\n  quantizedAttributes?: ('POSITION' | 'NORMAL' | 'COLOR' | 'TEX_COORD' | 'GENERIC')[];\n  octahedronAttributes?: ('POSITION' | 'NORMAL' | 'COLOR' | 'TEX_COORD' | 'GENERIC')[];\n};\n\n// @ts-ignore\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst GEOMETRY_TYPE = {\n  TRIANGULAR_MESH: 0,\n  POINT_CLOUD: 1\n};\n\n// Native Draco attribute names to GLTF attribute names.\nconst DRACO_TO_GLTF_ATTRIBUTE_NAME_MAP = {\n  POSITION: 'POSITION',\n  NORMAL: 'NORMAL',\n  COLOR: 'COLOR_0',\n  TEX_COORD: 'TEXCOORD_0'\n};\n\nconst DRACO_DATA_TYPE_TO_TYPED_ARRAY_MAP = {\n  1: Int8Array,\n  2: Uint8Array,\n  3: Int16Array,\n  4: Uint16Array,\n  5: Int32Array,\n  6: Uint32Array,\n  9: Float32Array\n};\n\nconst INDEX_ITEM_SIZE = 4;\n\nexport default class DracoParser {\n  draco: Draco3D;\n  decoder: Decoder;\n  metadataQuerier: MetadataQuerier;\n\n  // draco - the draco decoder, either import `draco3d` or load dynamically\n  constructor(draco: Draco3D) {\n    this.draco = draco;\n    this.decoder = new this.draco.Decoder();\n    this.metadataQuerier = new this.draco.MetadataQuerier();\n  }\n\n  /**\n   * Destroy draco resources\n   */\n  destroy(): void {\n    this.draco.destroy(this.decoder);\n    this.draco.destroy(this.metadataQuerier);\n  }\n\n  /**\n   * NOTE: caller must call `destroyGeometry` on the return value after using it\n   * @param arrayBuffer\n   * @param options\n   */\n  parseSync(arrayBuffer: ArrayBuffer, options: DracoParseOptions = {}): DracoMesh {\n    const buffer = new this.draco.DecoderBuffer();\n    buffer.Init(new Int8Array(arrayBuffer), arrayBuffer.byteLength);\n\n    this._disableAttributeTransforms(options);\n\n    const geometry_type = this.decoder.GetEncodedGeometryType(buffer);\n    const dracoGeometry =\n      geometry_type === this.draco.TRIANGULAR_MESH\n        ? new this.draco.Mesh()\n        : new this.draco.PointCloud();\n\n    try {\n      let dracoStatus;\n      switch (geometry_type) {\n        case this.draco.TRIANGULAR_MESH:\n          dracoStatus = this.decoder.DecodeBufferToMesh(buffer, dracoGeometry as Mesh);\n          break;\n\n        case this.draco.POINT_CLOUD:\n          dracoStatus = this.decoder.DecodeBufferToPointCloud(buffer, dracoGeometry);\n          break;\n\n        default:\n          throw new Error('DRACO: Unknown geometry type.');\n      }\n\n      if (!dracoStatus.ok() || !dracoGeometry.ptr) {\n        const message = `DRACO decompression failed: ${dracoStatus.error_msg()}`;\n        // console.error(message);\n        throw new Error(message);\n      }\n\n      const loaderData = this._getDracoLoaderData(dracoGeometry, geometry_type, options);\n\n      const geometry = this._getMeshData(dracoGeometry, loaderData, options);\n\n      const boundingBox = getMeshBoundingBox(geometry.attributes);\n\n      const schema = getDracoSchema(geometry.attributes, loaderData, geometry.indices);\n\n      const data: DracoMesh = {\n        loader: 'draco',\n        loaderData,\n        header: {\n          vertexCount: dracoGeometry.num_points(),\n          boundingBox\n        },\n        ...geometry,\n        schema\n      };\n      return data;\n    } finally {\n      this.draco.destroy(buffer);\n      if (dracoGeometry) {\n        this.draco.destroy(dracoGeometry);\n      }\n    }\n  }\n\n  // Draco specific \"loader data\"\n\n  /**\n   * Extract\n   * @param dracoGeometry\n   * @param geometry_type\n   * @param options\n   * @returns\n   */\n  _getDracoLoaderData(\n    dracoGeometry: Mesh | PointCloud,\n    geometry_type,\n    options: DracoParseOptions\n  ): DracoLoaderData {\n    const metadata = this._getTopLevelMetadata(dracoGeometry);\n    const attributes = this._getDracoAttributes(dracoGeometry, options);\n\n    return {\n      geometry_type,\n      num_attributes: dracoGeometry.num_attributes(),\n      num_points: dracoGeometry.num_points(),\n      num_faces: dracoGeometry instanceof this.draco.Mesh ? dracoGeometry.num_faces() : 0,\n      metadata,\n      attributes\n    };\n  }\n\n  /**\n   * Extract all draco provided information and metadata for each attribute\n   * @param dracoGeometry\n   * @param options\n   * @returns\n   */\n  _getDracoAttributes(\n    dracoGeometry: Mesh | PointCloud,\n    options: DracoParseOptions\n  ): {[unique_id: number]: DracoAttribute} {\n    const dracoAttributes: {[unique_id: number]: DracoAttribute} = {};\n\n    for (let attributeId = 0; attributeId < dracoGeometry.num_attributes(); attributeId++) {\n      // Note: Draco docs do not seem clear on `GetAttribute` ids just being a zero-based index,\n      // but it does seems to work this way\n      const dracoAttribute = this.decoder.GetAttribute(dracoGeometry, attributeId);\n\n      const metadata = this._getAttributeMetadata(dracoGeometry, attributeId);\n\n      dracoAttributes[dracoAttribute.unique_id()] = {\n        unique_id: dracoAttribute.unique_id(),\n        attribute_type: dracoAttribute.attribute_type(),\n        data_type: dracoAttribute.data_type(),\n        num_components: dracoAttribute.num_components(),\n\n        byte_offset: dracoAttribute.byte_offset(),\n        byte_stride: dracoAttribute.byte_stride(),\n        normalized: dracoAttribute.normalized(),\n        attribute_index: attributeId,\n\n        metadata\n      };\n\n      // Add transformation parameters for any attributes app wants untransformed\n      const quantization = this._getQuantizationTransform(dracoAttribute, options);\n      if (quantization) {\n        dracoAttributes[dracoAttribute.unique_id()].quantization_transform = quantization;\n      }\n\n      const octahedron = this._getOctahedronTransform(dracoAttribute, options);\n      if (octahedron) {\n        dracoAttributes[dracoAttribute.unique_id()].octahedron_transform = octahedron;\n      }\n    }\n\n    return dracoAttributes;\n  }\n\n  /**\n   * Get standard loaders.gl mesh category data\n   * Extracts the geometry from draco\n   * @param dracoGeometry\n   * @param options\n   */\n  _getMeshData(\n    dracoGeometry: Mesh | PointCloud,\n    loaderData: DracoLoaderData,\n    options: DracoParseOptions\n  ): MeshGeometry {\n    const attributes = this._getMeshAttributes(loaderData, dracoGeometry, options);\n\n    const positionAttribute = attributes.POSITION;\n    if (!positionAttribute) {\n      throw new Error('DRACO: No position attribute found.');\n    }\n\n    // For meshes, we need indices to define the faces.\n    if (dracoGeometry instanceof this.draco.Mesh) {\n      switch (options.topology) {\n        case 'triangle-strip':\n          return {\n            topology: 'triangle-strip',\n            mode: 4, // GL.TRIANGLES\n            attributes,\n            indices: {\n              value: this._getTriangleStripIndices(dracoGeometry),\n              size: 1\n            }\n          };\n        case 'triangle-list':\n        default:\n          return {\n            topology: 'triangle-list',\n            mode: 5, // GL.TRIANGLE_STRIP\n            attributes,\n            indices: {\n              value: this._getTriangleListIndices(dracoGeometry),\n              size: 1\n            }\n          };\n      }\n    }\n\n    // PointCloud - must come last as Mesh inherits from PointCloud\n    return {\n      topology: 'point-list',\n      mode: 0, // GL.POINTS\n      attributes\n    };\n  }\n\n  _getMeshAttributes(\n    loaderData: DracoLoaderData,\n    dracoGeometry: Mesh | PointCloud,\n    options: DracoParseOptions\n  ): {[attributeName: string]: MeshAttribute} {\n    const attributes: {[key: string]: MeshAttribute} = {};\n\n    for (const loaderAttribute of Object.values(loaderData.attributes)) {\n      const attributeName = this._deduceAttributeName(loaderAttribute, options);\n      loaderAttribute.name = attributeName;\n      const {value, size} = this._getAttributeValues(dracoGeometry, loaderAttribute);\n      attributes[attributeName] = {\n        value,\n        size,\n        byteOffset: loaderAttribute.byte_offset,\n        byteStride: loaderAttribute.byte_stride,\n        normalized: loaderAttribute.normalized\n      };\n    }\n\n    return attributes;\n  }\n\n  // MESH INDICES EXTRACTION\n\n  /**\n   * For meshes, we need indices to define the faces.\n   * @param dracoGeometry\n   */\n  _getTriangleListIndices(dracoGeometry: Mesh) {\n    // Example on how to retrieve mesh and attributes.\n    const numFaces = dracoGeometry.num_faces();\n    const numIndices = numFaces * 3;\n    const byteLength = numIndices * INDEX_ITEM_SIZE;\n\n    const ptr = this.draco._malloc(byteLength);\n    try {\n      this.decoder.GetTrianglesUInt32Array(dracoGeometry, byteLength, ptr);\n      return new Uint32Array(this.draco.HEAPF32.buffer, ptr, numIndices).slice();\n    } finally {\n      this.draco._free(ptr);\n    }\n  }\n\n  /**\n   * For meshes, we need indices to define the faces.\n   * @param dracoGeometry\n   */\n  _getTriangleStripIndices(dracoGeometry: Mesh) {\n    const dracoArray = new this.draco.DracoInt32Array();\n    try {\n      /* const numStrips = */ this.decoder.GetTriangleStripsFromMesh(dracoGeometry, dracoArray);\n      return getUint32Array(dracoArray);\n    } finally {\n      this.draco.destroy(dracoArray);\n    }\n  }\n\n  /**\n   *\n   * @param dracoGeometry\n   * @param dracoAttribute\n   * @param attributeName\n   */\n  _getAttributeValues(\n    dracoGeometry: Mesh | PointCloud,\n    attribute: DracoAttribute\n  ): {value: TypedArray; size: number} {\n    const TypedArrayCtor = DRACO_DATA_TYPE_TO_TYPED_ARRAY_MAP[attribute.data_type];\n    const numComponents = attribute.num_components;\n    const numPoints = dracoGeometry.num_points();\n    const numValues = numPoints * numComponents;\n\n    const byteLength = numValues * TypedArrayCtor.BYTES_PER_ELEMENT;\n    const dataType = getDracoDataType(this.draco, TypedArrayCtor);\n\n    let value: TypedArray;\n\n    const ptr = this.draco._malloc(byteLength);\n    try {\n      const dracoAttribute = this.decoder.GetAttribute(dracoGeometry, attribute.attribute_index);\n      this.decoder.GetAttributeDataArrayForAllPoints(\n        dracoGeometry,\n        dracoAttribute,\n        dataType,\n        byteLength,\n        ptr\n      );\n      value = new TypedArrayCtor(this.draco.HEAPF32.buffer, ptr, numValues).slice();\n    } finally {\n      this.draco._free(ptr);\n    }\n\n    return {value, size: numComponents};\n  }\n\n  // Attribute names\n\n  /** \n   * DRACO does not store attribute names - We need to deduce an attribute name\n   * for each attribute\n  _getAttributeNames(\n    dracoGeometry: Mesh | PointCloud,\n    options: DracoParseOptions\n  ): {[unique_id: number]: string} {\n    const attributeNames: {[unique_id: number]: string} = {};\n    for (let attributeId = 0; attributeId < dracoGeometry.num_attributes(); attributeId++) {\n      const dracoAttribute = this.decoder.GetAttribute(dracoGeometry, attributeId);\n      const attributeName = this._deduceAttributeName(dracoAttribute, options);\n      attributeNames[attributeName] = attributeName;\n    }\n    return attributeNames;\n  }\n   */\n\n  /**\n   * Deduce an attribute name.\n   * @note DRACO does not save attribute names, just general type (POSITION, COLOR)\n   * to help optimize compression. We generate GLTF compatible names for the Draco-recognized\n   * types\n   * @param attributeData\n   */\n  _deduceAttributeName(attribute: DracoAttribute, options: DracoParseOptions): string {\n    // Deduce name based on application provided map\n    const uniqueId = attribute.unique_id;\n    for (const [attributeName, attributeUniqueId] of Object.entries(\n      options.extraAttributes || {}\n    )) {\n      if (attributeUniqueId === uniqueId) {\n        return attributeName;\n      }\n    }\n\n    // Deduce name based on attribute type\n    const thisAttributeType = attribute.attribute_type;\n    for (const dracoAttributeConstant in DRACO_TO_GLTF_ATTRIBUTE_NAME_MAP) {\n      const attributeType = this.draco[dracoAttributeConstant];\n      if (attributeType === thisAttributeType) {\n        // TODO - Return unique names if there multiple attributes per type\n        // (e.g. multiple TEX_COORDS or COLORS)\n        return DRACO_TO_GLTF_ATTRIBUTE_NAME_MAP[dracoAttributeConstant];\n      }\n    }\n\n    // Look up in metadata\n    // TODO - shouldn't this have priority?\n    const entryName = options.attributeNameEntry || 'name';\n    if (attribute.metadata[entryName]) {\n      return attribute.metadata[entryName].string;\n    }\n\n    // Attribute of \"GENERIC\" type, we need to assign some name\n    return `CUSTOM_ATTRIBUTE_${uniqueId}`;\n  }\n\n  // METADATA EXTRACTION\n\n  /** Get top level metadata */\n  _getTopLevelMetadata(dracoGeometry: Mesh | PointCloud) {\n    const dracoMetadata = this.decoder.GetMetadata(dracoGeometry);\n    return this._getDracoMetadata(dracoMetadata);\n  }\n\n  /** Get per attribute metadata */\n  _getAttributeMetadata(dracoGeometry: Mesh | PointCloud, attributeId: number) {\n    const dracoMetadata = this.decoder.GetAttributeMetadata(dracoGeometry, attributeId);\n    return this._getDracoMetadata(dracoMetadata);\n  }\n\n  /**\n   * Extract metadata field values\n   * @param dracoMetadata\n   * @returns\n   */\n  _getDracoMetadata(dracoMetadata: Metadata): {[entry: string]: DracoMetadataEntry} {\n    // The not so wonderful world of undocumented Draco APIs :(\n    if (!dracoMetadata || !dracoMetadata.ptr) {\n      return {};\n    }\n    const result = {};\n    const numEntries = this.metadataQuerier.NumEntries(dracoMetadata);\n    for (let entryIndex = 0; entryIndex < numEntries; entryIndex++) {\n      const entryName = this.metadataQuerier.GetEntryName(dracoMetadata, entryIndex);\n      result[entryName] = this._getDracoMetadataField(dracoMetadata, entryName);\n    }\n    return result;\n  }\n\n  /**\n   * Extracts possible values for one metadata entry by name\n   * @param dracoMetadata\n   * @param entryName\n   */\n  _getDracoMetadataField(dracoMetadata: Metadata, entryName: string): DracoMetadataEntry {\n    const dracoArray = new this.draco.DracoInt32Array();\n    try {\n      // Draco metadata fields can hold int32 arrays\n      this.metadataQuerier.GetIntEntryArray(dracoMetadata, entryName, dracoArray);\n      const intArray = getInt32Array(dracoArray);\n      return {\n        int: this.metadataQuerier.GetIntEntry(dracoMetadata, entryName),\n        string: this.metadataQuerier.GetStringEntry(dracoMetadata, entryName),\n        double: this.metadataQuerier.GetDoubleEntry(dracoMetadata, entryName),\n        intArray\n      };\n    } finally {\n      this.draco.destroy(dracoArray);\n    }\n  }\n\n  // QUANTIZED ATTRIBUTE SUPPORT (NO DECOMPRESSION)\n\n  /** Skip transforms for specific attribute types */\n  _disableAttributeTransforms(options: DracoParseOptions) {\n    const {quantizedAttributes = [], octahedronAttributes = []} = options;\n    const skipAttributes = [...quantizedAttributes, ...octahedronAttributes];\n    for (const dracoAttributeName of skipAttributes) {\n      this.decoder.SkipAttributeTransform(this.draco[dracoAttributeName]);\n    }\n  }\n\n  /**\n   * Extract (and apply?) Position Transform\n   * @todo not used\n   */\n  _getQuantizationTransform(\n    dracoAttribute: PointAttribute,\n    options: DracoParseOptions\n  ): DracoQuantizationTransform | null {\n    const {quantizedAttributes = []} = options;\n    const attribute_type = dracoAttribute.attribute_type();\n    const skip = quantizedAttributes.map((type) => this.decoder[type]).includes(attribute_type);\n    if (skip) {\n      const transform = new this.draco.AttributeQuantizationTransform();\n      try {\n        if (transform.InitFromAttribute(dracoAttribute)) {\n          return {\n            quantization_bits: transform.quantization_bits(),\n            range: transform.range(),\n            min_values: new Float32Array([1, 2, 3]).map((i) => transform.min_value(i))\n          };\n        }\n      } finally {\n        this.draco.destroy(transform);\n      }\n    }\n    return null;\n  }\n\n  _getOctahedronTransform(\n    dracoAttribute: PointAttribute,\n    options: DracoParseOptions\n  ): DracoOctahedronTransform | null {\n    const {octahedronAttributes = []} = options;\n    const attribute_type = dracoAttribute.attribute_type();\n    const octahedron = octahedronAttributes\n      .map((type) => this.decoder[type])\n      .includes(attribute_type);\n    if (octahedron) {\n      const transform = new this.draco.AttributeQuantizationTransform();\n      try {\n        if (transform.InitFromAttribute(dracoAttribute)) {\n          return {\n            quantization_bits: transform.quantization_bits()\n          };\n        }\n      } finally {\n        this.draco.destroy(transform);\n      }\n    }\n    return null;\n  }\n\n  // HELPERS\n}\n\n/**\n * Get draco specific data type by TypedArray constructor type\n * @param attributeType\n * @returns draco specific data type\n */\nfunction getDracoDataType(draco: Draco3D, attributeType: any): draco_DataType {\n  switch (attributeType) {\n    case Float32Array:\n      return draco.DT_FLOAT32;\n    case Int8Array:\n      return draco.DT_INT8;\n    case Int16Array:\n      return draco.DT_INT16;\n    case Int32Array:\n      return draco.DT_INT32;\n    case Uint8Array:\n      return draco.DT_UINT8;\n    case Uint16Array:\n      return draco.DT_UINT16;\n    case Uint32Array:\n      return draco.DT_UINT32;\n    default:\n      return draco.DT_INVALID;\n  }\n}\n\n/**\n * Copy a Draco int32 array into a JS typed array\n */\nfunction getInt32Array(dracoArray: DracoInt32Array): Int32Array {\n  const numValues = dracoArray.size();\n  const intArray = new Int32Array(numValues);\n  for (let i = 0; i < numValues; i++) {\n    intArray[i] = dracoArray.GetValue(i);\n  }\n  return intArray;\n}\n\n/**\n * Copy a Draco int32 array into a JS typed array\n */\nfunction getUint32Array(dracoArray: DracoInt32Array): Int32Array {\n  const numValues = dracoArray.size();\n  const intArray = new Int32Array(numValues);\n  for (let i = 0; i < numValues; i++) {\n    intArray[i] = dracoArray.GetValue(i);\n  }\n  return intArray;\n}\n", "// Dynamic DRACO module loading inspired by THREE.DRACOLoader\n// https://github.com/mrdoob/three.js/blob/398c4f39ebdb8b23eefd4a7a5ec49ec0c96c7462/examples/jsm/loaders/DRACOLoader.js\n// by <PERSON> / https://www.donmccurdy.com / MIT license\n\nimport {loadLibrary} from '@loaders.gl/worker-utils';\n\nconst DRACO_DECODER_VERSION = '1.5.5';\nconst DRACO_ENCODER_VERSION = '1.4.1';\n\nconst STATIC_DECODER_URL = `https://www.gstatic.com/draco/versioned/decoders/${DRACO_DECODER_VERSION}`;\n\nconst DRACO_JS_DECODER_URL = `${STATIC_DECODER_URL}/draco_decoder.js`;\nconst DRACO_WASM_WRAPPER_URL = `${STATIC_DECODER_URL}/draco_wasm_wrapper.js`;\nconst DRACO_WASM_DECODER_URL = `${STATIC_DECODER_URL}/draco_decoder.wasm`;\n\nconst DRACO_ENCODER_URL = `https://raw.githubusercontent.com/google/draco/${DRACO_ENCODER_VERSION}/javascript/draco_encoder.js`;\n\nlet loadDecoderPromise;\nlet loadEncoderPromise;\n\nexport async function loadDracoDecoderModule(options) {\n  const modules = options.modules || {};\n\n  // Check if a bundled draco3d library has been supplied by application\n  if (modules.draco3d) {\n    loadDecoderPromise =\n      loadDecoderPromise ||\n      modules.draco3d.createDecoderModule({}).then((draco) => {\n        return {draco};\n      });\n  } else {\n    // If not, dynamically load the WASM script from our CDN\n    loadDecoderPromise = loadDecoderPromise || loadDracoDecoder(options);\n  }\n  return await loadDecoderPromise;\n}\n\nexport async function loadDracoEncoderModule(options) {\n  const modules = options.modules || {};\n\n  // Check if a bundled draco3d library has been supplied by application\n  if (modules.draco3d) {\n    loadEncoderPromise =\n      loadEncoderPromise ||\n      modules.draco3d.createEncoderModule({}).then((draco) => {\n        return {draco};\n      });\n  } else {\n    // If not, dynamically load the WASM script from our CDN\n    loadEncoderPromise = loadEncoderPromise || loadDracoEncoder(options);\n  }\n  return await loadEncoderPromise;\n}\n\n// DRACO DECODER LOADING\n\nasync function loadDracoDecoder(options) {\n  let DracoDecoderModule;\n  let wasmBinary;\n  switch (options.draco && options.draco.decoderType) {\n    case 'js':\n      DracoDecoderModule = await loadLibrary(DRACO_JS_DECODER_URL, 'draco', options);\n      break;\n\n    case 'wasm':\n    default:\n      [DracoDecoderModule, wasmBinary] = await Promise.all([\n        await loadLibrary(DRACO_WASM_WRAPPER_URL, 'draco', options),\n        await loadLibrary(DRACO_WASM_DECODER_URL, 'draco', options)\n      ]);\n  }\n  // Depends on how import happened...\n  // @ts-ignore\n  DracoDecoderModule = DracoDecoderModule || globalThis.DracoDecoderModule;\n  return await initializeDracoDecoder(DracoDecoderModule, wasmBinary);\n}\n\nfunction initializeDracoDecoder(DracoDecoderModule, wasmBinary) {\n  const options: {wasmBinary?: any} = {};\n  if (wasmBinary) {\n    options.wasmBinary = wasmBinary;\n  }\n\n  return new Promise((resolve) => {\n    DracoDecoderModule({\n      ...options,\n      onModuleLoaded: (draco) => resolve({draco}) // Module is Promise-like. Wrap in object to avoid loop.\n    });\n  });\n}\n\n// ENCODER\n\nasync function loadDracoEncoder(options) {\n  let DracoEncoderModule = await loadLibrary(DRACO_ENCODER_URL, 'draco', options);\n  // @ts-ignore\n  DracoEncoderModule = DracoEncoderModule || globalThis.DracoEncoderModule;\n\n  return new Promise((resolve) => {\n    DracoEncoderModule({\n      onModuleLoaded: (draco) => resolve({draco}) // Module is Promise-like. Wrap in object to avoid loop.\n    });\n  });\n}\n", "import type {LoaderWithParser} from '@loaders.gl/loader-utils';\nimport type {<PERSON><PERSON>Mesh, DracoLoaderData} from './lib/draco-types';\nimport type {DracoLoaderOptions} from './draco-loader';\nimport {<PERSON><PERSON><PERSON>oa<PERSON> as DracoWorkerLoader} from './draco-loader';\nimport DracoParser from './lib/draco-parser';\nimport {loadDracoDecoderModule} from './lib/draco-module-loader';\nimport {VERSION} from './lib/utils/version';\nimport {isBrowser} from '@loaders.gl/worker-utils';\n\n// Draco data types\n\nexport type {DracoMesh, DracoLoaderData};\n\n// Draco Writer\n\nexport type {DracoWriterOptions} from './draco-writer';\nexport {DracoWriter} from './draco-writer';\n\n/**\n * Browser worker doesn't work because of issue during \"draco_encoder.js\" loading.\n * Refused to execute script from 'https://raw.githubusercontent.com/google/draco/1.4.1/javascript/draco_encoder.js' because its MIME type ('') is not executable.\n */\nexport const DracoWriterWorker = {\n  id: isBrowser ? 'draco-writer' : 'draco-writer-nodejs',\n  name: 'Draco compressed geometry writer',\n  module: 'draco',\n  version: VERSION,\n  worker: true,\n  options: {\n    draco: {},\n    source: null\n  }\n};\n\n// Draco Loader\n\nexport type {DracoLoaderOptions};\nexport {DracoWorkerLoader};\n\n/**\n * Loader for Draco3D compressed geometries\n */\nexport const DracoLoader = {\n  ...DracoWorkerLoader,\n  parse\n};\n\nasync function parse(arrayBuffer: ArrayBuffer, options?: DracoLoaderOptions): Promise<DracoMesh> {\n  const {draco} = await loadDracoDecoderModule(options);\n  const dracoParser = new DracoParser(draco);\n  try {\n    return dracoParser.parseSync(arrayBuffer, options?.draco);\n  } finally {\n    dracoParser.destroy();\n  }\n}\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckDracoLoader: LoaderWithParser = DracoLoader;\n", "import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport {DracoLoader} from '../index';\n\ncreateLoaderWorker(DracoLoader);\n"], "mappings": "iJAMO,GAAM,GAA+C,SCDrD,YAAgB,EAAgB,EAAwB,CAC7D,GAAI,CAAC,EACH,KAAM,IAAI,OAAM,GAAW,gCCH/B,GAAM,GAAU,CACd,KAAM,MAAO,OAAS,aAAe,KACrC,OAAQ,MAAO,SAAW,aAAe,OACzC,OAAQ,MAAO,SAAW,aAAe,OACzC,SAAU,MAAO,WAAa,aAAe,UAGzC,GAA8B,EAAQ,MAAQ,EAAQ,QAAU,EAAQ,QAAU,GAClF,GAAgC,EAAQ,QAAU,EAAQ,MAAQ,EAAQ,QAAU,GACpF,GAAgC,EAAQ,QAAU,EAAQ,MAAQ,EAAQ,QAAU,GACpF,GAAkC,EAAQ,UAAY,GAKrD,GAAM,GAEX,MAAO,UAAY,UAAY,OAAO,WAAa,oBAAsB,QAAQ,QAGtE,EAAoB,MAAO,gBAAkB,WAG7C,GACX,MAAO,SAAW,aAAe,MAAO,QAAO,aAAgB,YAG3D,GACJ,MAAO,UAAY,aAAe,QAAQ,SAAW,YAAY,KAAK,QAAQ,SAGnE,GAAuB,IAAW,WAAW,GAAQ,KAAQ,ECxBnE,WACL,EACA,EAAqB,GACrB,EACgB,CAEhB,GAAM,GAAe,GAAa,GAAI,KAEtC,GAAK,GAEE,GAAI,GAAe,GACxB,EAAa,IAAI,WACR,GAAe,EAAO,QAE/B,EAAa,IAAI,EAAO,gBACf,aAAY,OAAO,IAGvB,GAAI,GAAa,MAAO,IAAW,SACxC,OAAW,KAAO,GAEhB,EAAgB,EAAO,GAAM,EAAW,IAM5C,MAAO,KAAc,OAAY,MAAM,KAAK,GAAgB,GAI9D,YAAwB,EAAiB,CACvC,MAAK,GAGD,YAAkB,cAGlB,MAAO,cAAgB,aAAe,YAAkB,cAGxD,MAAO,cAAgB,aAAe,YAAkB,cAIxD,MAAO,kBAAoB,aAAe,YAAkB,iBAZvD,GCxCX,YAAyB,CAEvB,GAAI,YACJ,GAAI,CAEF,KAAK,gEACL,WAAa,WAAW,gBAExB,EACF,MAAO,YAGT,GAAM,GAAsB,GAAI,KAKhC,OAAgC,OAEvB,iBAA0B,CAC/B,MAAO,OAAO,OAAS,aAAe,QAAQ,eAMrC,WAAU,EAA4E,CAC/F,WAAuB,EAAS,CAE9B,GAAM,GAAa,IACb,CAAC,OAAM,WAAW,EAAa,EAAU,EAAQ,KAIvD,EAAU,EAAM,GAGlB,GAAM,GAAa,IACnB,AAAI,EACF,GAAW,GAAG,UAAW,GAGzB,EAAW,GAAG,OAAQ,IAAM,QAAQ,MAAM,yBAG1C,WAAW,UAAY,QAIpB,kBACL,EACA,CACA,GAAI,GAAmB,EAAoB,IAAI,GAE/C,AAAK,GACH,GAAmB,AAAC,GAA+B,CACjD,GAAI,CAAC,GAAe,GAClB,OAIF,GAAM,GAAa,IACb,CAAC,OAAM,WAAW,EAAa,EAAU,EAAQ,KACvD,EAAU,EAAM,KAKpB,AADmB,IAEjB,QAAQ,MAAM,mBAEd,WAAW,iBAAiB,UAAW,SAIpC,qBACL,EACA,CACA,GAAM,GAAmB,EAAoB,IAAI,GACjD,EAAoB,OAAO,GAE3B,AADmB,IAEjB,QAAQ,MAAM,mBAEd,WAAW,oBAAoB,UAAW,SASvC,aAAY,EAAyB,EAAqC,CAC/E,GAAM,GAA0B,CAAC,OAAQ,aAAc,OAAM,WAEvD,EAAe,EAAgB,GAE/B,EAAa,IACnB,AAAI,EACF,EAAW,YAAY,EAAM,GAI7B,WAAW,YAAY,EAAM,KAMnC,YAAwB,EAA4B,CAClD,GAAM,CAAC,OAAM,QAAQ,EACrB,MACE,KAAS,WACT,GACA,MAAO,GAAK,QAAW,UACvB,EAAK,OAAO,WAAW,cCxH3B,kHAAO,GAAM,IAAwB,KACxB,GAAiB,KACjB,EAAkB,KAClB,EAAoB,KCOjC,GAAM,IAAS,SACT,GAAU,MAAO,IAAgB,YAAc,EAAc,GAE7D,EAAoD,GAiB1D,iBACE,EACA,EAA4B,KAC5B,EAAkB,GACJ,CACd,MAAI,IACF,GAAa,GAAc,EAAY,EAAY,IAKrD,EAAoB,GAElB,EAAoB,IAAe,GAAoB,GAClD,KAAM,GAAoB,GAI5B,YAAuB,EAAiB,EAAqB,EAAuB,CAEzF,GAAI,EAAQ,WAAW,QACrB,MAAO,GAIT,GAAM,GAAU,EAAQ,SAAW,GACnC,MAAI,GAAQ,GACH,EAAQ,GAKZ,EAKD,EAAQ,IACV,IAAO,EAAQ,IAAI,WAAW,SACvB,GAAG,EAAQ,OAAO,KAAc,gBAAqB,KAI1D,EACK,eAAe,IAGjB,WAAW,cAAuB,IAdhC,WAAW,eAAwB,IAiB9C,kBAAmC,EAAkC,CACnE,GAAI,EAAW,SAAS,QAEtB,MAAO,MAAM,AADI,MAAM,OAAM,IACP,cAGxB,GAAI,CAAC,EACH,GAAI,CACF,MAAO,IAAa,GAAoB,KAAM,AAAK,GAAgB,QACnE,CACA,MAAO,MAGX,GAAI,EACF,MAAO,eAAc,GAQvB,GAAM,GAAe,KAAM,AADV,MAAM,OAAM,IACO,OACpC,MAAO,IAAsB,EAAc,GAqB7C,YAA+B,EAAsB,EAAwB,CAC3E,GAAI,CAAC,EACH,MAAO,AAAK,IAAqB,AAAK,EAAkB,EAAc,GAGxE,GAAI,EAEF,YAAK,KAAK,GAAQ,GAGX,KAGT,GAAM,GAAS,SAAS,cAAc,UACtC,EAAO,GAAK,EAEZ,GAAI,CACF,EAAO,YAAY,SAAS,eAAe,SAC3C,CACA,EAAO,KAAO,EAEhB,gBAAS,KAAK,YAAY,GACnB,KC7IT,GAAI,IAAY,EAMT,WAA4B,EAA0B,CAE3D,AAAI,CAAC,EAAW,kBAIhB,GAAW,UAAY,MAAO,EAAM,IAAY,CAC9C,OAAQ,OACD,UACH,GAAI,CAGF,GAAM,CAAC,QAAO,UAAU,GAAI,UAAU,IAAM,EAEtC,EAAS,KAAM,IAAU,CAC7B,SACA,YAAa,EACb,UACA,QAAS,IACJ,EACH,MAAO,MAGX,EAAW,YAAY,OAAQ,CAAC,iBACzB,EAAP,CACA,GAAM,GAAU,YAAiB,OAAQ,EAAM,QAAU,GACzD,EAAW,YAAY,QAAS,CAAC,MAAO,IAE1C,kBAMR,YAA2B,EAA0B,EAA8C,CACjG,MAAO,IAAI,SAAQ,CAAC,EAAS,IAAW,CACtC,GAAM,GAAK,KAIL,EAAY,CAAC,EAAM,IAAY,CACnC,GAAI,EAAQ,KAAO,EAKnB,OAAQ,OACD,OACH,EAAW,oBAAoB,GAC/B,EAAQ,EAAQ,QAChB,UAEG,QACH,EAAW,oBAAoB,GAC/B,EAAO,EAAQ,OACf,iBAON,EAAW,iBAAiB,GAG5B,GAAM,GAAU,CAAC,KAAI,MAAO,EAAa,WACzC,EAAW,YAAY,UAAW,KAQtC,kBAAyB,CAAC,SAAQ,cAAa,UAAS,WAAU,CAChE,GAAI,GACA,EACJ,GAAI,EAAO,WAAa,EAAO,MAC7B,EAAO,EACP,EAAS,EAAO,WAAa,EAAO,cAC3B,EAAO,cAEhB,EAAO,AADa,GAAI,eACL,OAAO,GAC1B,EAAS,EAAO,kBAEhB,MAAM,IAAI,OAAM,4BAA4B,EAAO,eAIrD,SAAU,IACL,EACH,QAAU,GAAU,EAAO,SAAW,EAAO,QAAQ,SAAY,GACjE,OAAQ,IAGH,KAAM,GAAO,EAAM,IAAI,GAAU,EAAS,GCvG5C,GAAM,IAA+C,SCa5D,GAAM,IAA4C,CAChD,MAAO,CACL,YAAa,MAAO,cAAgB,SAAW,OAAS,KACxD,YAAa,QACb,gBAAiB,GACjB,mBAAoB,SAOX,GAAc,CACzB,KAAM,QACN,GAAI,EAAY,QAAU,eAC1B,OAAQ,QACR,OAAQ,CAAC,QACT,QAAS,GACT,OAAQ,GACR,WAAY,CAAC,OACb,UAAW,CAAC,4BACZ,OAAQ,GACR,MAAO,CAAC,SACR,QAAS,ICJJ,WAA4B,EAAyC,CAC1E,GAAI,GAAO,IACP,EAAO,IACP,EAAO,IACP,EAAO,KACP,EAAO,KACP,EAAO,KAEL,EAAY,EAAW,SAAW,EAAW,SAAS,MAAQ,GAC9D,EAAM,GAAa,EAAU,OAEnC,OAAS,GAAI,EAAG,EAAI,EAAK,GAAK,EAAG,CAC/B,GAAM,GAAI,EAAU,GACd,EAAI,EAAU,EAAI,GAClB,EAAI,EAAU,EAAI,GAExB,EAAO,EAAI,EAAO,EAAI,EACtB,EAAO,EAAI,EAAO,EAAI,EACtB,EAAO,EAAI,EAAO,EAAI,EAEtB,EAAO,EAAI,EAAO,EAAI,EACtB,EAAO,EAAI,EAAO,EAAI,EACtB,EAAO,EAAI,EAAO,EAAI,EAExB,MAAO,CACL,CAAC,EAAM,EAAM,GACb,CAAC,EAAM,EAAM,IC1DV,YAAgB,EAAoB,EAAkB,CAC3D,GAAI,CAAC,EACH,KAAM,IAAI,OAAM,GAAW,4BCI/B,WAA4B,CAK1B,YAAY,EAAiB,EAA2B,CACtD,GAAO,MAAM,QAAQ,IACrB,GAAW,GAEX,KAAK,OAAS,EACd,KAAK,SAAW,GAAY,GAAI,KAIlC,UAAU,EAAwB,CAIhC,GAHI,KAAK,WAAa,EAAM,UAGxB,KAAK,OAAO,SAAW,EAAM,OAAO,OACtC,MAAO,GAET,OAAS,GAAI,EAAG,EAAI,KAAK,OAAO,OAAQ,EAAE,EACxC,GAAI,CAAC,KAAK,OAAO,GAAG,UAAU,EAAM,OAAO,IACzC,MAAO,GAGX,MAAO,GAGT,UAAU,EAA+B,CAEvC,GAAM,GAAU,OAAO,OAAO,MAC9B,OAAW,KAAQ,GACjB,EAAQ,GAAQ,GAElB,GAAM,GAAiB,KAAK,OAAO,OAAO,AAAC,GAAU,EAAQ,EAAM,OACnE,MAAO,IAAI,GAAO,EAAgB,KAAK,UAGzC,YAAY,EAAiC,CAE3C,GAAM,GAAiB,EAAc,IAAI,AAAC,GAAU,KAAK,OAAO,IAAQ,OAAO,SAC/E,MAAO,IAAI,GAAO,EAAgB,KAAK,UAGzC,OAAO,EAA0C,CAC/C,GAAI,GACA,EAA2B,KAAK,SAEpC,GAAI,YAA0B,GAAQ,CACpC,GAAM,GAAc,EACpB,EAAS,EAAY,OACrB,EAAW,GAAU,GAAU,GAAI,KAAO,KAAK,UAAW,EAAY,cAEtE,GAAS,EAIX,GAAM,GAAmC,OAAO,OAAO,MAEvD,OAAW,KAAS,MAAK,OACvB,EAAS,EAAM,MAAQ,EAGzB,OAAW,KAAS,GAClB,EAAS,EAAM,MAAQ,EAGzB,GAAM,GAAe,OAAO,OAAO,GAEnC,MAAO,IAAI,GAAO,EAAc,KAKpC,YAAoB,EAAuB,CACzC,GAAM,GAAqC,GAC3C,OAAW,KAAS,GAClB,AAAI,EAAU,EAAM,OAElB,QAAQ,KAAK,gCAAiC,EAAM,KAAM,GAE5D,EAAU,EAAM,MAAQ,GAI5B,YAAsB,EAAO,EAAU,CAErC,MAAO,IAAI,KAAI,CAAC,GAAI,GAAM,GAAI,KAAQ,GAAI,GAAM,GAAI,OCzFtD,WAA2B,CAMzB,YACE,EACA,EACA,EAAW,GACX,EAAgC,GAAI,KACpC,CACA,KAAK,KAAO,EACZ,KAAK,KAAO,EACZ,KAAK,SAAW,EAChB,KAAK,SAAW,KAGd,SAAiB,CACnB,MAAO,MAAK,MAAQ,KAAK,KAAK,OAGhC,OAAe,CACb,MAAO,IAAI,GAAM,KAAK,KAAM,KAAK,KAAM,KAAK,SAAU,KAAK,UAG7D,UAAU,EAAsB,CAC9B,MACE,MAAK,OAAS,EAAM,MACpB,KAAK,OAAS,EAAM,MACpB,KAAK,WAAa,EAAM,UACxB,KAAK,WAAa,EAAM,SAI5B,UAAmB,CACjB,MAAO,GAAG,KAAK,OAAO,KAAK,SAAW,aAAe,KACnD,KAAK,SAAW,eAAe,KAAK,WAAa,OChBhD,GAAK,GAAL,UAAK,EAAL,CAEL,SAAO,GAAP,OAEA,SAAO,GAAP,OAEA,QAAM,GAAN,MAEA,UAAQ,GAAR,QAEA,WAAS,GAAT,SAEA,SAAO,GAAP,OAEA,SAAO,GAAP,OAEA,YAAU,GAAV,UAEA,SAAO,GAAP,OAEA,SAAO,GAAP,OAEA,cAAY,IAAZ,YAEA,aAAW,IAAX,WAEA,SAAO,IAAP,OAEA,WAAS,IAAT,SAEA,UAAQ,IAAR,QAEA,oBAAkB,IAAlB,kBAEA,kBAAgB,IAAhB,gBAEA,QAAM,IAAN,MAGA,eAAa,IAAb,aACA,SAAO,IAAP,OACA,UAAQ,IAAR,QACA,UAAQ,IAAR,QACA,UAAQ,IAAR,QACA,UAAQ,IAAR,QACA,WAAS,IAAT,SACA,WAAS,IAAT,SACA,WAAS,IAAT,SACA,YAAU,KAAV,UACA,YAAU,KAAV,UACA,YAAU,KAAV,UACA,YAAU,KAAV,UACA,oBAAkB,KAAlB,kBACA,oBAAkB,KAAlB,kBACA,yBAAuB,KAAvB,uBACA,yBAAuB,KAAvB,uBACA,wBAAsB,KAAtB,sBACA,eAAa,KAAb,aACA,oBAAkB,KAAlB,kBACA,oBAAkB,KAAlB,kBACA,mBAAiB,KAAjB,iBACA,eAAa,KAAb,aACA,gBAAc,KAAd,cACA,oBAAkB,KAAlB,kBACA,sBAAoB,KAApB,sBAhEU,WCHL,WAAe,OACb,QAAO,EAAiB,CAC7B,MAAO,IAAK,EAAE,SAAW,EAAK,WAEzB,OAAM,EAAiB,CAC5B,MAAO,IAAK,EAAE,SAAW,EAAK,UAEzB,SAAQ,EAAiB,CAC9B,MAAO,IAAK,EAAE,SAAW,EAAK,YAEzB,UAAS,EAAiB,CAC/B,MAAO,IAAK,EAAE,SAAW,EAAK,aAEzB,QAAO,EAAiB,CAC7B,MAAO,IAAK,EAAE,SAAW,EAAK,WAEzB,QAAO,EAAiB,CAC7B,MAAO,IAAK,EAAE,SAAW,EAAK,WAEzB,WAAU,EAAiB,CAChC,MAAO,IAAK,EAAE,SAAW,EAAK,cAEzB,QAAO,EAAiB,CAC7B,MAAO,IAAK,EAAE,SAAW,EAAK,WAEzB,QAAO,EAAiB,CAC7B,MAAO,IAAK,EAAE,SAAW,EAAK,WAEzB,aAAY,EAAiB,CAClC,MAAO,IAAK,EAAE,SAAW,EAAK,gBAEzB,YAAW,EAAiB,CACjC,MAAO,IAAK,EAAE,SAAW,EAAK,eAEzB,QAAO,EAAiB,CAC7B,MAAO,IAAK,EAAE,SAAW,EAAK,WAEzB,UAAS,EAAiB,CAC/B,MAAO,IAAK,EAAE,SAAW,EAAK,aAEzB,SAAQ,EAAiB,CAC9B,MAAO,IAAK,EAAE,SAAW,EAAK,YAEzB,mBAAkB,EAAiB,CACxC,MAAO,IAAK,EAAE,SAAW,EAAK,sBAEzB,iBAAgB,EAAiB,CACtC,MAAO,IAAK,EAAE,SAAW,EAAK,oBAEzB,OAAM,EAAiB,CAC5B,MAAO,IAAK,EAAE,SAAW,EAAK,UAEzB,cAAa,EAAiB,CACnC,MAAO,IAAK,EAAE,SAAW,EAAK,cAG5B,SAAe,CACjB,MAAO,GAAK,KAQd,UAAU,EAA0B,CAElC,MAAO,QAAS,IAMb,eAAmB,EAAS,IAC7B,SAAe,CACjB,MAAO,GAAK,SAET,OAAO,cAAuB,CACjC,MAAO,OAET,UAAmB,CACjB,MAAO,SAMJ,eAAmB,EAAS,IAC7B,SAAe,CACjB,MAAO,GAAK,SAKT,OAAO,cAAuB,CACjC,MAAO,OAET,UAAmB,CACjB,MAAO,SAMJ,eAAkB,EAAS,CAGhC,YAAY,EAAU,EAAU,CAC9B,QACA,KAAK,SAAW,EAChB,KAAK,SAAW,KAEd,SAAe,CACjB,MAAO,GAAK,QAgBT,OAAO,cAAuB,CACjC,MAAO,MAET,UAAmB,CACjB,MAAO,GAAG,KAAK,SAAW,IAAM,SAAS,KAAK,aAI3C,eAAmB,EAAI,CAC5B,aAAc,CACZ,MAAM,GAAM,KAGT,eAAoB,EAAI,CAC7B,aAAc,CACZ,MAAM,GAAM,MAGT,eAAoB,EAAI,CAC7B,aAAc,CACZ,MAAM,GAAM,MAQT,mBAAoB,EAAI,CAC7B,aAAc,CACZ,MAAM,GAAO,KAGV,eAAqB,EAAI,CAC9B,aAAc,CACZ,MAAM,GAAO,MAGV,eAAqB,EAAI,CAC9B,aAAc,CACZ,MAAM,GAAO,MAWjB,GAAM,IAAY,CAChB,KAAM,GACN,OAAQ,GACR,OAAQ,IAGH,eAAoB,EAAS,CAElC,YAAY,EAAW,CACrB,QACA,KAAK,UAAY,KAEf,SAAe,CACjB,MAAO,GAAK,UAcT,OAAO,cAAuB,CACjC,MAAO,QAET,UAAmB,CACjB,MAAO,QAAQ,KAAK,cASjB,mBAAsB,EAAM,CACjC,aAAc,CACZ,MAAM,GAAU,UAGb,eAAsB,EAAM,CACjC,aAAc,CACZ,MAAM,GAAU,UAIb,eAAqB,EAAS,CACnC,aAAc,CACZ,WAEE,SAAS,CACX,MAAO,GAAK,OAEd,UAAW,CACT,MAAO,aAEJ,OAAO,cAAe,CACzB,MAAO,WAMJ,eAAmB,EAAS,IAC7B,SAAe,CACjB,MAAO,GAAK,SAKT,OAAO,cAAuB,CACjC,MAAO,OAET,UAAmB,CACjB,MAAO,SAML,GAAW,CACf,IAAK,EACL,YAAa,GAGR,eAAmB,EAAS,CAEjC,YAAY,EAAM,CAChB,QACA,KAAK,KAAO,KAEV,SAAe,CACjB,MAAO,GAAK,SAKT,OAAO,cAAuB,CACjC,MAAO,OAET,UAAmB,CACjB,MAAO,OAAQ,MAAK,KAAO,GAAK,MAAM,GAAS,KAAK,WAexD,GAAM,IAAW,CACf,OAAQ,EACR,YAAa,IACb,YAAa,IACb,WAAY,KAGP,eAAmB,EAAS,CAIjC,YAAY,EAAW,EAAkB,CACvC,QACA,KAAK,KAAO,EACZ,KAAK,SAAW,KAEd,SAAe,CACjB,MAAO,GAAK,KAEd,UAAmB,CACjB,MAAO,OAAO,KAAK,YAAY,GAAS,KAAK,aAE1C,OAAO,cAAuB,CACjC,MAAO,SAoBJ,mBAAwB,EAAS,CAItC,YAAY,EAAW,EAAW,KAAM,CACtC,QACA,KAAK,KAAO,EACZ,KAAK,SAAW,KAEd,SAAe,CACjB,MAAO,GAAK,cAKT,OAAO,cAAuB,CACjC,MAAO,YAET,UAAmB,CACjB,MAAO,aAAa,GAAS,KAAK,QAAQ,KAAK,SAAW,KAAK,KAAK,WAAa,QAyBrF,GAAM,IAAe,CACnB,SAAU,EACV,WAAY,GAGP,eAAuB,EAAS,CAErC,YAAY,EAAc,CACxB,QACA,KAAK,KAAO,KAEV,SAAe,CACjB,MAAO,GAAK,aAKT,OAAO,cAAuB,CACjC,MAAO,WAET,UAAmB,CACjB,MAAO,YAAY,GAAa,KAAK,WAelC,mBAA4B,EAAS,CAI1C,YAAY,EAAkB,EAAc,CAC1C,QACA,KAAK,SAAW,EAChB,KAAK,SAAW,CAAC,MAEf,SAAe,CACjB,MAAO,GAAK,iBAEV,YAAY,CACd,MAAO,MAAK,SAAS,GAAG,QAEtB,aAAa,CACf,MAAO,MAAK,SAAS,OAKlB,OAAO,cAAuB,CACjC,MAAO,gBAET,UAAmB,CACjB,MAAO,iBAAiB,KAAK,aAAa,KAAK,eAI5C,gBAAqB,EAAS,CAGnC,YAAY,EAAmB,CAC7B,QACA,KAAK,SAAW,KAGP,SAAS,CAClB,MAAO,GAAK,OAEP,UAAW,CAChB,MAAO,WAAW,KAAK,SAAS,IAAI,AAAC,GAAM,GAAG,EAAE,QAAQ,EAAE,QAAQ,KAAK,cAEpE,OAAO,cAAuB,CACjC,MAAO,WCjeJ,YAAoC,EAA6B,CACtE,OAAQ,EAAM,iBACP,WACH,MAAO,IAAI,OACR,YACH,MAAO,IAAI,OACR,YACH,MAAO,IAAI,OACR,aACH,MAAO,IAAI,OACR,YACH,MAAO,IAAI,OACR,aACH,MAAO,IAAI,OACR,cACH,MAAO,IAAI,OACR,cACH,MAAO,IAAI,WAEX,KAAM,IAAI,OAAM,6BCPf,YACL,EACA,EACA,EACO,CACP,GAAM,GAAO,GAA2B,EAAU,OAC5C,EAAW,GAAsC,GAA0B,GAOjF,MANc,IAAI,GAChB,EACA,GAAI,GAAc,EAAU,KAAM,GAAI,GAAM,QAAS,IACrD,GACA,GAwBG,YAAmC,EAA+C,CACvF,GAAM,GAAS,GAAI,KACnB,MAAI,cAAgB,IAClB,EAAO,IAAI,aAAc,EAAU,WAAY,SAAS,KAEtD,cAAgB,IAClB,EAAO,IAAI,aAAc,EAAU,WAAY,SAAS,KAEtD,cAAgB,IAClB,EAAO,IAAI,aAAc,EAAU,WAAY,YAE1C,EClEF,YACL,EACA,EACA,EACQ,CACR,GAAM,GAAc,GAAa,EAAW,UACtC,EAAkB,GAClB,EAA4B,GAA8B,EAAW,YAC3E,OAAW,KAAiB,GAAY,CACtC,GAAM,GAAY,EAAW,GACvB,EAAQ,GACZ,EACA,EACA,EAA0B,IAE5B,EAAO,KAAK,GAEd,GAAI,EAAS,CACX,GAAM,GAAe,GAA2B,UAAW,GAC3D,EAAO,KAAK,GAEd,MAAO,IAAI,GAAO,EAAQ,GAG5B,YAAuC,EAErC,CACA,GAAM,GAAoD,GAC1D,OAAW,KAAO,GAAY,CAC5B,GAAM,GAAiB,EAAW,GAClC,EAAO,EAAe,MAAQ,aAAe,EAE/C,MAAO,GAGT,YACE,EACA,EACA,EACO,CACP,GAAM,GAAc,EAAa,GAAa,EAAW,UAAY,OAErE,MADc,IAAgB,EAAe,EAAW,GAI1D,YAAsB,EAAoE,CACxF,GAAM,GAAc,GAAI,KACxB,OAAW,KAAO,GAChB,EAAY,IAAI,GAAG,WAAc,KAAK,UAAU,EAAS,KAE3D,MAAO,GCFT,GAAM,IAAmC,CACvC,SAAU,WACV,OAAQ,SACR,MAAO,UACP,UAAW,cAGP,GAAqC,CACzC,EAAG,UACH,EAAG,WACH,EAAG,WACH,EAAG,YACH,EAAG,WACH,EAAG,YACH,EAAG,cAGC,GAAkB,EAExB,OAAiC,CAM/B,YAAY,EAAgB,CAC1B,KAAK,MAAQ,EACb,KAAK,QAAU,GAAI,MAAK,MAAM,QAC9B,KAAK,gBAAkB,GAAI,MAAK,MAAM,gBAMxC,SAAgB,CACd,KAAK,MAAM,QAAQ,KAAK,SACxB,KAAK,MAAM,QAAQ,KAAK,iBAQ1B,UAAU,EAA0B,EAA6B,GAAe,CAC9E,GAAM,GAAS,GAAI,MAAK,MAAM,cAC9B,EAAO,KAAK,GAAI,WAAU,GAAc,EAAY,YAEpD,KAAK,4BAA4B,GAEjC,GAAM,GAAgB,KAAK,QAAQ,uBAAuB,GACpD,EACJ,IAAkB,KAAK,MAAM,gBACzB,GAAI,MAAK,MAAM,KACf,GAAI,MAAK,MAAM,WAErB,GAAI,CACF,GAAI,GACJ,OAAQ,OACD,MAAK,MAAM,gBACd,EAAc,KAAK,QAAQ,mBAAmB,EAAQ,GACtD,UAEG,MAAK,MAAM,YACd,EAAc,KAAK,QAAQ,yBAAyB,EAAQ,GAC5D,cAGA,KAAM,IAAI,OAAM,iCAGpB,GAAI,CAAC,EAAY,MAAQ,CAAC,EAAc,IAAK,CAC3C,GAAM,GAAU,+BAA+B,EAAY,cAE3D,KAAM,IAAI,OAAM,GAGlB,GAAM,GAAa,KAAK,oBAAoB,EAAe,EAAe,GAEpE,EAAW,KAAK,aAAa,EAAe,EAAY,GAExD,EAAc,EAAmB,EAAS,YAE1C,EAAS,GAAe,EAAS,WAAY,EAAY,EAAS,SAYxE,MAVwB,CACtB,OAAQ,QACR,aACA,OAAQ,CACN,YAAa,EAAc,aAC3B,kBAEC,EACH,iBAGF,CACA,KAAK,MAAM,QAAQ,GACf,GACF,KAAK,MAAM,QAAQ,IAczB,oBACE,EACA,EACA,EACiB,CACjB,GAAM,GAAW,KAAK,qBAAqB,GACrC,EAAa,KAAK,oBAAoB,EAAe,GAE3D,MAAO,CACL,gBACA,eAAgB,EAAc,iBAC9B,WAAY,EAAc,aAC1B,UAAW,YAAyB,MAAK,MAAM,KAAO,EAAc,YAAc,EAClF,WACA,cAUJ,oBACE,EACA,EACuC,CACvC,GAAM,GAAyD,GAE/D,OAAS,GAAc,EAAG,EAAc,EAAc,iBAAkB,IAAe,CAGrF,GAAM,GAAiB,KAAK,QAAQ,aAAa,EAAe,GAE1D,EAAW,KAAK,sBAAsB,EAAe,GAE3D,EAAgB,EAAe,aAAe,CAC5C,UAAW,EAAe,YAC1B,eAAgB,EAAe,iBAC/B,UAAW,EAAe,YAC1B,eAAgB,EAAe,iBAE/B,YAAa,EAAe,cAC5B,YAAa,EAAe,cAC5B,WAAY,EAAe,aAC3B,gBAAiB,EAEjB,YAIF,GAAM,GAAe,KAAK,0BAA0B,EAAgB,GACpE,AAAI,GACF,GAAgB,EAAe,aAAa,uBAAyB,GAGvE,GAAM,GAAa,KAAK,wBAAwB,EAAgB,GAChE,AAAI,GACF,GAAgB,EAAe,aAAa,qBAAuB,GAIvE,MAAO,GAST,aACE,EACA,EACA,EACc,CACd,GAAM,GAAa,KAAK,mBAAmB,EAAY,EAAe,GAGtE,GAAI,CADsB,EAAW,SAEnC,KAAM,IAAI,OAAM,uCAIlB,GAAI,YAAyB,MAAK,MAAM,KACtC,OAAQ,EAAQ,cACT,iBACH,MAAO,CACL,SAAU,iBACV,KAAM,EACN,aACA,QAAS,CACP,MAAO,KAAK,yBAAyB,GACrC,KAAM,QAGP,wBAEH,MAAO,CACL,SAAU,gBACV,KAAM,EACN,aACA,QAAS,CACP,MAAO,KAAK,wBAAwB,GACpC,KAAM,IAOhB,MAAO,CACL,SAAU,aACV,KAAM,EACN,cAIJ,mBACE,EACA,EACA,EAC0C,CAC1C,GAAM,GAA6C,GAEnD,OAAW,KAAmB,QAAO,OAAO,EAAW,YAAa,CAClE,GAAM,GAAgB,KAAK,qBAAqB,EAAiB,GACjE,EAAgB,KAAO,EACvB,GAAM,CAAC,QAAO,QAAQ,KAAK,oBAAoB,EAAe,GAC9D,EAAW,GAAiB,CAC1B,QACA,OACA,WAAY,EAAgB,YAC5B,WAAY,EAAgB,YAC5B,WAAY,EAAgB,YAIhC,MAAO,GAST,wBAAwB,EAAqB,CAG3C,GAAM,GAAa,AADF,EAAc,YACD,EACxB,EAAa,EAAa,GAE1B,EAAM,KAAK,MAAM,QAAQ,GAC/B,GAAI,CACF,YAAK,QAAQ,wBAAwB,EAAe,EAAY,GACzD,GAAI,aAAY,KAAK,MAAM,QAAQ,OAAQ,EAAK,GAAY,eACnE,CACA,KAAK,MAAM,MAAM,IAQrB,yBAAyB,EAAqB,CAC5C,GAAM,GAAa,GAAI,MAAK,MAAM,gBAClC,GAAI,CACsB,YAAK,QAAQ,0BAA0B,EAAe,GACvE,GAAe,UACtB,CACA,KAAK,MAAM,QAAQ,IAUvB,oBACE,EACA,EACmC,CACnC,GAAM,GAAiB,GAAmC,EAAU,WAC9D,EAAgB,EAAU,eAE1B,EAAY,AADA,EAAc,aACF,EAExB,EAAa,EAAY,EAAe,kBACxC,EAAW,GAAiB,KAAK,MAAO,GAE1C,EAEE,EAAM,KAAK,MAAM,QAAQ,GAC/B,GAAI,CACF,GAAM,GAAiB,KAAK,QAAQ,aAAa,EAAe,EAAU,iBAC1E,KAAK,QAAQ,kCACX,EACA,EACA,EACA,EACA,GAEF,EAAQ,GAAI,GAAe,KAAK,MAAM,QAAQ,OAAQ,EAAK,GAAW,eACtE,CACA,KAAK,MAAM,MAAM,GAGnB,MAAO,CAAC,QAAO,KAAM,GA6BvB,qBAAqB,EAA2B,EAAoC,CAElF,GAAM,GAAW,EAAU,UAC3B,OAAW,CAAC,EAAe,IAAsB,QAAO,QACtD,EAAQ,iBAAmB,IAE3B,GAAI,IAAsB,EACxB,MAAO,GAKX,GAAM,GAAoB,EAAU,eACpC,OAAW,KAA0B,IAEnC,GAAI,AADkB,KAAK,MAAM,KACX,EAGpB,MAAO,IAAiC,GAM5C,GAAM,GAAY,EAAQ,oBAAsB,OAChD,MAAI,GAAU,SAAS,GACd,EAAU,SAAS,GAAW,OAIhC,oBAAoB,IAM7B,qBAAqB,EAAkC,CACrD,GAAM,GAAgB,KAAK,QAAQ,YAAY,GAC/C,MAAO,MAAK,kBAAkB,GAIhC,sBAAsB,EAAkC,EAAqB,CAC3E,GAAM,GAAgB,KAAK,QAAQ,qBAAqB,EAAe,GACvE,MAAO,MAAK,kBAAkB,GAQhC,kBAAkB,EAAgE,CAEhF,GAAI,CAAC,GAAiB,CAAC,EAAc,IACnC,MAAO,GAET,GAAM,GAAS,GACT,EAAa,KAAK,gBAAgB,WAAW,GACnD,OAAS,GAAa,EAAG,EAAa,EAAY,IAAc,CAC9D,GAAM,GAAY,KAAK,gBAAgB,aAAa,EAAe,GACnE,EAAO,GAAa,KAAK,uBAAuB,EAAe,GAEjE,MAAO,GAQT,uBAAuB,EAAyB,EAAuC,CACrF,GAAM,GAAa,GAAI,MAAK,MAAM,gBAClC,GAAI,CAEF,KAAK,gBAAgB,iBAAiB,EAAe,EAAW,GAChE,GAAM,GAAW,GAAc,GAC/B,MAAO,CACL,IAAK,KAAK,gBAAgB,YAAY,EAAe,GACrD,OAAQ,KAAK,gBAAgB,eAAe,EAAe,GAC3D,OAAQ,KAAK,gBAAgB,eAAe,EAAe,GAC3D,mBAEF,CACA,KAAK,MAAM,QAAQ,IAOvB,4BAA4B,EAA4B,CACtD,GAAM,CAAC,sBAAsB,GAAI,uBAAuB,IAAM,EACxD,EAAiB,CAAC,GAAG,EAAqB,GAAG,GACnD,OAAW,KAAsB,GAC/B,KAAK,QAAQ,uBAAuB,KAAK,MAAM,IAQnD,0BACE,EACA,EACmC,CACnC,GAAM,CAAC,sBAAsB,IAAM,EAC7B,EAAiB,EAAe,iBAEtC,GADa,EAAoB,IAAI,AAAC,GAAS,KAAK,QAAQ,IAAO,SAAS,GAClE,CACR,GAAM,GAAY,GAAI,MAAK,MAAM,+BACjC,GAAI,CACF,GAAI,EAAU,kBAAkB,GAC9B,MAAO,CACL,kBAAmB,EAAU,oBAC7B,MAAO,EAAU,QACjB,WAAY,GAAI,cAAa,CAAC,EAAG,EAAG,IAAI,IAAI,AAAC,GAAM,EAAU,UAAU,YAG3E,CACA,KAAK,MAAM,QAAQ,IAGvB,MAAO,MAGT,wBACE,EACA,EACiC,CACjC,GAAM,CAAC,uBAAuB,IAAM,EAC9B,EAAiB,EAAe,iBAItC,GAHmB,EAChB,IAAI,AAAC,GAAS,KAAK,QAAQ,IAC3B,SAAS,GACI,CACd,GAAM,GAAY,GAAI,MAAK,MAAM,+BACjC,GAAI,CACF,GAAI,EAAU,kBAAkB,GAC9B,MAAO,CACL,kBAAmB,EAAU,4BAGjC,CACA,KAAK,MAAM,QAAQ,IAGvB,MAAO,QAWX,YAA0B,EAAgB,EAAoC,CAC5E,OAAQ,OACD,cACH,MAAO,GAAM,eACV,WACH,MAAO,GAAM,YACV,YACH,MAAO,GAAM,aACV,YACH,MAAO,GAAM,aACV,YACH,MAAO,GAAM,aACV,aACH,MAAO,GAAM,cACV,aACH,MAAO,GAAM,kBAEb,MAAO,GAAM,YAOnB,YAAuB,EAAyC,CAC9D,GAAM,GAAY,EAAW,OACvB,EAAW,GAAI,YAAW,GAChC,OAAS,GAAI,EAAG,EAAI,EAAW,IAC7B,EAAS,GAAK,EAAW,SAAS,GAEpC,MAAO,GAMT,YAAwB,EAAyC,CAC/D,GAAM,GAAY,EAAW,OACvB,EAAW,GAAI,YAAW,GAChC,OAAS,GAAI,EAAG,EAAI,EAAW,IAC7B,EAAS,GAAK,EAAW,SAAS,GAEpC,MAAO,GC1lBT,GAAM,IAAwB,QACxB,GAAwB,QAExB,GAAqB,oDAAoD,KAEzE,GAAuB,GAAG,sBAC1B,GAAyB,GAAG,2BAC5B,GAAyB,GAAG,wBAE5B,GAAoB,kDAAkD,iCAExE,EAGJ,kBAA6C,EAAS,CACpD,GAAM,GAAU,EAAQ,SAAW,GAGnC,MAAI,GAAQ,QACV,EACE,GACA,EAAQ,QAAQ,oBAAoB,IAAI,KAAK,AAAC,GACrC,EAAC,WAIZ,EAAqB,GAAsB,GAAiB,GAEvD,KAAM,GAsBf,kBAAgC,EAAS,CACvC,GAAI,GACA,EACJ,OAAQ,EAAQ,OAAS,EAAQ,MAAM,iBAChC,KACH,EAAqB,KAAM,GAAY,GAAsB,QAAS,GACtE,UAEG,eAEH,CAAC,EAAoB,GAAc,KAAM,SAAQ,IAAI,CACnD,KAAM,GAAY,GAAwB,QAAS,GACnD,KAAM,GAAY,GAAwB,QAAS,KAKzD,SAAqB,GAAsB,WAAW,mBAC/C,KAAM,IAAuB,EAAoB,GAG1D,YAAgC,EAAoB,EAAY,CAC9D,GAAM,GAA8B,GACpC,MAAI,IACF,GAAQ,WAAa,GAGhB,GAAI,SAAQ,AAAC,GAAY,CAC9B,EAAmB,IACd,EACH,eAAgB,AAAC,GAAU,EAAQ,CAAC,cC5CnC,GAAM,IAAc,IACtB,GACH,UAGF,kBAAqB,EAA0B,EAAkD,CAC/F,GAAM,CAAC,SAAS,KAAM,IAAuB,GACvC,EAAc,GAAI,GAAY,GACpC,GAAI,CACF,MAAO,GAAY,UAAU,EAAa,GAAS,cACnD,CACA,EAAY,WClDhB,EAAmB", "names": []}